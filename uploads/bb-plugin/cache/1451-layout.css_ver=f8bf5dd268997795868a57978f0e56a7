.fl-builder-content *,.fl-builder-content *:before,.fl-builder-content *:after {-webkit-box-sizing: border-box;-moz-box-sizing: border-box;box-sizing: border-box;}.fl-row:before,.fl-row:after,.fl-row-content:before,.fl-row-content:after,.fl-col-group:before,.fl-col-group:after,.fl-col:before,.fl-col:after,.fl-module:before,.fl-module:after,.fl-module-content:before,.fl-module-content:after {display: table;content: " ";}.fl-row:after,.fl-row-content:after,.fl-col-group:after,.fl-col:after,.fl-module:after,.fl-module-content:after {clear: both;}.fl-row,.fl-row-content,.fl-col-group,.fl-col,.fl-module,.fl-module-content {zoom:1;}.fl-clear {clear: both;}.fl-clearfix:before,.fl-clearfix:after {display: table;content: " ";}.fl-clearfix:after {clear: both;}.fl-clearfix {zoom:1;}.fl-visible-medium,.fl-visible-medium-mobile,.fl-visible-mobile,.fl-col-group .fl-visible-medium.fl-col,.fl-col-group .fl-visible-medium-mobile.fl-col,.fl-col-group .fl-visible-mobile.fl-col {display: none;}.fl-row,.fl-row-content {margin-left: auto;margin-right: auto;}.fl-row-content-wrap {position: relative;}.fl-builder-mobile .fl-row-bg-photo .fl-row-content-wrap {background-attachment: scroll;}.fl-row-bg-video,.fl-row-bg-video .fl-row-content {position: relative;}.fl-row-bg-video .fl-bg-video {bottom: 0;left: 0;overflow: hidden;position: absolute;right: 0;top: 0;}.fl-row-bg-video .fl-bg-video video {bottom: 0;left: 0px;position: absolute;right: 0;top: 0px;}.fl-row-bg-video .fl-bg-video iframe {pointer-events: none;width: 100vw;height: 56.25vw; min-height: 100vh;min-width: 177.77vh; position: absolute;top: 50%;left: 50%;transform: translate(-50%, -50%);}.fl-bg-video-fallback {background-position: 50% 50%;background-repeat: no-repeat;background-size: cover;bottom: 0px;left: 0px;position: absolute;right: 0px;top: 0px;}.fl-row-bg-slideshow,.fl-row-bg-slideshow .fl-row-content {position: relative;}.fl-row .fl-bg-slideshow {bottom: 0;left: 0;overflow: hidden;position: absolute;right: 0;top: 0;z-index: 0;}.fl-builder-edit .fl-row .fl-bg-slideshow * {bottom: 0;height: auto !important;left: 0;position: absolute !important;right: 0;top: 0;}.fl-row-bg-overlay .fl-row-content-wrap:after {content: '';display: block;position: absolute;top: 0;right: 0;bottom: 0;left: 0;z-index: 0;}.fl-row-bg-overlay .fl-row-content {position: relative;z-index: 1;}.fl-row-full-height .fl-row-content-wrap {display: -webkit-box;display: -webkit-flex;display: -ms-flexbox;display: flex;min-height: 100vh;}.fl-row-full-height .fl-row-content {-webkit-box-flex: 1 1 auto; -moz-box-flex: 1 1 auto;-webkit-flex: 1 1 auto;-ms-flex: 1 1 auto;flex: 1 1 auto;}.fl-row-full-height .fl-row-full-width.fl-row-content {max-width: 100%;width: 100%;}.fl-builder-ie-11 .fl-row-full-height .fl-row-content-wrap {height: 1px;}.fl-builder-ie-11 .fl-row-full-height .fl-row-content {flex: 0 0 auto;flex-basis: 100%;margin: 0;}.fl-row-full-height.fl-row-align-center .fl-row-content-wrap {align-items: center;justify-content: center;-webkit-align-items: center;-webkit-box-align: center;-webkit-box-pack: center;-webkit-justify-content: center;-ms-flex-align: center;-ms-flex-pack: center;}@media all and (device-width: 768px) and (device-height: 1024px) and (orientation:portrait){.fl-row-full-height .fl-row-content-wrap{min-height: 1024px;}}@media all and (device-width: 1024px) and (device-height: 768px) and (orientation:landscape){.fl-row-full-height .fl-row-content-wrap{min-height: 768px;}}@media screen and (device-aspect-ratio: 40/71) {.fl-row-full-height .fl-row-content-wrap {min-height: 500px;}}.fl-col-group-equal-height,.fl-col-group-equal-height .fl-col,.fl-col-group-equal-height .fl-col-content{display: -webkit-box;display: -webkit-flex;display: -ms-flexbox;display: flex;}.fl-col-group-equal-height{-webkit-flex-wrap: wrap;-ms-flex-wrap: wrap;flex-wrap: wrap;}.fl-col-group-equal-height .fl-col,.fl-col-group-equal-height .fl-col-content{-webkit-box-flex: 1 1 auto; -moz-box-flex: 1 1 auto;-webkit-flex: 1 1 auto;-ms-flex: 1 1 auto;flex: 1 1 auto;}.fl-col-group-equal-height .fl-col-content{-webkit-box-orient: vertical; -webkit-box-direction: normal;-webkit-flex-direction: column;-ms-flex-direction: column;flex-direction: column; flex-shrink: 1; min-width: 1px; max-width: 100%;width: 100%;}.fl-col-group-equal-height:before,.fl-col-group-equal-height .fl-col:before,.fl-col-group-equal-height .fl-col-content:before,.fl-col-group-equal-height:after,.fl-col-group-equal-height .fl-col:after,.fl-col-group-equal-height .fl-col-content:after{content: none;}.fl-col-group-equal-height.fl-col-group-align-center .fl-col-content {align-items: center;justify-content: center;-webkit-align-items: center;-webkit-box-align: center;-webkit-box-pack: center;-webkit-justify-content: center;-ms-flex-align: center;-ms-flex-pack: center;}.fl-col-group-equal-height.fl-col-group-align-bottom .fl-col-content {justify-content: flex-end;-webkit-justify-content: flex-end;-webkit-box-align: end;-webkit-box-pack: end;-ms-flex-pack: end;}.fl-col-group-equal-height.fl-col-group-align-center .fl-module,.fl-col-group-equal-height.fl-col-group-align-center .fl-col-group {width: 100%;}.fl-builder-ie-11 .fl-col-group-equal-height .fl-module,.fl-col-group-equal-height.fl-col-group-align-center .fl-col-group{min-height: 1px;}.fl-col {float: left;min-height: 1px;}.fl-col-bg-overlay .fl-col-content {position: relative;}.fl-col-bg-overlay .fl-col-content:after {content: '';display: block;position: absolute;top: 0;right: 0;bottom: 0;left: 0;z-index: 0;}.fl-col-bg-overlay .fl-module {position: relative;z-index: 2;}.fl-module img {max-width: 100%;} .fl-builder-module-template {margin: 0 auto;max-width: 1100px;padding: 20px;}.fl-builder-content a.fl-button,.fl-builder-content a.fl-button:visited {border-radius: 4px;-moz-border-radius: 4px;-webkit-border-radius: 4px;display: inline-block;font-size: 16px;font-weight: normal;line-height: 18px;padding: 12px 24px;text-decoration: none;text-shadow: none;}.fl-builder-content .fl-button:hover {text-decoration: none;}.fl-builder-content .fl-button:active {position: relative;top: 1px;}.fl-builder-content .fl-button-width-full .fl-button {display: block;text-align: center;}.fl-builder-content .fl-button-width-custom .fl-button {display: inline-block;text-align: center;max-width: 100%;}.fl-builder-content .fl-button-left {text-align: left;}.fl-builder-content .fl-button-center {text-align: center;}.fl-builder-content .fl-button-right {text-align: right;}.fl-builder-content .fl-button i {font-size: 1.3em;height: auto;margin-right:8px;vertical-align: middle;width: auto;}.fl-builder-content .fl-button i.fl-button-icon-after {margin-left: 8px;margin-right: 0;}.fl-builder-content .fl-button-has-icon .fl-button-text {vertical-align: middle;}.fl-icon-wrap {display: inline-block;}.fl-icon {display: table-cell;vertical-align: middle;}.fl-icon a {text-decoration: none;}.fl-icon i {float: left;}.fl-icon i:before {border: none !important;}.fl-icon-text {display: table-cell;text-align: left;padding-left: 15px;vertical-align: middle;}.fl-icon-text *:last-child {margin: 0 !important;padding: 0 !important;}.fl-icon-text a {text-decoration: none;}.fl-photo {line-height: 0;position: relative;}.fl-photo-align-left {text-align: left;}.fl-photo-align-center {text-align: center;}.fl-photo-align-right {text-align: right;}.fl-photo-content {display: inline-block;line-height: 0;position: relative;max-width: 100%;}.fl-photo-img-svg {width: 100%;}.fl-photo-content img {display: inline;height: auto !important;max-width: 100%;width: auto !important;}.fl-photo-crop-circle img {-webkit-border-radius: 100%;-moz-border-radius: 100%;border-radius: 100%;}.fl-photo-caption {font-size: 13px;line-height: 18px;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;}.fl-photo-caption-below {padding-bottom: 20px;padding-top: 10px;}.fl-photo-caption-hover {background: rgba(0,0,0,0.7);bottom: 0;color: #fff;left: 0;opacity: 0;filter: alpha(opacity = 0);padding: 10px 15px;position: absolute;right: 0;-webkit-transition:opacity 0.3s ease-in;-moz-transition:opacity 0.3s ease-in;transition:opacity 0.3s ease-in;}.fl-photo-content:hover .fl-photo-caption-hover {opacity: 100;filter: alpha(opacity = 100);}.fl-builder-pagination {padding: 40px 0;}.fl-builder-pagination ul.page-numbers {list-style: none;margin: 0;padding: 0;text-align: center;}.fl-builder-pagination li {display: inline-block;list-style: none;margin: 0;padding: 0;}.fl-builder-pagination li a.page-numbers,.fl-builder-pagination li span.page-numbers {border: 1px solid #e6e6e6;display: inline-block;padding: 5px 10px;margin: 0 0 5px;}.fl-builder-pagination li a.page-numbers:hover,.fl-builder-pagination li span.current {background: #f5f5f5;text-decoration: none;}.fl-slideshow,.fl-slideshow * {-webkit-box-sizing: content-box;-moz-box-sizing: content-box;box-sizing: content-box;}.fl-slideshow .fl-slideshow-image img {max-width: none !important;}.fl-slideshow-social {line-height: 0 !important;}.fl-slideshow-social * {margin: 0 !important;}.fl-builder-content .bx-wrapper .bx-viewport {background: transparent;border: none;box-shadow: none;-moz-box-shadow: none;-webkit-box-shadow: none;left: 0;}.mfp-wrap button.mfp-arrow,.mfp-wrap button.mfp-arrow:active, .mfp-wrap button.mfp-arrow:hover, .mfp-wrap button.mfp-arrow:focus {background: transparent !important;border: none !important;outline: none;position: absolute;top: 50%;box-shadow: none !important;-moz-box-shadow: none !important;-webkit-box-shadow: none !important;}.mfp-wrap .mfp-close,.mfp-wrap .mfp-close:active,.mfp-wrap .mfp-close:hover,.mfp-wrap .mfp-close:focus {background: transparent !important;border: none !important;outline: none;position: absolute;top: 0;box-shadow: none !important;-moz-box-shadow: none !important;-webkit-box-shadow: none !important;}.admin-bar .mfp-wrap .mfp-close,.admin-bar .mfp-wrap .mfp-close:active,.admin-bar .mfp-wrap .mfp-close:hover,.admin-bar .mfp-wrap .mfp-close:focus {top: 32px!important;}img.mfp-img {padding: 0;}.mfp-counter {display: none;}.mfp-wrap .mfp-preloader.fa {font-size: 30px;}.fl-form-field {margin-bottom: 15px;}.fl-form-field input.fl-form-error {border-color: #DD6420;}.fl-form-error-message {clear: both;color: #DD6420;display: none;padding-top: 8px;font-size: 12px;font-weight: lighter;}.fl-form-button-disabled {opacity: 0.5;}.fl-animation {opacity: 0;}.fl-builder-mobile .fl-animation,.fl-builder-edit .fl-animation,.fl-animated {opacity: 1;}.fl-animated.fl-fade-in {animation: fl-fade-in 1s ease-out;-webkit-animation: fl-fade-in 1s ease-out;-moz-animation: fl-fade-in 1s ease-out;}@keyframes fl-fade-in {0% { opacity: 0; }100% { opacity: 1; }}@-webkit-keyframes fl-fade-in {0% { opacity: 0; }100% { opacity: 1; }}@-moz-keyframes fl-fade-in {0% { opacity: 0; }100% { opacity: 1; }}.fl-animated.fl-slide-left {animation: fl-slide-left 1s ease-out;-webkit-animation: fl-slide-left 1s ease-out;-moz-animation: fl-slide-left 1s ease-out;}@keyframes fl-slide-left {0% { opacity: 0; transform: translateX(10%); }100% { opacity: 1; transform: translateX(0%); }}@-webkit-keyframes fl-slide-left {0% { opacity: 0; -webkit-transform: translateX(10%); }100% { opacity: 1; -webkit-transform: translateX(0%); }}@-moz-keyframes fl-slide-left {0% { opacity: 0; -moz-transform: translateX(10%); } 100% { opacity: 1; -moz-transform: translateX(0%); }}.fl-animated.fl-slide-right {animation: fl-slide-right 1s ease-out;-webkit-animation: fl-slide-right 1s ease-out;-moz-animation: fl-slide-right 1s ease-out;}@keyframes fl-slide-right {0% { opacity: 0; transform: translateX(-10%); } 100% { opacity: 1; transform: translateX(0%); }}@-webkit-keyframes fl-slide-right {0% { opacity: 0; -webkit-transform: translateX(-10%); } 100% { opacity: 1; -webkit-transform: translateX(0%); }}@-moz-keyframes fl-slide-right {0% { opacity: 0; -moz-transform: translateX(-10%); }100% { opacity: 1; -moz-transform: translateX(0%); }}.fl-animated.fl-slide-up {animation: fl-slide-up 1s ease-out;-webkit-animation: fl-slide-up 1s ease-out;-moz-animation: fl-slide-up 1s ease-out;}@keyframes fl-slide-up {0% { opacity: 0; transform: translateY(10%); }100% { opacity: 1; transform: translateY(0%); }}@-webkit-keyframes fl-slide-up {0% { opacity: 0; -webkit-transform: translateY(10%); }100% { opacity: 1; -webkit-transform: translateY(0%); }}@-moz-keyframes fl-slide-up {0% { opacity: 0; -moz-transform: translateY(10%); } 100% { opacity: 1; -moz-transform: translateY(0%); }}.fl-animated.fl-slide-down {animation: fl-slide-down 1s ease-out;-webkit-animation: fl-slide-down 1s ease-out;-moz-animation: fl-slide-down 1s ease-out;}@keyframes fl-slide-down {0% { opacity: 0; transform: translateY(-10%); } 100% { opacity: 1; transform: translateY(0%); }}@-webkit-keyframes fl-slide-down {0% { opacity: 0; -webkit-transform: translateY(-10%); } 100% { opacity: 1; -webkit-transform: translateY(0%); }}@-moz-keyframes fl-slide-down {0% { opacity: 0; -moz-transform: translateY(-10%); }100% { opacity: 1; -moz-transform: translateY(0%); }}.fl-button.fl-button-icon-animation i {width: 0 !important;opacity: 0;-ms-filter: "alpha(opacity=0)";transition: all 0.2s ease-out;-webkit-transition: all 0.2s ease-out;}.fl-button.fl-button-icon-animation:hover i {opacity: 1! important;-ms-filter: "alpha(opacity=100)";}.fl-button.fl-button-icon-animation i.fl-button-icon-after {margin-left: 0px !important;}.fl-button.fl-button-icon-animation:hover i.fl-button-icon-after {margin-left: 10px !important;}.fl-button.fl-button-icon-animation i.fl-button-icon-before {margin-right: 0 !important;}.fl-button.fl-button-icon-animation:hover i.fl-button-icon-before {margin-right: 20px !important;margin-left: -10px;}.fl-builder-content a.fl-button,.fl-builder-content a.fl-button:visited {background: #fafafa;border: 1px solid #ccc;color: #333;}.fl-builder-content a.fl-button *,.fl-builder-content a.fl-button:visited * {color: #333;}.fl-row-content-wrap { margin: 0px; }.fl-row-content-wrap { padding: 20px; }.fl-row-fixed-width { max-width: 1100px; }.fl-module-content { margin: 10px; }@media (max-width: 992px) { .fl-visible-desktop,.fl-visible-mobile,.fl-col-group .fl-visible-desktop.fl-col,.fl-col-group .fl-visible-mobile.fl-col {display: none;}.fl-visible-desktop-medium,.fl-visible-medium,.fl-visible-medium-mobile,.fl-col-group .fl-visible-desktop-medium.fl-col,.fl-col-group .fl-visible-medium.fl-col,.fl-col-group .fl-visible-medium-mobile.fl-col {display: block;} }@media (max-width: 768px) { .fl-visible-desktop,.fl-visible-desktop-medium,.fl-visible-medium,.fl-col-group .fl-visible-desktop.fl-col,.fl-col-group .fl-visible-desktop-medium.fl-col,.fl-col-group .fl-visible-medium.fl-col {display: none;}.fl-visible-medium-mobile,.fl-visible-mobile,.fl-col-group .fl-visible-medium-mobile.fl-col,.fl-col-group .fl-visible-mobile.fl-col {display: block;}.fl-row-content-wrap {background-attachment: scroll !important;}.fl-row-bg-parallax .fl-row-content-wrap {background-attachment: scroll !important;background-position: center center !important;}.fl-col-group.fl-col-group-equal-height {display: block;}.fl-col-group.fl-col-group-equal-height.fl-col-group-custom-width {display: -webkit-box;display: -webkit-flex;display: -ms-flexbox;display: flex;}.fl-col-group.fl-col-group-responsive-reversed {display: -webkit-box;display: -moz-box;display: -ms-flexbox;display: -moz-flex;display: -webkit-flex;display: flex;flex-flow: row wrap;-ms-box-orient: horizontal;-webkit-flex-flow: row wrap;}.fl-col-group-responsive-reversed .fl-col:nth-of-type(1) { -webkit-box-ordinal-group: 12; -moz-box-ordinal-group: 12;-ms-flex-order: 12;-webkit-order: 12; order: 12; }.fl-col-group-responsive-reversed .fl-col:nth-of-type(2) { -webkit-box-ordinal-group: 11;-moz-box-ordinal-group: 11;-ms-flex-order: 11;-webkit-order: 11;order: 11;}.fl-col-group-responsive-reversed .fl-col:nth-of-type(3) { -webkit-box-ordinal-group: 10;-moz-box-ordinal-group: 10;-ms-flex-order: 10;-webkit-order: 10;order: 10; }.fl-col-group-responsive-reversed .fl-col:nth-of-type(4) { -webkit-box-ordinal-group: 9;-moz-box-ordinal-group: 9;-ms-flex-order: 9;-webkit-order: 9;order: 9; }.fl-col-group-responsive-reversed .fl-col:nth-of-type(5) { -webkit-box-ordinal-group: 8;-moz-box-ordinal-group: 8;-ms-flex-order: 8;-webkit-order: 8;order: 8; }.fl-col-group-responsive-reversed .fl-col:nth-of-type(6) { -webkit-box-ordinal-group: 7;-moz-box-ordinal-group: 7;-ms-flex-order: 7;-webkit-order: 7;order: 7; }.fl-col-group-responsive-reversed .fl-col:nth-of-type(7) { -webkit-box-ordinal-group: 6;-moz-box-ordinal-group: 6;-ms-flex-order: 6;-webkit-order: 6;order: 6; }.fl-col-group-responsive-reversed .fl-col:nth-of-type(8) { -webkit-box-ordinal-group: 5;-moz-box-ordinal-group: 5;-ms-flex-order: 5;-webkit-order: 5;order: 5; }.fl-col-group-responsive-reversed .fl-col:nth-of-type(9) { -webkit-box-ordinal-group: 4;-moz-box-ordinal-group: 4;-ms-flex-order: 4;-webkit-order: 4;order: 4; }.fl-col-group-responsive-reversed .fl-col:nth-of-type(10) { -webkit-box-ordinal-group: 3;-moz-box-ordinal-group: 3;-ms-flex-order: 3;-webkit-order: 3;order: 3; }.fl-col-group-responsive-reversed .fl-col:nth-of-type(11) { -webkit-box-ordinal-group: 2;-moz-box-ordinal-group: 2;-ms-flex-order: 2;-webkit-order: 2;order: 2; }.fl-col-group-responsive-reversed .fl-col:nth-of-type(12) {-webkit-box-ordinal-group: 1;-moz-box-ordinal-group: 1;-ms-flex-order: 1;-webkit-order: 1;order: 1;}.fl-col {clear: both;float: none;margin-left: auto;margin-right: auto;width: auto !important;}.fl-col-small {max-width: 400px;}.fl-block-col-resize {display:none;}.fl-row[data-node] .fl-row-content-wrap {border-left: none;border-right: none;margin: 0;padding-left: 0;padding-right: 0;}.fl-row[data-node] .fl-bg-video,.fl-row[data-node] .fl-bg-slideshow {left: 0;right: 0;}.fl-col[data-node] .fl-col-content {border-left: none;border-right: none;margin: 0;padding-left: 0;padding-right: 0;} }.page .fl-post-header, .single-fl-builder-template .fl-post-header { display:none; } .fl-node-639d8e77e8467 > .fl-row-content-wrap {padding-top:0px;padding-right:0px;padding-bottom:0px;padding-left:0px;} .fl-node-639d8ee6b9db7 > .fl-row-content-wrap {margin-top:20px;} .fl-node-639d8ee6b9db7 > .fl-row-content-wrap {padding-top:0px;padding-right:0px;padding-bottom:0px;padding-left:0px;} .fl-node-639d8f2b2192e > .fl-row-content-wrap {margin-top:20px;} .fl-node-639d8f2b2192e > .fl-row-content-wrap {padding-top:0px;padding-right:0px;padding-bottom:0px;padding-left:0px;} .fl-node-639d9003ceaff > .fl-row-content-wrap {margin-top:40px;} .fl-node-639d9003ceaff > .fl-row-content-wrap {padding-top:0px;padding-right:0px;padding-bottom:0px;padding-left:0px;}.fl-node-639d8e77e84e1 {width: 100%;}.fl-node-639d8e77e89dd {width: 33.33%;} .fl-node-639d8e77e89dd > .fl-col-content {margin-bottom:0px;margin-left:0px;}.fl-node-639d8e77e8acf {width: 33.34%;} .fl-node-639d8e77e8acf > .fl-col-content {margin-top:0px;margin-right:5px;margin-bottom:0px;margin-left:5px;}.fl-node-639d8e77e8a19 {width: 33.33%;}.fl-node-639d8ee6ba2ff {width: 100%;}.fl-node-639d8ee6ba4a5 {width: 100%;} .fl-node-639d8ee6ba4a5 > .fl-col-content {margin-top:0px;margin-right:5px;margin-bottom:0px;margin-left:5px;}.fl-node-639d8f2b21ccf {width: 100%;}.fl-node-639d8f3e830bf {width: 25%;}.fl-node-639d8f3e83101 {width: 25%;}.fl-node-639d8f3e83140 {width: 25%;}.fl-node-639d8f3e8317f {width: 25%;}.fl-node-639d9003d3052 {width: 50%;}.fl-node-639d9003d3052 > .fl-col-content {background-color: #f1f1f1;background-color: rgba(241,241,241, 1);} .fl-node-639d9003d3052 > .fl-col-content {padding-right:20px;padding-left:20px;}.fl-node-639d9003d3095 {width: 50%;}.fl-node-639d8ebeec93d .fl-separator {border-top:1px solid #0a0a0a;filter: alpha(opacity = 100);opacity: 1;width: 8%;max-width: 100%;margin: auto;} .fl-node-639d8ebeec93d > .fl-module-content {margin-top:0px;} .fl-node-639d8e77e8a93 > .fl-module-content {margin-top:0px;margin-right:0px;margin-bottom:0px;margin-left:10px;} .fl-node-639d8e77e8a56 > .fl-module-content {margin-top:0px;margin-right:0px;margin-bottom:0px;margin-left:0px;} .fl-node-639d8e77e8966 > .fl-module-content {margin-top:0px;margin-right:10px;margin-bottom:0px;margin-left:0px;} .fl-node-639d8ee6ba4e2 > .fl-module-content {margin-top:0px;margin-right:0px;margin-bottom:0px;margin-left:0px;} .fl-node-639d900e71a18 > .fl-module-content {margin-top:0px;margin-right:0px;margin-bottom:0px;margin-left:0px;}