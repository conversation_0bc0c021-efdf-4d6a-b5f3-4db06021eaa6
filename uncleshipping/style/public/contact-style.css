/* Contact Info Cards Styles */
.contact-info {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin: 50px 0;
    width: 100%;
}

.contact-item {
    width: 22%;
    text-align: center;
    padding: 20px 15px;
    margin-bottom: 20px;
    background: #fff;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.contact-item:hover {
    transform: translateY(-5px);
}

.contact-item .icon {
    width: 100px;
    height: 100px;
    margin: 0 auto 20px;
    background: #f5f5f5;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.contact-item .icon i {
    font-size: 36px;
    color: #333;
}

.contact-item h3 {
    font-size: 18px;
    margin-bottom: 15px;
    color: #333;
    font-weight: 600;
}

.contact-item p {
    font-size: 14px;
    color: #666;
    line-height: 1.6;
}

/* Responsive styles */
@media only screen and (max-width: 992px) {
    .contact-item {
        width: 48%;
    }
}

@media only screen and (max-width: 768px) {
    .contact-item {
        width: 100%;
    }
}