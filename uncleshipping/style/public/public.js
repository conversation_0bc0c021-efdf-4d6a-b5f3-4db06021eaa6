function addHideMsgPop(){$('.inquiry-pop-bd').fadeOut('fast');$('.pop_task').fadeOut('fast')}
	if($('body .inquiry-form-wrap').length>0){
		var webTop=$('body .inquiry-form-wrap').offset().top-80
		$('.add_email12').click(function(){
			 $("html, body").animate({ scrollTop:webTop }, 1000);
			 $(".ad_prompt").show().delay(3000).hide(300);
			})
		}else{
			$('.add_email12').click(function(){
				$('.inquiry-pop-bd').fadeIn('fast')
				 $('.pop_task').fadeIn('fast')
				})
			}
$('.add_email12').click(function(e) {e.stopPropagation();})
 $(function(){
 	$('.change-language-cont .lang-more').click(function(){
	if($(this).parents('.change-language-cont').find('.prisna-wp-translate-seo li.lang-item').hasClass('lang-item-hide')){
		$(this).text('More Language')
	}else{
		 $(this).text('x')
		}
	}) 
})
 