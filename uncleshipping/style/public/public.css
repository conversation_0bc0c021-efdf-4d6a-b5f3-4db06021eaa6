@charset "utf-8";

.change-language-cont {
	top: 27px;
	*top: 26px;
	padding: 10px 5px;
	width: 380px !important;
	max-height: 80vh !important;
	overflow-y: scroll !important;
}
.inquiry-form-wrap.ct-inquiry-form{    margin-top: 20px;}
.inquiry-form-wrap.ct-inquiry-form .layout{height: 420px !important;}
@media only screen and (max-width: 950px) {
.change-language-cont {
	 top: 0 !important;
		width: 100% !important;

height: 100vh !important;

max-height: unset !important;

overflow-y: visible !important;
	}
}
.product_list_items ,.product-list ul.product_list_items ,  .items_list ul.product_list_items{    width: 100%;
    margin: 0;}
 
.product_list_items .product_list_item { }

.product_list_item figure {
	    padding: 10px 0;
	background: #fff;
border-bottom: 1px #eee dashed;
	display: flex;
	justify-content: space-between;
	-webkit-justify-content: space-between;
	flex-wrap: wrap;
	-webkit-flex-wrap: wrap;
}

.product_list_item figure .item_img {
	display: inline-block;
	max-width: 20%;
}
.product_list_item figure .item_img img{ box-shadow: 1px 1px 4px rgba(0,0,0,.2); width: 100%;}
.product_list_item figure figcaption {
	width: calc(80% - 20px);
	display: flex;
	flex-flow: column;
	justify-content: space-between;
}

.product_list_item figure figcaption .item_title {
}

.product_list_item figure figcaption .item_title a {
	text-transform: capitalize;
	font-size: 18px;
	letter-spacing: .38px;
}

.product_list_item .product_btns {
	text-align: right;
	position: relative;
}

.product_list_item figure figcaption .item_info {
	font-size: 13px;
	margin: 13px 0;
	max-height: 80px;
	overflow: hidden;
	line-height: 20px;    letter-spacing: 0;
}

.product_list_item .product_btns .priduct_btn_inquiry ,.product_list_item .product_btns .priduct_btn_detail {
line-height: 30px;
    display: inline-block;
    padding: 0 25px;
    position: relative;
    background: #666;
    text-transform: uppercase;
    color: #fff;
    transition: all ease-in-out .35s;
    -webkit-transition: all ease-in-out .35s;
    letter-spacing: .38px;
    font-weight: bold;
    font-size: 13px;
    z-index: 0;
}

.product_list_item .product_btns .priduct_btn_inquiry {
     margin-right: 13px;
}

.product_list_item .product_btns .priduct_btn_detail {
 
}
.product_list_item .product_btns .priduct_btn_inquiry:after ,.product_list_item .product_btns .priduct_btn_detail:after{    position: absolute;
    content: "";
    left: 0;
    top: 0;
    width:0;
    height: 100%;
    background: #13adeb; 

      z-index: -1; transition: all ease-in-out .35s;
    -webkit-transition: all ease-in-out .35s;}
 

.product_list_item .product_btns .priduct_btn_inquiry:hover:after ,.product_list_item .product_btns .priduct_btn_detail:hover:after{   width: 100%;}

 

@media only screen and (max-width: 1280px) {
	.product_list_item figure figcaption .item_info {
		max-height: 60px;
	}
}

@media only screen and (max-width: 1200px) {
	.product_list_item figure figcaption .item_title a {
		font-size: 16px;
		letter-spacing: unset;
	}
}

@media only screen and (max-width: 768px) {
	.product_list_item figure .item_img {
		max-width: 25%;
	}

	.product_list_item figure figcaption {
		width: 73%;
	}
}

@media only screen and (max-width: 640px) {
	.product_list_item figure figcaption .item_title a {
		font-size: 14px;
	}

	.product_list_item figure figcaption .item_info {
		margin: 7px 0;
		font-size: 12px;
		line-height: 18px;
		max-height: 36px;
	}

	.product_list_item .product_btns .priduct_btn_inquiry, .product_list_item .product_btns .priduct_btn_detail {
		line-height: 24px;
	}
}

@media only screen and (max-width: 480px) {
	.product_list_item figure .item_img {
		max-width: 100%;
		width: 100%;
		text-align: center;
	}

	.product_list_item figure figcaption {
		width: 100%;
		padding: 7px 0;
	}

	.product_list_item figure figcaption .item_title a {
		font-size: 12px;
	}

	.product_list_item figure figcaption .item_info {
		margin: 3px 0;
	}

	.product_list_item .product_btns {
		text-align: left;
		margin-top: 7px;
	}
}
