// 在线聊天功能的JavaScript文件
// 替代原有的在线聊天功能

(function() {
  // 初始化聊天功能
  console.log('在线聊天功能初始化');
  
  document.addEventListener('DOMContentLoaded', function() {
    // 添加侧边栏聊天功能的交互逻辑
    var showBtn = document.querySelector('.show_btn');
    var closeBtn = document.querySelector('.close_btn');
    var sideContent = document.querySelector('.side_content');
    
    if (showBtn) {
      showBtn.addEventListener('click', function() {
        if (sideContent) {
          sideContent.style.display = 'block';
          showBtn.style.display = 'none';
        }
      });
    }
    
    if (closeBtn) {
      closeBtn.addEventListener('click', function() {
        if (sideContent) {
          sideContent.style.display = 'none';
          if (showBtn) {
            showBtn.style.display = 'block';
          }
        }
      });
    }
  });
})();