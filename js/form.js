// 表单处理JavaScript文件
// 替代原有的表单处理逻辑

function showMsgPop() {
  console.log("显示消息弹窗");
  var inquiryPop = document.querySelector('.inquiry-pop-bd');
  if (inquiryPop) {
    inquiryPop.style.display = 'block';
  }
}

function hideMsgPop() {
  console.log("隐藏消息弹窗");
  var inquiryPop = document.querySelector('.inquiry-pop-bd');
  if (inquiryPop) {
    inquiryPop.style.display = 'none';
  }
}

// 初始化表单处理
document.addEventListener('DOMContentLoaded', function() {
  console.log("表单处理初始化");
  
  // 隐藏初始弹窗
  hideMsgPop();
  
  // 为关闭按钮添加事件监听
  var closeBtn = document.querySelector('.ico-close-pop');
  if (closeBtn) {
    closeBtn.addEventListener('click', hideMsgPop);
  }
});