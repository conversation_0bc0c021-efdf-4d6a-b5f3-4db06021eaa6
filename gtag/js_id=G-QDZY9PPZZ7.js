
// Copyright 2012 Google Inc. All rights reserved.
 
(function(){

var data = {
"resource": {
  "version":"1",
  
  "macros":[{"function":"__e"},{"function":"__c","vtp_value":"google.cn"},{"function":"__c","vtp_value":1}],
  "tags":[{"function":"__ogt_1p_data_v2","priority":12,"vtp_isAutoEnabled":true,"vtp_autoCollectExclusionSelectors":["list",["map","exclusionSelector",""]],"vtp_isEnabled":true,"vtp_cityType":"CSS_SELECTOR","vtp_manualEmailEnabled":false,"vtp_firstNameType":"CSS_SELECTOR","vtp_countryType":"CSS_SELECTOR","vtp_cityValue":"","vtp_emailType":"CSS_SELECTOR","vtp_regionType":"CSS_SELECTOR","vtp_autoEmailEnabled":true,"vtp_postalCodeValue":"","vtp_lastNameValue":"","vtp_phoneType":"CSS_SELECTOR","vtp_phoneValue":"","vtp_streetType":"CSS_SELECTOR","vtp_autoPhoneEnabled":false,"vtp_postalCodeType":"CSS_SELECTOR","vtp_emailValue":"","vtp_firstNameValue":"","vtp_streetValue":"","vtp_lastNameType":"CSS_SELECTOR","vtp_autoAddressEnabled":false,"vtp_regionValue":"","vtp_countryValue":"","vtp_isAutoCollectPiiEnabledFlag":false,"tag_id":4},{"function":"__ccd_ga_first","priority":11,"vtp_instanceDestinationId":"G-QDZY9PPZZ7","tag_id":17},{"function":"__set_product_settings","priority":10,"vtp_instanceDestinationId":"G-QDZY9PPZZ7","vtp_foreignTldMacroResult":["macro",1],"vtp_isChinaVipRegionMacroResult":["macro",2],"tag_id":16},{"function":"__ccd_ga_regscope","priority":9,"vtp_settingsTable":["list",["map","redactFieldGroup","DEVICE_AND_GEO","disallowAllRegions",false,"disallowedRegions",""],["map","redactFieldGroup","GOOGLE_SIGNALS","disallowAllRegions",true,"disallowedRegions",""]],"vtp_instanceDestinationId":"G-QDZY9PPZZ7","tag_id":15},{"function":"__ccd_em_download","priority":8,"vtp_includeParams":true,"vtp_instanceDestinationId":"G-QDZY9PPZZ7","tag_id":14},{"function":"__ccd_em_form","priority":7,"vtp_includeParams":true,"vtp_instanceDestinationId":"G-QDZY9PPZZ7","tag_id":13},{"function":"__ccd_em_outbound_click","priority":6,"vtp_includeParams":true,"vtp_instanceDestinationId":"G-QDZY9PPZZ7","tag_id":12},{"function":"__ccd_em_page_view","priority":5,"vtp_historyEvents":true,"vtp_includeParams":true,"vtp_instanceDestinationId":"G-QDZY9PPZZ7","tag_id":11},{"function":"__ccd_em_scroll","priority":4,"vtp_includeParams":true,"vtp_instanceDestinationId":"G-QDZY9PPZZ7","tag_id":10},{"function":"__ccd_em_site_search","priority":3,"vtp_searchQueryParams":"q,s,search,query,keyword","vtp_includeParams":true,"vtp_instanceDestinationId":"G-QDZY9PPZZ7","tag_id":9},{"function":"__ccd_em_video","priority":2,"vtp_includeParams":true,"vtp_instanceDestinationId":"G-QDZY9PPZZ7","tag_id":8},{"function":"__ccd_conversion_marking","priority":1,"vtp_conversionRules":["list",["map","matchingRules","{\"type\":5,\"args\":[{\"stringValue\":\"purchase\"},{\"contextValue\":{\"namespaceType\":1,\"keyParts\":[\"eventName\"]}}]}"]],"vtp_instanceDestinationId":"G-QDZY9PPZZ7","tag_id":7},{"function":"__gct","vtp_trackingId":"G-QDZY9PPZZ7","vtp_sessionDuration":0,"tag_id":1},{"function":"__ccd_ga_last","priority":0,"vtp_instanceDestinationId":"G-QDZY9PPZZ7","tag_id":6}],
  "predicates":[{"function":"_eq","arg0":["macro",0],"arg1":"gtm.js"},{"function":"_eq","arg0":["macro",0],"arg1":"gtm.init"}],
  "rules":[[["if",0],["add",12]],[["if",1],["add",0,13,11,10,9,8,7,6,5,4,3,2,1]]]
},
"runtime":[ [50,"__c",[46,"a"],[36,[17,[15,"a"],"value"]]]
 ,[50,"__ccd_conversion_marking",[46,"a"],[22,[30,[28,[17,[15,"a"],"conversionRules"]],[20,[17,[17,[15,"a"],"conversionRules"],"length"],0]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"b",["require","internal.copyPreHit"]],[52,"c",["require","internal.evaluateBooleanExpression"]],[52,"d",["require","internal.registerCcdCallback"]],[52,"e","is_conversion"],[52,"f","is_first_visit"],[52,"g","is_first_visit_conversion"],[52,"h","is_session_start"],[52,"i","is_session_start_conversion"],[52,"j","first_visit"],[52,"k","session_start"],[41,"l"],[41,"m"],["d",[17,[15,"a"],"instanceDestinationId"],[51,"",[7,"n"],[52,"o",[8,"preHit",[15,"n"]]],[65,"p",[17,[15,"a"],"conversionRules"],[46,[53,[22,["c",[17,[15,"p"],"matchingRules"],[15,"o"]],[46,[53,[2,[15,"n"],"setMetadata",[7,[15,"e"],true]],[4]]]]]]],[22,[2,[15,"n"],"getMetadata",[7,[15,"f"]]],[46,[53,[22,[28,[15,"l"]],[46,[53,[52,"p",["b",[15,"n"],[8,"omitHitData",true,"omitMetadata",true]]],[2,[15,"p"],"setEventName",[7,[15,"j"]]],[3,"l",[8,"preHit",[15,"p"]]]]]],[65,"p",[17,[15,"a"],"conversionRules"],[46,[53,[22,["c",[17,[15,"p"],"matchingRules"],[15,"l"]],[46,[53,[2,[15,"n"],"setMetadata",[7,[15,"g"],true]],[4]]]]]]]]]],[22,[2,[15,"n"],"getMetadata",[7,[15,"h"]]],[46,[53,[22,[28,[15,"m"]],[46,[53,[52,"p",["b",[15,"n"],[8,"omitHitData",true,"omitMetadata",true]]],[2,[15,"p"],"setEventName",[7,[15,"k"]]],[3,"m",[8,"preHit",[15,"p"]]]]]],[65,"p",[17,[15,"a"],"conversionRules"],[46,[53,[22,["c",[17,[15,"p"],"matchingRules"],[15,"m"]],[46,[53,[2,[15,"n"],"setMetadata",[7,[15,"i"],true]],[4]]]]]]]]]]]],[2,[15,"a"],"gtmOnSuccess",[7]],[36]]
 ,[50,"__ccd_em_download",[46,"a"],[50,"r",[46,"x"],[36,[1,[15,"x"],[21,[2,[2,[15,"x"],"toLowerCase",[7]],"match",[7,[15,"q"]]],[45]]]]],[50,"s",[46,"x"],[52,"y",[2,[17,[15,"x"],"pathname"],"split",[7,"."]]],[52,"z",[39,[18,[17,[15,"y"],"length"],1],[16,[15,"y"],[37,[17,[15,"y"],"length"],1]],""]],[36,[16,[2,[15,"z"],"split",[7,"/"]],0]]],[50,"t",[46,"x"],[36,[39,[12,[2,[17,[15,"x"],"pathname"],"substring",[7,0,1]],"/"],[17,[15,"x"],"pathname"],[0,"/",[17,[15,"x"],"pathname"]]]]],[50,"u",[46,"x"],[41,"y"],[3,"y",""],[22,[1,[15,"x"],[17,[15,"x"],"href"]],[46,[53,[41,"z"],[3,"z",[2,[17,[15,"x"],"href"],"indexOf",[7,"#"]]],[3,"y",[39,[23,[15,"z"],0],[17,[15,"x"],"href"],[2,[17,[15,"x"],"href"],"substring",[7,0,[15,"z"]]]]]]]],[36,[15,"y"]]],[50,"w",[46,"x"],[52,"y",[8]],[43,[15,"y"],[15,"j"],true],[43,[15,"y"],[15,"f"],true],[43,[15,"x"],"eventMetadata",[15,"y"]]],[52,"b",[13,[41,"$0"],[3,"$0",["require","internal.getFlags"]],["$0"]]],[52,"c",["require","internal.getProductSettingsParameter"]],[52,"d",["require","templateStorage"]],[52,"e",[15,"__module_ccdEmDownloadActivity"]],[52,"f","speculative"],[52,"g","ae_block_downloads"],[52,"h","file_download"],[52,"i","isRegistered"],[52,"j","em_event"],[52,"k",[17,[15,"a"],"instanceDestinationId"]],[22,["c",[15,"k"],[15,"g"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[2,[15,"e"],"registerDownloadActivityCallback",[7,[15,"k"],[17,[15,"a"],"includeParams"]]],[22,[2,[15,"d"],"getItem",[7,[15,"i"]]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"l",["require","internal.addDataLayerEventListener"]],[52,"m",["require","internal.enableAutoEventOnLinkClick"]],[52,"n",["require","internal.getDestinationIds"]],[52,"o",["require","parseUrl"]],[52,"p",["require","internal.sendGtagEvent"]],[52,"q",[0,"^(pdf|xlsx?|docx?|txt|rtf|csv|exe|key|pp(s|t|tx)|7z|pkg|rar|gz|zip|avi|","mov|mp4|mpe?g|wmv|midi?|mp3|wav|wma)$"]],[52,"v",["m",[8,"checkValidation",true]]],[22,[28,[15,"v"]],[46,[53,[2,[15,"a"],"gtmOnFailure",[7]],[36]]]],[2,[15,"d"],"setItem",[7,[15,"i"],true]],["l","gtm.linkClick",[51,"",[7,"x","y"],["y"],[52,"z",[8,"eventId",[16,[15,"x"],"gtm.uniqueEventId"]]],[22,[16,[15,"b"],"enableDeferAllEnhancedMeasurement"],[46,[53,[43,[15,"z"],"deferrable",true]]]],[52,"ba",[16,[15,"x"],"gtm.elementUrl"]],[52,"bb",["o",[15,"ba"]]],[22,[28,[15,"bb"]],[46,[36]]],[52,"bc",["s",[15,"bb"]]],[22,[28,["r",[15,"bc"]]],[46,[53,[36]]]],[52,"bd",[8,"link_id",[16,[15,"x"],"gtm.elementId"],"link_url",["u",[15,"bb"]],"link_text",[16,[15,"x"],"gtm.elementText"],"file_name",["t",[15,"bb"]],"file_extension",[15,"bc"]]],["w",[15,"z"]],["p",["n"],[15,"h"],[15,"bd"],[15,"z"]]],[15,"v"]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_em_form",[46,"a"],[50,"t",[46,"ba"],[52,"bb",[30,[16,[15,"ba"],[15,"l"]],[8]]],[43,[15,"bb"],"event_usage",[7,8]],[43,[15,"ba"],[15,"l"],[15,"bb"]]],[50,"u",[46,"ba","bb"],[52,"bc",[30,[16,[15,"ba"],[15,"l"]],[8]]],[43,[15,"bc"],[15,"k"],true],[43,[15,"bc"],[15,"f"],true],[22,[1,[15,"o"],[16,[15,"bb"],"gtm.formCanceled"]],[46,[53,[43,[15,"bc"],[15,"m"],true]]]],[43,[15,"ba"],[15,"l"],[15,"bc"]]],[50,"v",[46,"ba","bb","bc"],[52,"bd",[2,["r"],"filter",[7,[51,"",[7,"bf"],[36,[20,[2,[15,"bf"],"indexOf",[7,"AW-"]],0]]]]]],[22,[18,[17,[15,"bd"],"length"],0],[46,[53,["s",[15,"bd"],[15,"ba"],[15,"bb"],[15,"bc"]]]]],[52,"be",[2,["r"],"filter",[7,[51,"",[7,"bf"],[36,[21,[2,[15,"bf"],"indexOf",[7,"AW-"]],0]]]]]],[22,[18,[17,[15,"be"],"length"],0],[46,[53,[22,[16,[15,"b"],"enableDeferAllEnhancedMeasurement"],[46,[53,[43,[15,"bc"],"deferrable",true]]]],["s",[15,"be"],[15,"ba"],[15,"bb"],[15,"bc"]]]]]],[52,"b",[13,[41,"$0"],[3,"$0",["require","internal.getFlags"]],["$0"]]],[52,"c",["require","internal.getProductSettingsParameter"]],[52,"d",["require","templateStorage"]],[52,"e",[15,"__module_ccdEmFormActivity"]],[52,"f","speculative"],[52,"g","ae_block_form"],[52,"h","form_submit"],[52,"i","form_start"],[52,"j","isRegistered"],[52,"k","em_event"],[52,"l","eventMetadata"],[52,"m","form_event_canceled"],[52,"n",[17,[15,"a"],"instanceDestinationId"]],[52,"o",[28,[28,[16,[15,"b"],"enableFormSkipValidation"]]]],[22,["c",[15,"n"],[15,"g"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[2,[15,"e"],"registerFormActivityCallback",[7,[17,[15,"a"],"instanceDestinationId"],[17,[15,"a"],"skipValidation"],[17,[15,"a"],"includeParams"]]],[22,[2,[15,"d"],"getItem",[7,[15,"j"]]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[2,[15,"d"],"setItem",[7,[15,"j"],true]],[52,"p",["require","internal.addFormInteractionListener"]],[52,"q",["require","internal.addFormSubmitListener"]],[52,"r",["require","internal.getDestinationIds"]],[52,"s",["require","internal.sendGtagEvent"]],[52,"w",[8]],[52,"x",[51,"",[7,"ba","bb"],[22,[15,"bb"],[46,["bb"]]],[52,"bc",[16,[15,"ba"],"gtm.elementId"]],[22,[16,[15,"w"],[15,"bc"]],[46,[36]]],[43,[15,"w"],[15,"bc"],true],[52,"bd",[8,"form_id",[15,"bc"],"form_name",[16,[15,"ba"],"gtm.interactedFormName"],"form_destination",[16,[15,"ba"],"gtm.elementUrl"],"form_length",[16,[15,"ba"],"gtm.interactedFormLength"],"first_field_id",[16,[15,"ba"],"gtm.interactedFormFieldId"],"first_field_name",[16,[15,"ba"],"gtm.interactedFormFieldName"],"first_field_type",[16,[15,"ba"],"gtm.interactedFormFieldType"],"first_field_position",[16,[15,"ba"],"gtm.interactedFormFieldPosition"]]],[52,"be",[8,"eventId",[17,[15,"a"],"gtmEventId"]]],["t",[15,"be"]],["u",[15,"be"],[15,"ba"]],["v",[15,"i"],[15,"bd"],[15,"be"]]]],[52,"y",[16,[15,"b"],"useEnableAutoEventOnFormApis"]],[52,"z",[51,"",[7,"ba","bb"],["x",[15,"ba"],[44]],[52,"bc",[8,"form_id",[16,[15,"ba"],"gtm.elementId"],"form_name",[16,[15,"ba"],"gtm.interactedFormName"],"form_destination",[16,[15,"ba"],"gtm.elementUrl"],"form_length",[16,[15,"ba"],"gtm.interactedFormLength"],"form_submit_text",[39,[15,"y"],[16,[15,"ba"],"gtm.formSubmitElementText"],[16,[15,"ba"],"gtm.formSubmitButtonText"]]]],[43,[15,"bc"],"event_callback",[15,"bb"]],[52,"bd",[8,"eventId",[17,[15,"a"],"gtmEventId"]]],["t",[15,"bd"]],["u",[15,"bd"],[15,"ba"]],["v",[15,"h"],[15,"bc"],[15,"bd"]]]],[22,[15,"y"],[46,[53,[52,"ba",["require","internal.addDataLayerEventListener"]],[52,"bb",["require","internal.enableAutoEventOnFormSubmit"]],[52,"bc",["require","internal.enableAutoEventOnFormInteraction"]],[52,"bd",["bc"]],[22,[28,[15,"bd"]],[46,[53,[2,[15,"a"],"gtmOnFailure",[7]],[36]]]],["ba","gtm.formInteract",[15,"x"],[15,"bd"]],[52,"be",["bb",[8,"checkValidation",[28,[15,"o"]],"waitForTags",false]]],[22,[28,[15,"be"]],[46,[53,[2,[15,"a"],"gtmOnFailure",[7]],[36]]]],["ba","gtm.formSubmit",[15,"z"],[15,"be"]]]],[46,[53,["p",[15,"x"]],["q",[15,"z"],[8,"waitForCallbacks",false,"checkValidation",[28,[15,"o"]]]]]]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_em_outbound_click",[46,"a"],[50,"s",[46,"y"],[22,[28,[15,"y"]],[46,[36,[44]]]],[41,"z"],[3,"z",""],[22,[1,[15,"y"],[17,[15,"y"],"href"]],[46,[53,[41,"ba"],[3,"ba",[2,[17,[15,"y"],"href"],"indexOf",[7,"#"]]],[3,"z",[39,[23,[15,"ba"],0],[17,[15,"y"],"href"],[2,[17,[15,"y"],"href"],"substring",[7,0,[15,"ba"]]]]]]]],[36,[15,"z"]]],[50,"t",[46,"y"],[22,[28,[15,"y"]],[46,[36,[44]]]],[41,"z"],[3,"z",[17,[15,"y"],"hostname"]],[52,"ba",[2,[15,"z"],"match",[7,"^www\\d*\\."]]],[22,[1,[15,"ba"],[16,[15,"ba"],0]],[46,[3,"z",[2,[15,"z"],"substring",[7,[17,[16,[15,"ba"],0],"length"]]]]]],[36,[15,"z"]]],[50,"u",[46,"y"],[22,[28,[15,"y"]],[46,[36,false]]],[52,"z",[2,[17,[15,"y"],"hostname"],"toLowerCase",[7]]],[22,[1,[17,[15,"b"],"enableGa4OutboundClicksFix"],[28,[15,"z"]]],[46,[53,[36,false]]]],[41,"ba"],[3,"ba",[2,["t",["q",["p"]]],"toLowerCase",[7]]],[41,"bb"],[3,"bb",[37,[17,[15,"z"],"length"],[17,[15,"ba"],"length"]]],[22,[1,[18,[15,"bb"],0],[29,[2,[15,"ba"],"charAt",[7,0]],"."]],[46,[53,[32,[15,"bb"],[3,"bb",[37,[15,"bb"],1]]],[3,"ba",[0,".",[15,"ba"]]]]]],[22,[1,[19,[15,"bb"],0],[12,[2,[15,"z"],"indexOf",[7,[15,"ba"],[15,"bb"]]],[15,"bb"]]],[46,[53,[36,false]]]],[36,true]],[50,"x",[46,"y"],[52,"z",[8]],[43,[15,"z"],[15,"j"],true],[43,[15,"z"],[15,"f"],true],[43,[15,"y"],"eventMetadata",[15,"z"]]],[52,"b",[13,[41,"$0"],[3,"$0",["require","internal.getFlags"]],["$0"]]],[52,"c",["require","internal.getProductSettingsParameter"]],[52,"d",["require","templateStorage"]],[52,"e",[15,"__module_ccdEmOutboundClickActivity"]],[52,"f","speculative"],[52,"g","ae_block_outbound_click"],[52,"h","click"],[52,"i","isRegistered"],[52,"j","em_event"],[52,"k",[17,[15,"a"],"instanceDestinationId"]],[22,["c",[15,"k"],[15,"g"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[2,[15,"e"],"registerOutbackClickActivityCallback",[7,[15,"k"],[17,[15,"a"],"includeParams"]]],[22,[2,[15,"d"],"getItem",[7,[15,"i"]]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"l",["require","internal.addDataLayerEventListener"]],[52,"m",["require","internal.enableAutoEventOnLinkClick"]],[52,"n",["require","internal.getDestinationIds"]],[52,"o",["require","internal.getRemoteConfigParameter"]],[52,"p",["require","getUrl"]],[52,"q",["require","parseUrl"]],[52,"r",["require","internal.sendGtagEvent"]],[52,"v",["o",[15,"k"],"cross_domain_conditions"]],[52,"w",["m",[8,"affiliateDomains",[15,"v"],"checkValidation",true,"waitForTags",false]]],[22,[28,[15,"w"]],[46,[53,[2,[15,"a"],"gtmOnFailure",[7]],[36]]]],[2,[15,"d"],"setItem",[7,[15,"i"],true]],["l","gtm.linkClick",[51,"",[7,"y","z"],[52,"ba",["q",[16,[15,"y"],"gtm.elementUrl"]]],[22,[28,["u",[15,"ba"]]],[46,[53,["z"],[36]]]],[52,"bb",[8,"link_id",[16,[15,"y"],"gtm.elementId"],"link_classes",[16,[15,"y"],"gtm.elementClasses"],"link_url",["s",[15,"ba"]],"link_domain",["t",[15,"ba"]],"outbound",true]],[43,[15,"bb"],"event_callback",[15,"z"]],[52,"bc",[8,"eventId",[16,[15,"y"],"gtm.uniqueEventId"]]],[22,[16,[15,"b"],"enableDeferAllEnhancedMeasurement"],[46,[53,[43,[15,"bc"],"deferrable",true]]]],["x",[15,"bc"]],["r",["n"],[15,"h"],[15,"bb"],[15,"bc"]]],[15,"w"]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_em_page_view",[46,"a"],[50,"r",[46,"s"],[52,"t",[8]],[43,[15,"t"],[17,[15,"g"],"EM_EVENT"],true],[43,[15,"t"],[17,[15,"g"],"SPECULATIVE"],true],[43,[15,"s"],"eventMetadata",[15,"t"]]],[22,[28,[17,[15,"a"],"historyEvents"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"b",[13,[41,"$0"],[3,"$0",["require","internal.getFlags"]],["$0"]]],[52,"c",["require","internal.getProductSettingsParameter"]],[52,"d",["require","internal.setRemoteConfigParameter"]],[52,"e",["require","templateStorage"]],[52,"f",[15,"__module_ccdEmPageViewActivity"]],[52,"g",[15,"__module_gtagMetadataSchema"]],[52,"h","ae_block_history"],[52,"i","page_view"],[52,"j","isRegistered"],[52,"k",[17,[15,"a"],"instanceDestinationId"]],[22,["c",[15,"k"],[15,"h"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[2,[15,"f"],"registerPageViewActivityCallback",[7,[15,"k"]]],[22,[2,[15,"e"],"getItem",[7,[15,"j"]]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"l",["require","internal.addDataLayerEventListener"]],[52,"m",["require","internal.enableAutoEventOnHistoryChange"]],[52,"n",["require","internal.getDestinationIds"]],[52,"o",["require","internal.sendGtagEvent"]],[52,"p",[8,"interval",1000,"useV2EventName",true]],[52,"q",["m",[15,"p"]]],[22,[28,[15,"q"]],[46,[53,[2,[15,"a"],"gtmOnFailure",[7]],[36]]]],[2,[15,"e"],"setItem",[7,[15,"j"],true]],["l","gtm.historyChange-v2",[51,"",[7,"s","t"],["t"],[52,"u",[16,[15,"s"],"gtm.oldUrl"]],[22,[20,[16,[15,"s"],"gtm.newUrl"],[15,"u"]],[46,[36]]],[52,"v",[16,[15,"s"],"gtm.historyChangeSource"]],[22,[1,[1,[21,[15,"v"],"pushState"],[21,[15,"v"],"popstate"]],[21,[15,"v"],"replaceState"]],[46,[53,[36]]]],[52,"w",[8]],[22,[17,[15,"a"],"includeParams"],[46,[53,[43,[15,"w"],"page_location",[16,[15,"s"],"gtm.newUrl"]],[43,[15,"w"],"page_referrer",[15,"u"]]]]],[52,"x",[8,"eventId",[16,[15,"s"],"gtm.uniqueEventId"]]],[22,[16,[15,"b"],"enableDeferAllEnhancedMeasurement"],[46,[53,[43,[15,"x"],"deferrable",true]]]],["r",[15,"x"]],["o",["n"],[15,"i"],[15,"w"],[15,"x"]]],[15,"q"]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_em_scroll",[46,"a"],[50,"q",[46,"r"],[52,"s",[8]],[43,[15,"s"],[15,"j"],true],[43,[15,"s"],[15,"f"],true],[43,[15,"r"],"eventMetadata",[15,"s"]]],[52,"b",[13,[41,"$0"],[3,"$0",["require","internal.getFlags"]],["$0"]]],[52,"c",["require","internal.getProductSettingsParameter"]],[52,"d",["require","templateStorage"]],[52,"e",[15,"__module_ccdEmScrollActivity"]],[52,"f","speculative"],[52,"g","ae_block_scroll"],[52,"h","scroll"],[52,"i","isRegistered"],[52,"j","em_event"],[52,"k",[17,[15,"a"],"instanceDestinationId"]],[22,["c",[15,"k"],[15,"g"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[2,[15,"e"],"registerScrollActivityCallback",[7,[15,"k"],[17,[15,"a"],"includeParams"]]],[22,[2,[15,"d"],"getItem",[7,[15,"i"]]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"l",["require","internal.addDataLayerEventListener"]],[52,"m",["require","internal.enableAutoEventOnScroll"]],[52,"n",["require","internal.getDestinationIds"]],[52,"o",["require","internal.sendGtagEvent"]],[52,"p",["m",[8,"verticalThresholdUnits","PERCENT","verticalThresholds",90]]],[22,[28,[15,"p"]],[46,[53,[2,[15,"a"],"gtmOnFailure",[7]],[36]]]],[2,[15,"d"],"setItem",[7,[15,"i"],true]],["l","gtm.scrollDepth",[51,"",[7,"r","s"],["s"],[52,"t",[8,"eventId",[16,[15,"r"],"gtm.uniqueEventId"]]],[22,[16,[15,"b"],"enableDeferAllEnhancedMeasurement"],[46,[53,[43,[15,"t"],"deferrable",true]]]],[52,"u",[8,"percent_scrolled",[16,[15,"r"],"gtm.scrollThreshold"]]],["q",[15,"t"]],["o",["n"],[15,"h"],[15,"u"],[15,"t"]]],[15,"p"]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_em_site_search",[46,"a"],[52,"b",["require","getQueryParameters"]],[52,"c",["require","internal.sendGtagEvent"]],[52,"d",["require","getContainerVersion"]],[52,"e",[15,"__module_ccdEmSiteSearchActivity"]],[52,"f",[2,[15,"e"],"getSearchTerm",[7,[17,[15,"a"],"searchQueryParams"],[15,"b"]]]],[52,"g",[30,[17,[15,"a"],"instanceDestinationId"],[17,["d"],"containerId"]]],[52,"h",[8,"deferrable",true,"eventId",[17,[15,"a"],"gtmEventId"],"eventMetadata",[8,"em_event",true]]],[22,[15,"f"],[46,[53,[52,"i",[39,[28,[28,[17,[15,"a"],"includeParams"]]],[2,[15,"e"],"buildEventParams",[7,[15,"f"],[17,[15,"a"],"additionalQueryParams"],[15,"b"]]],[8]]],["c",[15,"g"],"view_search_results",[15,"i"],[15,"h"]]]]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_em_video",[46,"a"],[50,"s",[46,"t"],[52,"u",[8]],[43,[15,"u"],[15,"l"],true],[43,[15,"u"],[15,"f"],true],[43,[15,"t"],"eventMetadata",[15,"u"]]],[52,"b",[13,[41,"$0"],[3,"$0",["require","internal.getFlags"]],["$0"]]],[52,"c",["require","internal.getProductSettingsParameter"]],[52,"d",["require","templateStorage"]],[52,"e",[15,"__module_ccdEmVideoActivity"]],[52,"f","speculative"],[52,"g","ae_block_video"],[52,"h","video_start"],[52,"i","video_progress"],[52,"j","video_complete"],[52,"k","isRegistered"],[52,"l","em_event"],[52,"m",[17,[15,"a"],"instanceDestinationId"]],[22,["c",[15,"m"],[15,"g"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[2,[15,"e"],"registerVideoActivityCallback",[7,[15,"m"],[17,[15,"a"],"includeParams"]]],[22,[2,[15,"d"],"getItem",[7,[15,"k"]]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"n",["require","internal.addDataLayerEventListener"]],[52,"o",["require","internal.enableAutoEventOnYouTubeActivity"]],[52,"p",["require","internal.getDestinationIds"]],[52,"q",["require","internal.sendGtagEvent"]],[52,"r",["o",[8,"captureComplete",true,"captureStart",true,"progressThresholdsPercent",[7,10,25,50,75]]]],[22,[28,[15,"r"]],[46,[53,[2,[15,"a"],"gtmOnFailure",[7]],[36]]]],[2,[15,"d"],"setItem",[7,[15,"k"],true]],["n","gtm.video",[51,"",[7,"t","u"],["u"],[52,"v",[16,[15,"t"],"gtm.videoStatus"]],[41,"w"],[22,[20,[15,"v"],"start"],[46,[53,[3,"w",[15,"h"]]]],[46,[22,[20,[15,"v"],"progress"],[46,[53,[3,"w",[15,"i"]]]],[46,[22,[20,[15,"v"],"complete"],[46,[53,[3,"w",[15,"j"]]]],[46,[53,[36]]]]]]]],[52,"x",[8,"video_current_time",[16,[15,"t"],"gtm.videoCurrentTime"],"video_duration",[16,[15,"t"],"gtm.videoDuration"],"video_percent",[16,[15,"t"],"gtm.videoPercent"],"video_provider",[16,[15,"t"],"gtm.videoProvider"],"video_title",[16,[15,"t"],"gtm.videoTitle"],"video_url",[16,[15,"t"],"gtm.videoUrl"],"visible",[16,[15,"t"],"gtm.videoVisible"]]],[52,"y",[8,"eventId",[16,[15,"t"],"gtm.uniqueEventId"]]],[22,[16,[15,"b"],"enableDeferAllEnhancedMeasurement"],[46,[53,[43,[15,"y"],"deferrable",true]]]],["s",[15,"y"]],["q",["p"],[15,"w"],[15,"x"],[15,"y"]]],[15,"r"]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_ga_first",[46,"a"],[52,"b",[13,[41,"$0"],[3,"$0",["require","internal.getFlags"]],["$0"]]],[52,"c",["require","internal.registerCcdCallback"]],[52,"d",[15,"__module_gtagMetadataSchema"]],[22,[28,[16,[15,"b"],"enableCcdSendTo"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"e",[51,"",[7,"f"],[52,"g",[2,[15,"f"],"getMetadata",[7,[17,[15,"d"],"SEND_TO_DESTINATIONS"]]]],[22,[28,[15,"g"]],[46,[36]]],[22,[20,[2,[15,"g"],"indexOf",[7,[17,[15,"a"],"instanceDestinationId"]]],[27,1]],[46,[53,[2,[15,"f"],"abort",[7]]]]]]],["c",[17,[15,"a"],"instanceDestinationId"],[15,"e"]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_ga_last",[46,"a"],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_ga_regscope",[46,"a"],[52,"b",[15,"__module_ccdGaRegionScopedSettings"]],[52,"c",[2,[15,"b"],"extractRedactedLocations",[7,[15,"a"]]]],[2,[15,"b"],"applyRegionScopedSettings",[7,[15,"a"],[15,"c"]]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__e",[46,"a"],[36,[13,[41,"$0"],[3,"$0",["require","internal.getEventData"]],["$0","event"]]]]
 ,[50,"__ogt_1p_data_v2",[46,"a"],[50,"o",[46,"t","u"],[52,"v",[7]],[52,"w",[2,[15,"b"],"keys",[7,[15,"t"]]]],[65,"x",[15,"w"],[46,[53,[52,"y",[30,[16,[15,"t"],[15,"x"]],[7]]],[52,"z",[39,[18,[17,[15,"y"],"length"],0],"1","0"]],[52,"ba",[39,["p",[15,"u"],[15,"x"]],"1","0"]],[2,[15,"v"],"push",[7,[0,[0,[0,[16,[15,"n"],[15,"x"]],"-"],[15,"z"]],[15,"ba"]]]]]]],[36,[2,[15,"v"],"join",[7,"~"]]]],[50,"p",[46,"t","u"],[22,[28,[15,"t"]],[46,[53,[36,false]]]],[38,[15,"u"],[46,"email","phone_number","first_name","last_name","street","city","region","postal_code","country"],[46,[5,[46,[36,[28,[28,[16,[15,"t"],"email"]]]]]],[5,[46,[36,[28,[28,[16,[15,"t"],"phone_number"]]]]]],[5,[46]],[5,[46]],[5,[46]],[5,[46]],[5,[46]],[5,[46]],[5,[46,[36,["q",[15,"t"],[15,"u"]]]]],[9,[46,[36,false]]]]]],[50,"q",[46,"t","u"],[36,[1,[28,[28,[16,[15,"t"],"address"]]],[28,[28,[16,[16,[15,"t"],"address"],[15,"u"]]]]]]],[50,"r",[46,"t","u","v"],[22,[20,[16,[15,"u"],"type"],[15,"v"]],[46,[53,[22,[28,[15,"t"]],[46,[53,[3,"t",[8]]]]],[22,[28,[16,[15,"t"],[15,"v"]]],[46,[53,[43,[15,"t"],[15,"v"],[16,[15,"u"],"userData"]]]]]]]],[36,[15,"t"]]],[50,"s",[46,"t","u","v"],[22,[28,[16,[15,"a"],[15,"v"]]],[46,[36]]],[43,[15,"t"],[15,"u"],[8,"value",[16,[15,"a"],[15,"v"]]]]],[22,[28,[17,[15,"a"],"isEnabled"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"b",["require","Object"]],[52,"c",[13,[41,"$0"],[3,"$0",["require","internal.getFlags"]],["$0"]]],[52,"d",["require","internal.getDestinationIds"]],[52,"e",["require","internal.getProductSettingsParameter"]],[52,"f",["require","internal.detectUserProvidedData"]],[52,"g",["require","queryPermission"]],[52,"h",["require","internal.setRemoteConfigParameter"]],[52,"i",["require","internal.registerCcdCallback"]],[52,"j",[15,"__module_gtagMetadataSchema"]],[52,"k","_z"],[52,"l",[30,["d"],[7]]],[52,"m",[8,"enable_code",true]],[52,"n",[8,"email","1","phone_number","2","first_name","3","last_name","4","country","5","postal_code","6","street","7","city","8","region","9"]],[22,[17,[15,"a"],"isAutoEnabled"],[46,[53,[52,"t",[7]],[22,[1,[17,[15,"a"],"autoCollectExclusionSelectors"],[17,[17,[15,"a"],"autoCollectExclusionSelectors"],"length"]],[46,[53,[53,[41,"w"],[3,"w",0],[63,[7,"w"],[23,[15,"w"],[17,[17,[15,"a"],"autoCollectExclusionSelectors"],"length"]],[33,[15,"w"],[3,"w",[0,[15,"w"],1]]],[46,[53,[52,"x",[17,[16,[17,[15,"a"],"autoCollectExclusionSelectors"],[15,"w"]],"exclusionSelector"]],[22,[15,"x"],[46,[53,[2,[15,"t"],"push",[7,[15,"x"]]]]]]]]]]]]],[52,"u",[30,[16,[15,"c"],"enableAutoPhoneAndAddressDetection"],[17,[15,"a"],"isAutoCollectPiiEnabledFlag"]]],[52,"v",[39,[17,[15,"a"],"isAutoCollectPiiEnabledFlag"],[17,[15,"a"],"autoEmailEnabled"],true]],[43,[15,"m"],"auto_detect",[8,"email",[15,"v"],"phone",[1,[15,"u"],[17,[15,"a"],"autoPhoneEnabled"]],"address",[1,[15,"u"],[17,[15,"a"],"autoAddressEnabled"]],"exclude_element_selectors",[15,"t"]]]]]],[22,[17,[15,"a"],"isManualEnabled"],[46,[53,[52,"t",[8]],[22,[17,[15,"a"],"manualEmailEnabled"],[46,[53,["s",[15,"t"],"email","emailValue"]]]],[22,[17,[15,"a"],"manualPhoneEnabled"],[46,[53,["s",[15,"t"],"phone","phoneValue"]]]],[22,[17,[15,"a"],"manualAddressEnabled"],[46,[53,[52,"u",[8]],["s",[15,"u"],"first_name","firstNameValue"],["s",[15,"u"],"last_name","lastNameValue"],["s",[15,"u"],"street","streetValue"],["s",[15,"u"],"city","cityValue"],["s",[15,"u"],"region","regionValue"],["s",[15,"u"],"country","countryValue"],["s",[15,"u"],"postal_code","postalCodeValue"],[43,[15,"t"],"name_and_address",[7,[15,"u"]]]]]],[43,[15,"m"],"selectors",[15,"t"]]]]],[65,"t",[15,"l"],[46,[53,["h",[15,"t"],"user_data_settings",[15,"m"]],[52,"u",[16,[15,"m"],"auto_detect"]],[22,[28,[15,"u"]],[46,[53,[6]]]],[52,"v",[51,"",[7,"w"],[52,"x",[2,[15,"w"],"getMetadata",[7,[17,[15,"j"],"USER_DATA_FROM_AUTOMATIC"]]]],[22,[15,"x"],[46,[53,[36,[15,"x"]]]]],[52,"y",[1,[16,[15,"c"],"enableDataLayerSearchExperiment"],[20,[2,[15,"t"],"indexOf",[7,"G-"]],0]]],[41,"z"],[22,["g","detect_user_provided_data","auto"],[46,[53,[3,"z",["f",[8,"excludeElementSelectors",[16,[15,"u"],"exclude_element_selectors"],"fieldFilters",[8,"email",[16,[15,"u"],"email"],"phone",[16,[15,"u"],"phone"],"address",[16,[15,"u"],"address"]],"performDataLayerSearch",[15,"y"]]]]]]],[52,"ba",[1,[15,"z"],[16,[15,"z"],"elements"]]],[52,"bb",[8]],[22,[1,[15,"ba"],[18,[17,[15,"ba"],"length"],0]],[46,[53,[41,"bc"],[53,[41,"bd"],[3,"bd",0],[63,[7,"bd"],[23,[15,"bd"],[17,[15,"ba"],"length"]],[33,[15,"bd"],[3,"bd",[0,[15,"bd"],1]]],[46,[53,[52,"be",[16,[15,"ba"],[15,"bd"]]],["r",[15,"bb"],[15,"be"],"email"],[22,[16,[15,"c"],"enableAutoPiiOnPhoneAndAddress"],[46,[53,["r",[15,"bb"],[15,"be"],"phone_number"],[3,"bc",["r",[15,"bc"],[15,"be"],"first_name"]],[3,"bc",["r",[15,"bc"],[15,"be"],"last_name"]],[3,"bc",["r",[15,"bc"],[15,"be"],"country"]],[3,"bc",["r",[15,"bc"],[15,"be"],"postal_code"]]]]]]]]],[22,[1,[15,"bc"],[28,[16,[15,"bb"],"address"]]],[46,[53,[43,[15,"bb"],"address",[15,"bc"]]]]]]]],[22,[15,"y"],[46,[53,[52,"bc",[1,[15,"z"],[16,[15,"z"],"dataLayerSearchResults"]]],[22,[15,"bc"],[46,[53,[52,"bd",["o",[15,"bc"],[15,"bb"]]],[22,[15,"bd"],[46,[53,[2,[15,"w"],"setHitData",[7,[15,"k"],[15,"bd"]]]]]]]]]]]],[2,[15,"w"],"setMetadata",[7,[17,[15,"j"],"USER_DATA_FROM_AUTOMATIC"],[15,"bb"]]],[36,[15,"bb"]]]],["i",[15,"t"],[51,"",[7,"w"],[2,[15,"w"],"setMetadata",[7,[17,[15,"j"],"USER_DATA_FROM_AUTOMATIC_GETTER"],[15,"v"]]]]]]]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__set_product_settings",[46,"a"],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[52,"__module_activities",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"b",[46,"c","d"],[36,[39,[15,"d"],["d",[15,"c"]],[15,"c"]]]],[36,[8,"withRequestContext",[15,"b"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_gtagMetadataSchema",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[52,"b","add_tag_timing"],[52,"c","allow_ad_personalization"],[52,"d","batch_on_navigation"],[52,"e","client_id_source"],[52,"f","consent_event_id"],[52,"g","consent_priority_id"],[52,"h","consent_state"],[52,"i","consent_updated"],[52,"j","conversion_linker_enabled"],[52,"k","cookie_options"],[52,"l","create_dc_join"],[52,"m","create_fpm_join"],[52,"n","create_google_join"],[52,"o","em_event"],[52,"p","endpoint_for_debug"],[52,"q","enhanced_client_id_source"],[52,"r","enhanced_match_result"],[52,"s","euid_mode_enabled"],[52,"t","event_start_timestamp_ms"],[52,"u","event_usage"],[52,"v","extra_tag_experiment_ids"],[52,"w","add_parameter"],[52,"x","attribution_reporting_experiment"],[52,"y","counting_method"],[52,"z","send_as_iframe"],[52,"ba","parameter_order"],[52,"bb","parsed_target"],[52,"bc","ga4_collection_subdomain"],[52,"bd","gbraid_cookie_marked"],[52,"be","hit_type"],[52,"bf","hit_type_override"],[52,"bg","is_config_command"],[52,"bh","is_consent_update"],[52,"bi","is_conversion"],[52,"bj","is_ecommerce"],[52,"bk","is_external_event"],[52,"bl","is_fallback_aw_conversion_ping_allowed"],[52,"bm","is_first_visit"],[52,"bn","is_first_visit_conversion"],[52,"bo","is_fl_fallback_conversion_flow_allowed"],[52,"bp","is_fpm_encryption"],[52,"bq","is_fpm_split"],[52,"br","is_gcp_conversion"],[52,"bs","is_google_signals_allowed"],[52,"bt","is_merchant_center"],[52,"bu","is_new_to_site"],[52,"bv","is_server_side_destination"],[52,"bw","is_session_start"],[52,"bx","is_session_start_conversion"],[52,"by","is_sgtm_ga_ads_conversion_study_control_group"],[52,"bz","is_sgtm_prehit"],[52,"ca","is_sgtm_service_worker"],[52,"cb","is_syn"],[52,"cc","join_id"],[52,"cd","join_timer_sec"],[52,"ce","tunnel_updated"],[52,"cf","promises"],[52,"cg","record_aw_latency"],[52,"ch","redact_ads_data"],[52,"ci","redact_click_ids"],[52,"cj","remarketing_only"],[52,"ck","send_ccm_parallel_ping"],[52,"cl","send_fledge_experiment"],[52,"cm","send_ccm_parallel_test_ping"],[52,"cn","send_to_destinations"],[52,"co","send_to_targets"],[52,"cp","send_user_data_hit"],[52,"cq","source_canonical_id"],[52,"cr","speculative"],[52,"cs","speculative_in_message"],[52,"ct","suppress_script_load"],[52,"cu","syn_or_mod"],[52,"cv","transient_ecsid"],[52,"cw","transmission_type"],[52,"cx","user_data"],[52,"cy","user_data_from_automatic"],[52,"cz","user_data_from_automatic_getter"],[52,"da","user_data_from_code"],[52,"db","user_data_from_manual"],[52,"dc","user_data_mode"],[52,"dd","user_id_updated"],[36,[8,"ADD_TAG_TIMING",[15,"b"],"ALLOW_AD_PERSONALIZATION",[15,"c"],"BATCH_ON_NAVIGATION",[15,"d"],"CLIENT_ID_SOURCE",[15,"e"],"CONSENT_EVENT_ID",[15,"f"],"CONSENT_PRIORITY_ID",[15,"g"],"CONSENT_STATE",[15,"h"],"CONSENT_UPDATED",[15,"i"],"CONVERSION_LINKER_ENABLED",[15,"j"],"COOKIE_OPTIONS",[15,"k"],"CREATE_DC_JOIN",[15,"l"],"CREATE_FPM_JOIN",[15,"m"],"CREATE_GOOGLE_JOIN",[15,"n"],"EM_EVENT",[15,"o"],"ENDPOINT_FOR_DEBUG",[15,"p"],"ENHANCED_CLIENT_ID_SOURCE",[15,"q"],"ENHANCED_MATCH_RESULT",[15,"r"],"EUID_MODE_ENABLED",[15,"s"],"EVENT_START_TIMESTAMP_MS",[15,"t"],"EVENT_USAGE",[15,"u"],"EXTRA_TAG_EXPERIMENT_IDS",[15,"v"],"FL_ADD_PARAMETER",[15,"w"],"FL_ATTRIBUTION_REPORTING_EXPERIMENT",[15,"x"],"FL_COUNTING_METHOD",[15,"y"],"FL_ENABLE_DYNAMIC_TAG_FOR_CONSENTED_USERS",[15,"z"],"FL_PARAMETER_ORDER",[15,"ba"],"FL_PARSED_TARGET",[15,"bb"],"GA4_COLLECTION_SUBDOMAIN",[15,"bc"],"GBRAID_COOKIE_MARKED",[15,"bd"],"HIT_TYPE",[15,"be"],"HIT_TYPE_OVERRIDE",[15,"bf"],"IS_CONFIG_COMMAND",[15,"bg"],"IS_CONSENT_UPDATE",[15,"bh"],"IS_CONVERSION",[15,"bi"],"IS_ECOMMERCE",[15,"bj"],"IS_EXTERNAL_EVENT",[15,"bk"],"IS_FALLBACK_AW_CONVERSION_PING_ALLOWED",[15,"bl"],"IS_FIRST_VISIT",[15,"bm"],"IS_FIRST_VISIT_CONVERSION",[15,"bn"],"IS_FL_FALLBACK_CONVERSION_FLOW_ALLOWED",[15,"bo"],"IS_FPM_ENCRYPTION",[15,"bp"],"IS_FPM_SPLIT",[15,"bq"],"IS_GCP_CONVERSION",[15,"br"],"IS_GOOGLE_SIGNALS_ALLOWED",[15,"bs"],"IS_MERCHANT_CENTER",[15,"bt"],"IS_NEW_TO_SITE",[15,"bu"],"IS_SERVER_SIDE_DESTINATION",[15,"bv"],"IS_SESSION_START",[15,"bw"],"IS_SESSION_START_CONVERSION",[15,"bx"],"IS_SGTM_GA_ADS_CONVERSION_STUDY_CONTROL_GROUP",[15,"by"],"IS_SGTM_PREHIT",[15,"bz"],"IS_SGTM_SERVICE_WORKER",[15,"ca"],"IS_SYNTHETIC_EVENT",[15,"cb"],"JOIN_ID",[15,"cc"],"JOIN_TIMER_SEC",[15,"cd"],"POSTMESSAGE_UPDATED",[15,"ce"],"PROMISES",[15,"cf"],"RECORD_AW_LATENCY",[15,"cg"],"REDACT_ADS_DATA",[15,"ch"],"REDACT_CLICK_IDS",[15,"ci"],"REMARKETING_ONLY",[15,"cj"],"SEND_CCM_PARALLEL_PING",[15,"ck"],"SEND_FLEDGE_EXPERIMENT",[15,"cl"],"SEND_FPM_PARALLEL_TEST_PING",[15,"cm"],"SEND_TO_DESTINATIONS",[15,"cn"],"SEND_TO_TARGETS",[15,"co"],"SEND_USER_DATA_HIT",[15,"cp"],"SOURCE_CANONICAL_ID",[15,"cq"],"SPECULATIVE",[15,"cr"],"SPECULATIVE_IN_MESSAGE",[15,"cs"],"SUPPRESS_SCRIPT_LOAD",[15,"ct"],"SYNTHETIC_OR_MODIFIED_EVENT",[15,"cu"],"TRANSIENT_ECSID",[15,"cv"],"TRANSMISSION_TYPE",[15,"cw"],"USER_DATA",[15,"cx"],"USER_DATA_FROM_AUTOMATIC",[15,"cy"],"USER_DATA_FROM_AUTOMATIC_GETTER",[15,"cz"],"USER_DATA_FROM_CODE",[15,"da"],"USER_DATA_FROM_MANUAL",[15,"db"],"USER_DATA_MODE",[15,"dc"],"USER_ID_UPDATED",[15,"dd"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdEmDownloadActivity",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"h",[46,"i","j"],["c",[15,"i"],[51,"",[7,"k"],[22,[30,[21,[2,[15,"k"],"getEventName",[7]],[15,"f"]],[28,[2,[15,"k"],"getMetadata",[7,[15,"g"]]]]],[46,[53,[36]]]],[22,["b",[15,"i"],[15,"e"]],[46,[53,[2,[15,"k"],"abort",[7]],[36]]]],[2,[15,"k"],"setMetadata",[7,[15,"d"],false]],[22,[28,[15,"j"]],[46,[53,[2,[15,"k"],"setHitData",[7,"link_id",[44]]],[2,[15,"k"],"setHitData",[7,"link_url",[44]]],[2,[15,"k"],"setHitData",[7,"link_text",[44]]],[2,[15,"k"],"setHitData",[7,"file_name",[44]]],[2,[15,"k"],"setHitData",[7,"file_extension",[44]]]]]]]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","internal.registerCcdCallback"]],[52,"d","speculative"],[52,"e","ae_block_downloads"],[52,"f","file_download"],[52,"g","em_event"],[36,[8,"registerDownloadActivityCallback",[15,"h"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdEmFormActivity",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"l",[46,"m","n","o"],[22,[1,[15,"k"],[20,[15,"n"],[44]]],[46,[53,[3,"n",[20,[2,[15,"m"],"indexOf",[7,"AW-"]],0]]]]],["d",[15,"m"],[51,"",[7,"p"],[52,"q",[2,[15,"p"],"getEventName",[7]]],[52,"r",[30,[20,[15,"q"],[15,"h"]],[20,[15,"q"],[15,"g"]]]],[22,[30,[28,[15,"r"]],[28,[2,[15,"p"],"getMetadata",[7,[15,"i"]]]]],[46,[53,[36]]]],[22,["c",[15,"m"],[15,"f"]],[46,[53,[2,[15,"p"],"abort",[7]],[36]]]],[22,[15,"k"],[46,[53,[22,[1,[28,[15,"n"]],[2,[15,"p"],"getMetadata",[7,[15,"j"]]]],[46,[53,[2,[15,"p"],"abort",[7]],[36]]]]]]],[2,[15,"p"],"setMetadata",[7,[15,"e"],false]],[22,[28,[15,"o"]],[46,[53,[2,[15,"p"],"setHitData",[7,"form_id",[44]]],[2,[15,"p"],"setHitData",[7,"form_name",[44]]],[2,[15,"p"],"setHitData",[7,"form_destination",[44]]],[2,[15,"p"],"setHitData",[7,"form_length",[44]]],[22,[20,[15,"q"],[15,"g"]],[46,[53,[2,[15,"p"],"setHitData",[7,"form_submit_text",[44]]]]],[46,[22,[20,[15,"q"],[15,"h"]],[46,[53,[2,[15,"p"],"setHitData",[7,"first_field_id",[44]]],[2,[15,"p"],"setHitData",[7,"first_field_name",[44]]],[2,[15,"p"],"setHitData",[7,"first_field_type",[44]]],[2,[15,"p"],"setHitData",[7,"first_field_position",[44]]]]]]]]]]]]]],[52,"b",[13,[41,"$0"],[3,"$0",["require","internal.getFlags"]],["$0"]]],[52,"c",["require","internal.getProductSettingsParameter"]],[52,"d",["require","internal.registerCcdCallback"]],[52,"e","speculative"],[52,"f","ae_block_form"],[52,"g","form_submit"],[52,"h","form_start"],[52,"i","em_event"],[52,"j","form_event_canceled"],[52,"k",[28,[28,[16,[15,"b"],"enableFormSkipValidation"]]]],[36,[8,"registerFormActivityCallback",[15,"l"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdEmOutboundClickActivity",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"h",[46,"i","j"],["c",[15,"i"],[51,"",[7,"k"],[22,[30,[21,[2,[15,"k"],"getEventName",[7]],[15,"f"]],[28,[2,[15,"k"],"getMetadata",[7,[15,"g"]]]]],[46,[53,[36]]]],[22,["b",[15,"i"],[15,"e"]],[46,[53,[2,[15,"k"],"abort",[7]],[36]]]],[2,[15,"k"],"setMetadata",[7,[15,"d"],false]],[22,[28,[15,"j"]],[46,[53,[2,[15,"k"],"setHitData",[7,"link_id",[44]]],[2,[15,"k"],"setHitData",[7,"link_classes",[44]]],[2,[15,"k"],"setHitData",[7,"link_url",[44]]],[2,[15,"k"],"setHitData",[7,"link_domain",[44]]],[2,[15,"k"],"setHitData",[7,"outbound",[44]]]]]]]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","internal.registerCcdCallback"]],[52,"d","speculative"],[52,"e","ae_block_outbound_click"],[52,"f","click"],[52,"g","em_event"],[36,[8,"registerOutbackClickActivityCallback",[15,"h"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdEmSiteSearchActivity",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"b",[46,"d","e"],[52,"f",[2,[30,[15,"d"],""],"split",[7,","]]],[53,[41,"g"],[3,"g",0],[63,[7,"g"],[23,[15,"g"],[17,[15,"f"],"length"]],[33,[15,"g"],[3,"g",[0,[15,"g"],1]]],[46,[53,[52,"h",["e",[2,[16,[15,"f"],[15,"g"]],"trim",[7]]]],[22,[21,[15,"h"],[44]],[46,[53,[36,[15,"h"]]]]]]]]]],[50,"c",[46,"d","e","f"],[52,"g",[8,"search_term",[15,"d"]]],[52,"h",[2,[30,[15,"e"],""],"split",[7,","]]],[53,[41,"i"],[3,"i",0],[63,[7,"i"],[23,[15,"i"],[17,[15,"h"],"length"]],[33,[15,"i"],[3,"i",[0,[15,"i"],1]]],[46,[53,[52,"j",[2,[16,[15,"h"],[15,"i"]],"trim",[7]]],[52,"k",["f",[15,"j"]]],[22,[21,[15,"k"],[44]],[46,[53,[43,[15,"g"],[0,"q_",[15,"j"]],[15,"k"]]]]]]]]],[36,[15,"g"]]],[36,[8,"getSearchTerm",[15,"b"],"buildEventParams",[15,"c"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdEmScrollActivity",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"h",[46,"i","j"],["c",[15,"i"],[51,"",[7,"k"],[22,[30,[21,[2,[15,"k"],"getEventName",[7]],[15,"f"]],[28,[2,[15,"k"],"getMetadata",[7,[15,"g"]]]]],[46,[53,[36]]]],[22,["b",[15,"i"],[15,"e"]],[46,[53,[2,[15,"k"],"abort",[7]],[36]]]],[2,[15,"k"],"setMetadata",[7,[15,"d"],false]],[22,[28,[15,"j"]],[46,[53,[2,[15,"k"],"setHitData",[7,"percent_scrolled",[44]]]]]]]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","internal.registerCcdCallback"]],[52,"d","speculative"],[52,"e","ae_block_scroll"],[52,"f","scroll"],[52,"g","em_event"],[36,[8,"registerScrollActivityCallback",[15,"h"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdEmVideoActivity",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"j",[46,"k","l"],["c",[15,"k"],[51,"",[7,"m"],[52,"n",[2,[15,"m"],"getEventName",[7]]],[52,"o",[30,[30,[20,[15,"n"],[15,"f"]],[20,[15,"n"],[15,"g"]]],[20,[15,"n"],[15,"h"]]]],[22,[30,[28,[15,"o"]],[28,[2,[15,"m"],"getMetadata",[7,[15,"i"]]]]],[46,[53,[36]]]],[22,["b",[15,"k"],[15,"e"]],[46,[53,[2,[15,"m"],"abort",[7]],[36]]]],[2,[15,"m"],"setMetadata",[7,[15,"d"],false]],[22,[28,[15,"l"]],[46,[53,[2,[15,"m"],"setHitData",[7,"video_current_time",[44]]],[2,[15,"m"],"setHitData",[7,"video_duration",[44]]],[2,[15,"m"],"setHitData",[7,"video_percent",[44]]],[2,[15,"m"],"setHitData",[7,"video_provider",[44]]],[2,[15,"m"],"setHitData",[7,"video_title",[44]]],[2,[15,"m"],"setHitData",[7,"video_url",[44]]],[2,[15,"m"],"setHitData",[7,"visible",[44]]]]]]]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","internal.registerCcdCallback"]],[52,"d","speculative"],[52,"e","ae_block_video"],[52,"f","video_start"],[52,"g","video_progress"],[52,"h","video_complete"],[52,"i","em_event"],[36,[8,"registerVideoActivityCallback",[15,"j"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdEmPageViewActivity",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"h",[46,"i"],["c",[15,"i"],[51,"",[7,"j"],[22,[30,[21,[2,[15,"j"],"getEventName",[7]],[15,"g"]],[28,[2,[15,"j"],"getMetadata",[7,[17,[15,"e"],"EM_EVENT"]]]]],[46,[53,[36]]]],[22,["b",[15,"i"],[15,"f"]],[46,[53,[2,[15,"j"],"abort",[7]],[36]]]],[22,[28,[2,[15,"j"],"getMetadata",[7,[17,[15,"e"],"IS_SGTM_PREHIT"]]]],[46,[53,["d",[15,"i"],"page_referrer",[2,[15,"j"],"getHitData",[7,"page_referrer"]]]]]],[2,[15,"j"],"setMetadata",[7,[17,[15,"e"],"SPECULATIVE"],false]]]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","internal.registerCcdCallback"]],[52,"d",["require","internal.setRemoteConfigParameter"]],[52,"e",[15,"__module_gtagMetadataSchema"]],[52,"f","ae_block_history"],[52,"g","page_view"],[36,[8,"registerPageViewActivityCallback",[15,"h"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdGaRegionScopedSettings",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"n",[46,"q","r","s"],[50,"x",[46,"z"],[52,"ba",[16,[15,"m"],[15,"z"]]],[22,[28,[15,"ba"]],[46,[36]]],[53,[41,"bb"],[3,"bb",0],[63,[7,"bb"],[23,[15,"bb"],[17,[15,"ba"],"length"]],[33,[15,"bb"],[3,"bb",[0,[15,"bb"],1]]],[46,[53,[52,"bc",[16,[15,"ba"],[15,"bb"]]],["u",[15,"t"],[17,[15,"bc"],"name"],[17,[15,"bc"],"value"]]]]]]],[50,"y",[46,"z"],[22,[30,[28,[15,"v"]],[21,[17,[15,"v"],"length"],2]],[46,[53,[36,false]]]],[41,"ba"],[3,"ba",[16,[15,"z"],[15,"w"]]],[22,[20,[15,"ba"],[44]],[46,[53,[3,"ba",[16,[15,"z"],[15,"v"]]]]]],[36,[28,[28,[15,"ba"]]]]],[22,[28,[15,"r"]],[46,[36]]],[52,"t",[30,[17,[15,"q"],"instanceDestinationId"],[17,["d"],"containerId"]]],[52,"u",["i",[15,"g"],[15,"s"]]],[52,"v",[13,[41,"$0"],[3,"$0",["i",[15,"e"],[15,"s"]]],["$0"]]],[52,"w",[13,[41,"$0"],[3,"$0",["i",[15,"f"],[15,"s"]]],["$0"]]],[53,[41,"z"],[3,"z",0],[63,[7,"z"],[23,[15,"z"],[17,[15,"r"],"length"]],[33,[15,"z"],[3,"z",[0,[15,"z"],1]]],[46,[53,[52,"ba",[16,[15,"r"],[15,"z"]]],[22,[30,[17,[15,"ba"],"disallowAllRegions"],["y",[17,[15,"ba"],"disallowedRegions"]]],[46,[53,["x",[17,[15,"ba"],"redactFieldGroup"]]]]]]]]]],[50,"o",[46,"q"],[52,"r",[8]],[22,[28,[15,"q"]],[46,[36,[15,"r"]]]],[52,"s",[2,[15,"q"],"split",[7,","]]],[53,[41,"t"],[3,"t",0],[63,[7,"t"],[23,[15,"t"],[17,[15,"s"],"length"]],[33,[15,"t"],[3,"t",[0,[15,"t"],1]]],[46,[53,[52,"u",[2,[16,[15,"s"],[15,"t"]],"trim",[7]]],[22,[28,[15,"u"]],[46,[6]]],[52,"v",[2,[15,"u"],"split",[7,"-"]]],[52,"w",[16,[15,"v"],0]],[52,"x",[39,[20,[17,[15,"v"],"length"],2],[15,"u"],[44]]],[22,[30,[28,[15,"w"]],[21,[17,[15,"w"],"length"],2]],[46,[53,[6]]]],[22,[1,[21,[15,"x"],[44]],[30,[23,[17,[15,"x"],"length"],4],[18,[17,[15,"x"],"length"],6]]],[46,[53,[6]]]],[43,[15,"r"],[15,"u"],true]]]]],[36,[15,"r"]]],[50,"p",[46,"q"],[22,[28,[17,[15,"q"],"settingsTable"]],[46,[36,[7]]]],[52,"r",[8]],[53,[41,"s"],[3,"s",0],[63,[7,"s"],[23,[15,"s"],[17,[17,[15,"q"],"settingsTable"],"length"]],[33,[15,"s"],[3,"s",[0,[15,"s"],1]]],[46,[53,[52,"t",[16,[17,[15,"q"],"settingsTable"],[15,"s"]]],[52,"u",[17,[15,"t"],"redactFieldGroup"]],[22,[28,[16,[15,"m"],[15,"u"]]],[46,[6]]],[43,[15,"r"],[15,"u"],[8,"redactFieldGroup",[15,"u"],"disallowAllRegions",false,"disallowedRegions",[8]]],[52,"v",[16,[15,"r"],[15,"u"]]],[22,[17,[15,"t"],"disallowAllRegions"],[46,[53,[43,[15,"v"],"disallowAllRegions",true],[6]]]],[43,[15,"v"],"disallowedRegions",["o",[17,[15,"t"],"disallowedRegions"]]]]]]],[36,[2,[15,"b"],"values",[7,[15,"r"]]]]],[52,"b",["require","Object"]],[52,"c",[13,[41,"$0"],[3,"$0",["require","internal.getFlags"]],["$0"]]],[52,"d",["require","getContainerVersion"]],[52,"e",["require","internal.getCountryCode"]],[52,"f",["require","internal.getRegionCode"]],[52,"g",["require","internal.setRemoteConfigParameter"]],[52,"h",[15,"__module_activities"]],[52,"i",[17,[15,"h"],"withRequestContext"]],[41,"j"],[41,"k"],[41,"l"],[52,"m",[8,"GOOGLE_SIGNALS",[7,[8,"name","allow_google_signals","value",false]],"DEVICE_AND_GEO",[7,[8,"name","geo_granularity","value",true],[8,"name","redact_device_info","value",true]]]],[36,[8,"applyRegionScopedSettings",[15,"n"],"extractRedactedLocations",[15,"p"]]]],[36,["a"]]]],["$0"]]]
 
]
,"entities":{
"__c":{"2":true,"4":true}
,
"__ccd_conversion_marking":{"2":true,"4":true}
,
"__ccd_em_download":{"2":true,"4":true}
,
"__ccd_em_form":{"2":true,"4":true}
,
"__ccd_em_outbound_click":{"2":true,"4":true}
,
"__ccd_em_page_view":{"2":true,"4":true}
,
"__ccd_em_scroll":{"2":true,"4":true}
,
"__ccd_em_site_search":{"2":true,"4":true}
,
"__ccd_em_video":{"2":true,"4":true}
,
"__ccd_ga_first":{"2":true,"4":true}
,
"__ccd_ga_last":{"2":true,"4":true}
,
"__ccd_ga_regscope":{"2":true,"4":true}
,
"__e":{"2":true,"4":true}
,
"__ogt_1p_data_v2":{"2":true}
,
"__set_product_settings":{"2":true,"4":true}


}
,"blob":{"1":"1"}
,"permissions":{
"__c":{}
,
"__ccd_conversion_marking":{}
,
"__ccd_em_download":{"listen_data_layer":{"accessType":"specific","allowedEvents":["gtm.linkClick"]},"access_template_storage":{},"detect_link_click_events":{"allowWaitForTags":""}}
,
"__ccd_em_form":{"access_template_storage":{},"listen_data_layer":{"accessType":"specific","allowedEvents":["gtm.formInteract","gtm.formSubmit"]},"detect_form_submit_events":{"allowWaitForTags":""},"detect_form_interaction_events":{}}
,
"__ccd_em_outbound_click":{"get_url":{"urlParts":"any","queriesAllowed":"any"},"listen_data_layer":{"accessType":"specific","allowedEvents":["gtm.linkClick"]},"access_template_storage":{},"detect_link_click_events":{"allowWaitForTags":""}}
,
"__ccd_em_page_view":{"listen_data_layer":{"accessType":"specific","allowedEvents":["gtm.historyChange-v2"]},"access_template_storage":{},"detect_history_change_events":{}}
,
"__ccd_em_scroll":{"listen_data_layer":{"accessType":"specific","allowedEvents":["gtm.scrollDepth"]},"access_template_storage":{},"detect_scroll_events":{}}
,
"__ccd_em_site_search":{"get_url":{"urlParts":"any","queriesAllowed":"any"},"read_container_data":{}}
,
"__ccd_em_video":{"listen_data_layer":{"accessType":"specific","allowedEvents":["gtm.video"]},"access_template_storage":{},"detect_youtube_activity_events":{"allowFixMissingJavaScriptApi":false}}
,
"__ccd_ga_first":{}
,
"__ccd_ga_last":{}
,
"__ccd_ga_regscope":{"read_container_data":{}}
,
"__e":{"read_event_data":{"eventDataAccess":"specific","keyPatterns":["event"]}}
,
"__ogt_1p_data_v2":{"detect_user_provided_data":{"limitDataSources":true,"allowAutoDataSources":true,"allowManualDataSources":false,"allowCodeDataSources":false}}
,
"__set_product_settings":{}


}



,"security_groups":{
"google":[
"__c"
,
"__ccd_conversion_marking"
,
"__ccd_em_download"
,
"__ccd_em_form"
,
"__ccd_em_outbound_click"
,
"__ccd_em_page_view"
,
"__ccd_em_scroll"
,
"__ccd_em_site_search"
,
"__ccd_em_video"
,
"__ccd_ga_first"
,
"__ccd_ga_last"
,
"__ccd_ga_regscope"
,
"__e"
,
"__ogt_1p_data_v2"
,
"__set_product_settings"

]


}



};




var h,aa=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}},ca=typeof Object.defineProperties=="function"?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a},da=function(a){for(var b=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global],c=0;c<b.length;++c){var d=b[c];if(d&&d.Math==Math)return d}throw Error("Cannot find global object");
},ea=da(this),fa=function(a,b){if(b)a:{for(var c=ea,d=a.split("."),e=0;e<d.length-1;e++){var f=d[e];if(!(f in c))break a;c=c[f]}var g=d[d.length-1],k=c[g],m=b(k);m!=k&&m!=null&&ca(c,g,{configurable:!0,writable:!0,value:m})}};
fa("Symbol",function(a){if(a)return a;var b=function(f,g){this.C=f;ca(this,"description",{configurable:!0,writable:!0,value:g})};b.prototype.toString=function(){return this.C};var c="jscomp_symbol_"+(Math.random()*1E9>>>0)+"_",d=0,e=function(f){if(this instanceof e)throw new TypeError("Symbol is not a constructor");return new b(c+(f||"")+"_"+d++,f)};return e});var ja=typeof Object.create=="function"?Object.create:function(a){var b=function(){};b.prototype=a;return new b},ka;
if(typeof Object.setPrototypeOf=="function")ka=Object.setPrototypeOf;else{var na;a:{var oa={a:!0},pa={};try{pa.__proto__=oa;na=pa.a;break a}catch(a){}na=!1}ka=na?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}
var qa=ka,ra=function(a,b){a.prototype=ja(b.prototype);a.prototype.constructor=a;if(qa)qa(a,b);else for(var c in b)if(c!="prototype")if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.Oo=b.prototype},l=function(a){var b=typeof Symbol!="undefined"&&Symbol.iterator&&a[Symbol.iterator];if(b)return b.call(a);if(typeof a.length=="number")return{next:aa(a)};throw Error(String(a)+" is not an iterable or ArrayLike");},sa=function(a){for(var b,
c=[];!(b=a.next()).done;)c.push(b.value);return c},ta=function(a){return a instanceof Array?a:sa(l(a))},va=function(a){return ua(a,a)},ua=function(a,b){a.raw=b;Object.freeze&&(Object.freeze(a),Object.freeze(b));return a},wa=typeof Object.assign=="function"?Object.assign:function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(d)for(var e in d)Object.prototype.hasOwnProperty.call(d,e)&&(a[e]=d[e])}return a};fa("Object.assign",function(a){return a||wa});
var xa=function(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b};/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var ya=this||self;var za=function(a,b){this.type=a;this.data=b};var Aa=function(){this.map={};this.C={}};Aa.prototype.get=function(a){return this.map["dust."+a]};Aa.prototype.set=function(a,b){var c="dust."+a;this.C.hasOwnProperty(c)||(this.map[c]=b)};Aa.prototype.has=function(a){return this.map.hasOwnProperty("dust."+a)};Aa.prototype.remove=function(a){var b="dust."+a;this.C.hasOwnProperty(b)||delete this.map[b]};
var Ba=function(a,b){var c=[],d;for(d in a.map)if(a.map.hasOwnProperty(d)){var e=d.substring(5);switch(b){case 1:c.push(e);break;case 2:c.push(a.map[d]);break;case 3:c.push([e,a.map[d]])}}return c};Aa.prototype.na=function(){return Ba(this,1)};Aa.prototype.ic=function(){return Ba(this,2)};Aa.prototype.Mb=function(){return Ba(this,3)};var Ca=function(){};Ca.prototype.reset=function(){};var Da=function(a,b){this.O=a;this.parent=b;this.C=this.H=void 0;this.Ec=!1;this.N=function(c,d,e){return c.apply(d,e)};this.values=new Aa};Da.prototype.add=function(a,b){Fa(this,a,b,!1)};var Fa=function(a,b,c,d){if(!a.Ec)if(d){var e=a.values;e.set(b,c);e.C["dust."+b]=!0}else a.values.set(b,c)};Da.prototype.set=function(a,b){this.Ec||(!this.values.has(a)&&this.parent&&this.parent.has(a)?this.parent.set(a,b):this.values.set(a,b))};
Da.prototype.get=function(a){return this.values.has(a)?this.values.get(a):this.parent?this.parent.get(a):void 0};Da.prototype.has=function(a){return!!this.values.has(a)||!(!this.parent||!this.parent.has(a))};var Ga=function(a){var b=new Da(a.O,a);a.H&&(b.H=a.H);b.N=a.N;b.C=a.C;return b};Da.prototype.Sd=function(){return this.O};Da.prototype.Ma=function(){this.Ec=!0};var Ia=function(a,b,c){var d;d=Error.call(this,a.message);this.message=d.message;"stack"in d&&(this.stack=d.stack);this.Mk=a;this.zk=c===void 0?!1:c;this.debugInfo=[];this.C=b};ra(Ia,Error);var Ja=function(a){return a instanceof Ia?a:new Ia(a,void 0,!0)};function Ka(a,b){for(var c,d=l(b),e=d.next();!e.done&&!(c=La(a,e.value),c instanceof za);e=d.next());return c}function La(a,b){try{var c=l(b),d=c.next().value,e=sa(c),f=a.get(String(d));if(!f||typeof f.invoke!=="function")throw Ja(Error("Attempting to execute non-function "+b[0]+"."));return f.invoke.apply(f,[a].concat(ta(e)))}catch(k){var g=a.H;g&&g(k,b.context?{id:b[0],line:b.context.line}:null);throw k;}};var Ma=function(){this.H=new Ca;this.C=new Da(this.H)};h=Ma.prototype;h.Sd=function(){return this.H};h.execute=function(a){return this.Mi([a].concat(ta(xa.apply(1,arguments))))};h.Mi=function(){for(var a,b=l(xa.apply(0,arguments)),c=b.next();!c.done;c=b.next())a=La(this.C,c.value);return a};h.Tl=function(a){var b=xa.apply(1,arguments),c=Ga(this.C);c.C=a;for(var d,e=l(b),f=e.next();!f.done;f=e.next())d=La(c,f.value);return d};h.Ma=function(){this.C.Ma()};var Na=function(){this.wa=!1;this.V=new Aa};h=Na.prototype;h.get=function(a){return this.V.get(a)};h.set=function(a,b){this.wa||this.V.set(a,b)};h.has=function(a){return this.V.has(a)};h.remove=function(a){this.wa||this.V.remove(a)};h.na=function(){return this.V.na()};h.ic=function(){return this.V.ic()};h.Mb=function(){return this.V.Mb()};h.Ma=function(){this.wa=!0};h.Ec=function(){return this.wa};function Oa(){for(var a=Pa,b={},c=0;c<a.length;++c)b[a[c]]=c;return b}function Qa(){var a="ABCDEFGHIJKLMNOPQRSTUVWXYZ";a+=a.toLowerCase()+"0123456789-_";return a+"."}var Pa,Ra;function Ua(a){Pa=Pa||Qa();Ra=Ra||Oa();for(var b=[],c=0;c<a.length;c+=3){var d=c+1<a.length,e=c+2<a.length,f=a.charCodeAt(c),g=d?a.charCodeAt(c+1):0,k=e?a.charCodeAt(c+2):0,m=f>>2,n=(f&3)<<4|g>>4,p=(g&15)<<2|k>>6,q=k&63;e||(q=64,d||(p=64));b.push(Pa[m],Pa[n],Pa[p],Pa[q])}return b.join("")}
function Va(a){function b(m){for(;d<a.length;){var n=a.charAt(d++),p=Ra[n];if(p!=null)return p;if(!/^[\s\xa0]*$/.test(n))throw Error("Unknown base64 encoding at char: "+n);}return m}Pa=Pa||Qa();Ra=Ra||Oa();for(var c="",d=0;;){var e=b(-1),f=b(0),g=b(64),k=b(64);if(k===64&&e===-1)return c;c+=String.fromCharCode(e<<2|f>>4);g!==64&&(c+=String.fromCharCode(f<<4&240|g>>2),k!==64&&(c+=String.fromCharCode(g<<6&192|k)))}};var Wa={};function Xa(a,b){Wa[a]=Wa[a]||[];Wa[a][b]=!0}function Ya(a){var b=Wa[a];if(!b||b.length===0)return"";for(var c=[],d=0,e=0;e<b.length;e++)e%8===0&&e>0&&(c.push(String.fromCharCode(d)),d=0),b[e]&&(d|=1<<e%8);d>0&&c.push(String.fromCharCode(d));return Ua(c.join("")).replace(/\.+$/,"")}function Za(){for(var a=[],b=Wa.fdr||[],c=0;c<b.length;c++)b[c]&&a.push(c);return a.length>0?a:void 0};function $a(){}function ab(a){return typeof a==="function"}function bb(a){return typeof a==="string"}function db(a){return typeof a==="number"&&!isNaN(a)}function eb(a){return Array.isArray(a)?a:[a]}function fb(a,b){if(a&&Array.isArray(a))for(var c=0;c<a.length;c++)if(a[c]&&b(a[c]))return a[c]}function gb(a,b){if(!db(a)||!db(b)||a>b)a=0,b=2147483647;return Math.floor(Math.random()*(b-a+1)+a)}
function hb(a,b){for(var c=new ib,d=0;d<a.length;d++)c.set(a[d],!0);for(var e=0;e<b.length;e++)if(c.get(b[e]))return!0;return!1}function jb(a,b){for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(c,a[c])}function kb(a){return!!a&&(Object.prototype.toString.call(a)==="[object Arguments]"||Object.prototype.hasOwnProperty.call(a,"callee"))}function lb(a){return Math.round(Number(a))||0}function mb(a){return"false"===String(a).toLowerCase()?!1:!!a}
function nb(a){var b=[];if(Array.isArray(a))for(var c=0;c<a.length;c++)b.push(String(a[c]));return b}function ob(a){return a?a.replace(/^\s+|\s+$/g,""):""}function pb(){return new Date(Date.now())}function qb(){return pb().getTime()}var ib=function(){this.prefix="gtm.";this.values={}};ib.prototype.set=function(a,b){this.values[this.prefix+a]=b};ib.prototype.get=function(a){return this.values[this.prefix+a]};ib.prototype.contains=function(a){return this.get(a)!==void 0};
function rb(a,b,c){return a&&a.hasOwnProperty(b)?a[b]:c}function sb(a){var b=a;return function(){if(b){var c=b;b=void 0;try{c()}catch(d){}}}}function tb(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])}function ub(a,b){for(var c=[],d=0;d<a.length;d++)c.push(a[d]),c.push.apply(c,b[a[d]]||[]);return c}function vb(a,b){return a.length>=b.length&&a.substring(0,b.length)===b}
function wb(a,b){var c=z;b=b||[];for(var d=c,e=0;e<a.length-1;e++){if(!d.hasOwnProperty(a[e]))return;d=d[a[e]];if(b.indexOf(d)>=0)return}return d}function xb(a,b){for(var c={},d=c,e=a.split("."),f=0;f<e.length-1;f++)d=d[e[f]]={};d[e[e.length-1]]=b;return c}var yb=/^\w{1,9}$/;function zb(a,b){a=a||{};b=b||",";var c=[];jb(a,function(d,e){yb.test(d)&&e&&c.push(d)});return c.join(b)}function Ab(a,b){function c(){e&&++d===b&&(e(),e=null,c.done=!0)}var d=0,e=a;c.done=!1;return c}
function Bb(a){if(!a)return a;var b=a;try{b=decodeURIComponent(a)}catch(d){}var c=b.split(",");return c.length===2&&c[0]===c[1]?c[0]:a}
function Cb(a,b,c){function d(n){var p=n.split("=")[0];if(a.indexOf(p)<0)return n;if(c!==void 0)return p+"="+c}function e(n){return n.split("&").map(d).filter(function(p){return p!==void 0}).join("&")}var f=b.href.split(/[?#]/)[0],g=b.search,k=b.hash;g[0]==="?"&&(g=g.substring(1));k[0]==="#"&&(k=k.substring(1));g=e(g);k=e(k);g!==""&&(g="?"+g);k!==""&&(k="#"+k);var m=""+f+g+k;m[m.length-1]==="/"&&(m=m.substring(0,m.length-1));return m}
function Db(a){for(var b=0;b<3;++b)try{var c=decodeURIComponent(a).replace(/\+/g," ");if(c===a)break;a=c}catch(d){return""}return a};/*

 Copyright Google LLC
 SPDX-License-Identifier: Apache-2.0
*/
var Eb=globalThis.trustedTypes,Fb;function Gb(){var a=null;if(!Eb)return a;try{var b=function(c){return c};a=Eb.createPolicy("goog#html",{createHTML:b,createScript:b,createScriptURL:b})}catch(c){}return a}function Hb(){Fb===void 0&&(Fb=Gb());return Fb};var Ib=function(a){this.C=a};Ib.prototype.toString=function(){return this.C+""};function Jb(a){var b=a,c=Hb(),d=c?c.createScriptURL(b):b;return new Ib(d)}function Kb(a){if(a instanceof Ib)return a.C;throw Error("");};var Mb=va([""]),Nb=ua(["\x00"],["\\0"]),Ob=ua(["\n"],["\\n"]),Pb=ua(["\x00"],["\\u0000"]);function Qb(a){return a.toString().indexOf("`")===-1}Qb(function(a){return a(Mb)})||Qb(function(a){return a(Nb)})||Qb(function(a){return a(Ob)})||Qb(function(a){return a(Pb)});var Rb=function(a){this.C=a};Rb.prototype.toString=function(){return this.C};var Sb=function(a){this.un=a};function Tb(a){return new Sb(function(b){return b.substr(0,a.length+1).toLowerCase()===a+":"})}var Ub=[Tb("data"),Tb("http"),Tb("https"),Tb("mailto"),Tb("ftp"),new Sb(function(a){return/^[^:]*([/?#]|$)/.test(a)})];function Vb(a){var b;b=b===void 0?Ub:b;if(a instanceof Rb)return a;for(var c=0;c<b.length;++c){var d=b[c];if(d instanceof Sb&&d.un(a))return new Rb(a)}}var Wb=/^\s*(?!javascript:)(?:[\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i;
function Xb(a){var b;if(a instanceof Rb)if(a instanceof Rb)b=a.C;else throw Error("");else b=Wb.test(a)?a:void 0;return b};function Yb(a,b){var c=Xb(b);c!==void 0&&(a.action=c)};function Zb(a,b){throw Error(b===void 0?"unexpected value "+a+"!":b);};var $b=function(a){this.C=a};$b.prototype.toString=function(){return this.C+""};var bc=function(){this.C=ac[0].toLowerCase()};bc.prototype.toString=function(){return this.C};function cc(a,b){var c=[new bc];if(c.length===0)throw Error("");var d=c.map(function(f){var g;if(f instanceof bc)g=f.C;else throw Error("");return g}),e=b.toLowerCase();if(d.every(function(f){return e.indexOf(f)!==0}))throw Error('Attribute "'+b+'" does not match any of the allowed prefixes.');a.setAttribute(b,"true")};var dc=Array.prototype.indexOf?function(a,b){return Array.prototype.indexOf.call(a,b,void 0)}:function(a,b){if(typeof a==="string")return typeof b!=="string"||b.length!=1?-1:a.indexOf(b,0);for(var c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1};"ARTICLE SECTION NAV ASIDE H1 H2 H3 H4 H5 H6 HEADER FOOTER ADDRESS P HR PRE BLOCKQUOTE OL UL LH LI DL DT DD FIGURE FIGCAPTION MAIN DIV EM STRONG SMALL S CITE Q DFN ABBR RUBY RB RT RTC RP DATA TIME CODE VAR SAMP KBD SUB SUP I B U MARK BDI BDO SPAN BR WBR NOBR INS DEL PICTURE PARAM TRACK MAP TABLE CAPTION COLGROUP COL TBODY THEAD TFOOT TR TD TH SELECT DATALIST OPTGROUP OPTION OUTPUT PROGRESS METER FIELDSET LEGEND DETAILS SUMMARY MENU DIALOG SLOT CANVAS FONT CENTER ACRONYM BASEFONT BIG DIR HGROUP STRIKE TT".split(" ").concat(["BUTTON",
"INPUT"]);function ec(a){return a===null?"null":a===void 0?"undefined":a};var z=window,fc=window.history,A=document,gc=navigator;function hc(){var a;try{a=gc.serviceWorker}catch(b){return}return a}var ic=A.currentScript,jc=ic&&ic.src;function kc(a,b){var c=z[a];z[a]=c===void 0?b:c;return z[a]}function lc(a){return(gc.userAgent||"").indexOf(a)!==-1}function mc(){return lc("Firefox")||lc("FxiOS")}function nc(){return(lc("GSA")||lc("GoogleApp"))&&(lc("iPhone")||lc("iPad"))}function oc(){return lc("Edg/")||lc("EdgA/")||lc("EdgiOS/")}
var pc={async:1,nonce:1,onerror:1,onload:1,src:1,type:1},qc={onload:1,src:1,width:1,height:1,style:1};function rc(a,b,c){b&&jb(b,function(d,e){d=d.toLowerCase();c.hasOwnProperty(d)||a.setAttribute(d,e)})}
function sc(a,b,c,d,e){var f=A.createElement("script");rc(f,d,pc);f.type="text/javascript";f.async=d&&d.async===!1?!1:!0;var g;g=Jb(ec(a));f.src=Kb(g);var k,m=f.ownerDocument;m=m===void 0?document:m;var n,p,q=(p=(n=m).querySelector)==null?void 0:p.call(n,"script[nonce]");(k=q==null?"":q.nonce||q.getAttribute("nonce")||"")&&f.setAttribute("nonce",k);b&&(f.onload=b);c&&(f.onerror=c);if(e)e.appendChild(f);else{var r=A.getElementsByTagName("script")[0]||A.body||A.head;r.parentNode.insertBefore(f,r)}return f}
function tc(){if(jc){var a=jc.toLowerCase();if(a.indexOf("https://")===0)return 2;if(a.indexOf("http://")===0)return 3}return 1}function uc(a,b,c,d,e,f){f=f===void 0?!0:f;var g=e,k=!1;g||(g=A.createElement("iframe"),k=!0);rc(g,c,qc);d&&jb(d,function(n,p){g.dataset[n]=p});f&&(g.height="0",g.width="0",g.style.display="none",g.style.visibility="hidden");a!==void 0&&(g.src=a);if(k){var m=A.body&&A.body.lastChild||A.body||A.head;m.parentNode.insertBefore(g,m)}b&&(g.onload=b);return g}
function vc(a,b,c,d){return wc(a,b,c,d)}function xc(a,b,c,d){a.addEventListener&&a.addEventListener(b,c,!!d)}function yc(a,b,c){a.removeEventListener&&a.removeEventListener(b,c,!1)}function D(a){z.setTimeout(a,0)}function zc(a,b){return a&&b&&a.attributes&&a.attributes[b]?a.attributes[b].value:null}function Ac(a){var b=a.innerText||a.textContent||"";b&&b!==" "&&(b=b.replace(/^[\s\xa0]+/g,""),b=b.replace(/[\s\xa0]+$/g,""));b&&(b=b.replace(/(\xa0+|\s{2,}|\n|\r\t)/g," "));return b}
function Bc(a){var b=A.createElement("div"),c=b,d,e=ec("A<div>"+a+"</div>"),f=Hb(),g=f?f.createHTML(e):e;d=new $b(g);if(c.nodeType===1&&/^(script|style)$/i.test(c.tagName))throw Error("");var k;if(d instanceof $b)k=d.C;else throw Error("");c.innerHTML=k;b=b.lastChild;for(var m=[];b&&b.firstChild;)m.push(b.removeChild(b.firstChild));return m}
function Cc(a,b,c){c=c||100;for(var d={},e=0;e<b.length;e++)d[b[e]]=!0;for(var f=a,g=0;f&&g<=c;g++){if(d[String(f.tagName).toLowerCase()])return f;f=f.parentElement}return null}function Dc(a,b,c){var d;try{d=gc.sendBeacon&&gc.sendBeacon(a)}catch(e){Xa("TAGGING",15)}d?b==null||b():wc(a,b,c)}function Ec(a,b){try{return gc.sendBeacon(a,b)}catch(c){Xa("TAGGING",15)}return!1}var Fc={cache:"no-store",credentials:"include",keepalive:!0,method:"POST",mode:"no-cors",redirect:"follow"};
function Gc(a,b,c,d,e){if(Hc()){var f=Object.assign({},Fc);b&&(f.body=b);c&&(c.attributionReporting&&(f.attributionReporting=c.attributionReporting),c.browsingTopics&&(f.browsingTopics=c.browsingTopics),c.credentials&&(f.credentials=c.credentials),c.mode&&(f.mode=c.mode),c.method&&(f.method=c.method));try{var g=z.fetch(a,f);if(g)return g.then(function(m){m&&(m.ok||m.status===0)?d==null||d():e==null||e()}).catch(function(){e==null||e()}),!0}catch(m){}}if(c&&c.yi)return e==null||e(),!1;if(b){var k=
Ec(a,b);k?d==null||d():e==null||e();return k}Ic(a,d,e);return!0}function Hc(){return typeof z.fetch==="function"}function Jc(a,b){var c=a[b];c&&typeof c.animVal==="string"&&(c=c.animVal);return c}function Kc(){var a=z.performance;if(a&&ab(a.now))return a.now()}
function Lc(){var a,b=z.performance;if(b&&b.getEntriesByType)try{var c=b.getEntriesByType("navigation");c&&c.length>0&&(a=c[0].type)}catch(d){return"e"}if(!a)return"u";switch(a){case "navigate":return"n";case "back_forward":return"h";case "reload":return"r";case "prerender":return"p";default:return"x"}}function Mc(){return z.performance||void 0}function Nc(){var a=z.webPixelsManager;return a?a.createShopifyExtend!==void 0:!1}
var wc=function(a,b,c,d){var e=new Image(1,1);rc(e,d,{});e.onload=function(){e.onload=null;b&&b()};e.onerror=function(){e.onerror=null;c&&c()};e.src=a;return e},Ic=Dc;function Oc(a,b){return this.evaluate(a)&&this.evaluate(b)}function Pc(a,b){return this.evaluate(a)===this.evaluate(b)}function Qc(a,b){return this.evaluate(a)||this.evaluate(b)}function Rc(a,b){var c=this.evaluate(a),d=this.evaluate(b);return String(c).indexOf(String(d))>-1}function Sc(a,b){var c=String(this.evaluate(a)),d=String(this.evaluate(b));return c.substring(0,d.length)===d}
function Tc(a,b){var c=this.evaluate(a),d=this.evaluate(b);switch(c){case "pageLocation":var e=z.location.href;d instanceof Na&&d.get("stripProtocol")&&(e=e.replace(/^https?:\/\//,""));return e}};/*
 jQuery (c) 2005, 2012 jQuery Foundation, Inc. jquery.org/license.
*/
var Uc=/\[object (Boolean|Number|String|Function|Array|Date|RegExp)\]/,Wc=function(a){if(a==null)return String(a);var b=Uc.exec(Object.prototype.toString.call(Object(a)));return b?b[1].toLowerCase():"object"},Xc=function(a,b){return Object.prototype.hasOwnProperty.call(Object(a),b)},Yc=function(a){if(!a||Wc(a)!="object"||a.nodeType||a==a.window)return!1;try{if(a.constructor&&!Xc(a,"constructor")&&!Xc(a.constructor.prototype,"isPrototypeOf"))return!1}catch(c){return!1}for(var b in a);return b===void 0||
Xc(a,b)},Zc=function(a,b){var c=b||(Wc(a)=="array"?[]:{}),d;for(d in a)if(Xc(a,d)){var e=a[d];Wc(e)=="array"?(Wc(c[d])!="array"&&(c[d]=[]),c[d]=Zc(e,c[d])):Yc(e)?(Yc(c[d])||(c[d]={}),c[d]=Zc(e,c[d])):c[d]=e}return c};function $c(a){if(a==void 0||Array.isArray(a)||Yc(a))return!0;switch(typeof a){case "boolean":case "number":case "string":case "function":return!0}return!1}function ad(a){return typeof a==="number"&&a>=0&&isFinite(a)&&a%1===0||typeof a==="string"&&a[0]!=="-"&&a===""+parseInt(a)};var bd=function(a){a=a===void 0?[]:a;this.V=new Aa;this.values=[];this.wa=!1;for(var b in a)a.hasOwnProperty(b)&&(ad(b)?this.values[Number(b)]=a[Number(b)]:this.V.set(b,a[b]))};h=bd.prototype;h.toString=function(a){if(a&&a.indexOf(this)>=0)return"";for(var b=[],c=0;c<this.values.length;c++){var d=this.values[c];d===null||d===void 0?b.push(""):d instanceof bd?(a=a||[],a.push(this),b.push(d.toString(a)),a.pop()):b.push(String(d))}return b.join(",")};
h.set=function(a,b){if(!this.wa)if(a==="length"){if(!ad(b))throw Ja(Error("RangeError: Length property must be a valid integer."));this.values.length=Number(b)}else ad(a)?this.values[Number(a)]=b:this.V.set(a,b)};h.get=function(a){return a==="length"?this.length():ad(a)?this.values[Number(a)]:this.V.get(a)};h.length=function(){return this.values.length};h.na=function(){for(var a=this.V.na(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push(String(b));return a};
h.ic=function(){for(var a=this.V.ic(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push(this.values[b]);return a};h.Mb=function(){for(var a=this.V.Mb(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push([String(b),this.values[b]]);return a};h.remove=function(a){ad(a)?delete this.values[Number(a)]:this.wa||this.V.remove(a)};h.pop=function(){return this.values.pop()};h.push=function(){return this.values.push.apply(this.values,ta(xa.apply(0,arguments)))};h.shift=function(){return this.values.shift()};
h.splice=function(a,b){var c=xa.apply(2,arguments);return b===void 0&&c.length===0?new bd(this.values.splice(a)):new bd(this.values.splice.apply(this.values,[a,b||0].concat(ta(c))))};h.unshift=function(){return this.values.unshift.apply(this.values,ta(xa.apply(0,arguments)))};h.has=function(a){return ad(a)&&this.values.hasOwnProperty(a)||this.V.has(a)};h.Ma=function(){this.wa=!0;Object.freeze(this.values)};h.Ec=function(){return this.wa};
function cd(a){for(var b=[],c=0;c<a.length();c++)a.has(c)&&(b[c]=a.get(c));return b};var dd=function(a,b){this.functionName=a;this.Rd=b;this.V=new Aa;this.wa=!1};h=dd.prototype;h.toString=function(){return this.functionName};h.getName=function(){return this.functionName};h.getKeys=function(){return new bd(this.na())};h.invoke=function(a){return this.Rd.call.apply(this.Rd,[new ed(this,a)].concat(ta(xa.apply(1,arguments))))};h.kb=function(a){var b=xa.apply(1,arguments);try{return this.invoke.apply(this,[a].concat(ta(b)))}catch(c){}};h.get=function(a){return this.V.get(a)};
h.set=function(a,b){this.wa||this.V.set(a,b)};h.has=function(a){return this.V.has(a)};h.remove=function(a){this.wa||this.V.remove(a)};h.na=function(){return this.V.na()};h.ic=function(){return this.V.ic()};h.Mb=function(){return this.V.Mb()};h.Ma=function(){this.wa=!0};h.Ec=function(){return this.wa};var fd=function(a,b){dd.call(this,a,b)};ra(fd,dd);var gd=function(a,b){dd.call(this,a,b)};ra(gd,dd);var ed=function(a,b){this.Rd=a;this.J=b};
ed.prototype.evaluate=function(a){var b=this.J;return Array.isArray(a)?La(b,a):a};ed.prototype.getName=function(){return this.Rd.getName()};ed.prototype.Sd=function(){return this.J.Sd()};var hd=function(){this.map=new Map};hd.prototype.set=function(a,b){this.map.set(a,b)};hd.prototype.get=function(a){return this.map.get(a)};var id=function(){this.keys=[];this.values=[]};id.prototype.set=function(a,b){this.keys.push(a);this.values.push(b)};id.prototype.get=function(a){var b=this.keys.indexOf(a);if(b>-1)return this.values[b]};function jd(){try{return Map?new hd:new id}catch(a){return new id}};var kd=function(a){if(a instanceof kd)return a;if($c(a))throw Error("Type of given value has an equivalent Pixie type.");this.value=a};kd.prototype.getValue=function(){return this.value};kd.prototype.toString=function(){return String(this.value)};var md=function(a){this.promise=a;this.wa=!1;this.V=new Aa;this.V.set("then",ld(this));this.V.set("catch",ld(this,!0));this.V.set("finally",ld(this,!1,!0))};h=md.prototype;h.get=function(a){return this.V.get(a)};h.set=function(a,b){this.wa||this.V.set(a,b)};h.has=function(a){return this.V.has(a)};h.remove=function(a){this.wa||this.V.remove(a)};h.na=function(){return this.V.na()};h.ic=function(){return this.V.ic()};h.Mb=function(){return this.V.Mb()};
var ld=function(a,b,c){b=b===void 0?!1:b;c=c===void 0?!1:c;return new fd("",function(d,e){b&&(e=d,d=void 0);c&&(e=d);d instanceof fd||(d=void 0);e instanceof fd||(e=void 0);var f=Ga(this.J),g=function(m){return function(n){try{return c?(m.invoke(f),a.promise):m.invoke(f,n)}catch(p){return Promise.reject(p instanceof Error?new kd(p):String(p))}}},k=a.promise.then(d&&g(d),e&&g(e));return new md(k)})};md.prototype.Ma=function(){this.wa=!0};md.prototype.Ec=function(){return this.wa};function nd(a,b,c){var d=jd(),e=function(g,k){for(var m=g.na(),n=0;n<m.length;n++)k[m[n]]=f(g.get(m[n]))},f=function(g){if(g===null||g===void 0)return g;var k=d.get(g);if(k)return k;if(g instanceof bd){var m=[];d.set(g,m);for(var n=g.na(),p=0;p<n.length;p++)m[n[p]]=f(g.get(n[p]));return m}if(g instanceof md)return g.promise.then(function(u){return nd(u,b,1)},function(u){return Promise.reject(nd(u,b,1))});if(g instanceof Na){var q={};d.set(g,q);e(g,q);return q}if(g instanceof fd){var r=function(){for(var u=
xa.apply(0,arguments),t=[],w=0;w<u.length;w++)t[w]=od(u[w],b,c);var x=new Da(b?b.Sd():new Ca);b&&(x.C=b.C);return f(g.invoke.apply(g,[x].concat(ta(t))))};d.set(g,r);e(g,r);return r}var v=!1;switch(c){case 1:v=!0;break;case 2:v=!1;break;case 3:v=!1;break;default:}if(g instanceof kd&&v)return g.getValue();switch(typeof g){case "boolean":case "number":case "string":case "undefined":return g;
case "object":if(g===null)return null}};return f(a)}
function od(a,b,c){var d=jd(),e=function(g,k){for(var m in g)g.hasOwnProperty(m)&&k.set(m,f(g[m]))},f=function(g){var k=d.get(g);if(k)return k;if(Array.isArray(g)||kb(g)){var m=new bd;d.set(g,m);for(var n in g)g.hasOwnProperty(n)&&m.set(n,f(g[n]));return m}if(Yc(g)){var p=new Na;d.set(g,p);e(g,p);return p}if(typeof g==="function"){var q=new fd("",function(){for(var u=xa.apply(0,arguments),t=[],w=0;w<u.length;w++)t[w]=nd(this.evaluate(u[w]),b,c);return f((0,this.J.N)(g,g,t))});d.set(g,q);e(g,q);return q}var r=typeof g;if(g===null||r==="string"||r==="number"||r==="boolean")return g;var v=!1;switch(c){case 1:v=!0;break;case 2:v=!1;break;default:}if(g!==void 0&&v)return new kd(g)};return f(a)};var pd={supportedMethods:"concat every filter forEach hasOwnProperty indexOf join lastIndexOf map pop push reduce reduceRight reverse shift slice some sort splice unshift toString".split(" "),concat:function(a){for(var b=[],c=0;c<this.length();c++)b.push(this.get(c));for(var d=1;d<arguments.length;d++)if(arguments[d]instanceof bd)for(var e=arguments[d],f=0;f<e.length();f++)b.push(e.get(f));else b.push(arguments[d]);return new bd(b)},every:function(a,b){for(var c=this.length(),d=0;d<this.length()&&
d<c;d++)if(this.has(d)&&!b.invoke(a,this.get(d),d,this))return!1;return!0},filter:function(a,b){for(var c=this.length(),d=[],e=0;e<this.length()&&e<c;e++)this.has(e)&&b.invoke(a,this.get(e),e,this)&&d.push(this.get(e));return new bd(d)},forEach:function(a,b){for(var c=this.length(),d=0;d<this.length()&&d<c;d++)this.has(d)&&b.invoke(a,this.get(d),d,this)},hasOwnProperty:function(a,b){return this.has(b)},indexOf:function(a,b,c){var d=this.length(),e=c===void 0?0:Number(c);e<0&&(e=Math.max(d+e,0));for(var f=
e;f<d;f++)if(this.has(f)&&this.get(f)===b)return f;return-1},join:function(a,b){for(var c=[],d=0;d<this.length();d++)c.push(this.get(d));return c.join(b)},lastIndexOf:function(a,b,c){var d=this.length(),e=d-1;c!==void 0&&(e=c<0?d+c:Math.min(c,e));for(var f=e;f>=0;f--)if(this.has(f)&&this.get(f)===b)return f;return-1},map:function(a,b){for(var c=this.length(),d=[],e=0;e<this.length()&&e<c;e++)this.has(e)&&(d[e]=b.invoke(a,this.get(e),e,this));return new bd(d)},pop:function(){return this.pop()},push:function(a){return this.push.apply(this,
ta(xa.apply(1,arguments)))},reduce:function(a,b,c){var d=this.length(),e,f=0;if(c!==void 0)e=c;else{if(d===0)throw Ja(Error("TypeError: Reduce on List with no elements."));for(var g=0;g<d;g++)if(this.has(g)){e=this.get(g);f=g+1;break}if(g===d)throw Ja(Error("TypeError: Reduce on List with no elements."));}for(var k=f;k<d;k++)this.has(k)&&(e=b.invoke(a,e,this.get(k),k,this));return e},reduceRight:function(a,b,c){var d=this.length(),e,f=d-1;if(c!==void 0)e=c;else{if(d===0)throw Ja(Error("TypeError: ReduceRight on List with no elements."));
for(var g=1;g<=d;g++)if(this.has(d-g)){e=this.get(d-g);f=d-(g+1);break}if(g>d)throw Ja(Error("TypeError: ReduceRight on List with no elements."));}for(var k=f;k>=0;k--)this.has(k)&&(e=b.invoke(a,e,this.get(k),k,this));return e},reverse:function(){for(var a=cd(this),b=a.length-1,c=0;b>=0;b--,c++)a.hasOwnProperty(b)?this.set(c,a[b]):this.remove(c);return this},shift:function(){return this.shift()},slice:function(a,b,c){var d=this.length();b===void 0&&(b=0);b=b<0?Math.max(d+b,0):Math.min(b,d);c=c===
void 0?d:c<0?Math.max(d+c,0):Math.min(c,d);c=Math.max(b,c);for(var e=[],f=b;f<c;f++)e.push(this.get(f));return new bd(e)},some:function(a,b){for(var c=this.length(),d=0;d<this.length()&&d<c;d++)if(this.has(d)&&b.invoke(a,this.get(d),d,this))return!0;return!1},sort:function(a,b){var c=cd(this);b===void 0?c.sort():c.sort(function(e,f){return Number(b.invoke(a,e,f))});for(var d=0;d<c.length;d++)c.hasOwnProperty(d)?this.set(d,c[d]):this.remove(d);return this},splice:function(a,b,c){return this.splice.apply(this,
[b,c].concat(ta(xa.apply(3,arguments))))},toString:function(){return this.toString()},unshift:function(a){return this.unshift.apply(this,ta(xa.apply(1,arguments)))}};var qd={charAt:1,concat:1,indexOf:1,lastIndexOf:1,match:1,replace:1,search:1,slice:1,split:1,substring:1,toLowerCase:1,toLocaleLowerCase:1,toString:1,toUpperCase:1,toLocaleUpperCase:1,trim:1},rd=new za("break"),sd=new za("continue");function td(a,b){return this.evaluate(a)+this.evaluate(b)}function ud(a,b){return this.evaluate(a)&&this.evaluate(b)}
function vd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(!(f instanceof bd))throw Error("Error: Non-List argument given to Apply instruction.");if(d===null||d===void 0)throw Ja(Error("TypeError: Can't read property "+e+" of "+d+"."));var g=typeof d==="number";if(typeof d==="boolean"||g){if(e==="toString"){if(g&&f.length()){var k=nd(f.get(0));try{return d.toString(k)}catch(t){}}return d.toString()}throw Ja(Error("TypeError: "+d+"."+e+" is not a function."));}if(typeof d===
"string"){if(qd.hasOwnProperty(e)){var m=2;m=1;var n=nd(f,void 0,m);return od(d[e].apply(d,n),this.J)}throw Ja(Error("TypeError: "+e+" is not a function"));}if(d instanceof bd){if(d.has(e)){var p=d.get(String(e));if(p instanceof fd){var q=cd(f);return p.invoke.apply(p,[this.J].concat(ta(q)))}throw Ja(Error("TypeError: "+e+" is not a function"));}if(pd.supportedMethods.indexOf(e)>=
0){var r=cd(f);return pd[e].call.apply(pd[e],[d,this.J].concat(ta(r)))}}if(d instanceof fd||d instanceof Na||d instanceof md){if(d.has(e)){var v=d.get(e);if(v instanceof fd){var u=cd(f);return v.invoke.apply(v,[this.J].concat(ta(u)))}throw Ja(Error("TypeError: "+e+" is not a function"));}if(e==="toString")return d instanceof fd?d.getName():d.toString();if(e==="hasOwnProperty")return d.has(f.get(0))}if(d instanceof kd&&e==="toString")return d.toString();throw Ja(Error("TypeError: Object has no '"+
e+"' property."));}function wd(a,b){a=this.evaluate(a);if(typeof a!=="string")throw Error("Invalid key name given for assignment.");var c=this.J;if(!c.has(a))throw Error("Attempting to assign to undefined value "+b);var d=this.evaluate(b);c.set(a,d);return d}function xd(){var a=xa.apply(0,arguments),b=Ga(this.J),c=Ka(b,a);if(c instanceof za)return c}function yd(){return rd}function zd(a){for(var b=this.evaluate(a),c=0;c<b.length;c++){var d=this.evaluate(b[c]);if(d instanceof za)return d}}
function Ad(){for(var a=this.J,b=0;b<arguments.length-1;b+=2){var c=arguments[b];if(typeof c==="string"){var d=this.evaluate(arguments[b+1]);Fa(a,c,d,!0)}}}function Bd(){return sd}function Cd(a,b){return new za(a,this.evaluate(b))}function Dd(a,b){for(var c=xa.apply(2,arguments),d=new bd,e=this.evaluate(b),f=0;f<e.length;f++)d.push(e[f]);var g=[51,a,d].concat(ta(c));this.J.add(a,this.evaluate(g))}function Ed(a,b){return this.evaluate(a)/this.evaluate(b)}
function Fd(a,b){var c=this.evaluate(a),d=this.evaluate(b),e=c instanceof kd,f=d instanceof kd;return e||f?e&&f?c.getValue()===d.getValue():!1:c==d}function Gd(){for(var a,b=0;b<arguments.length;b++)a=this.evaluate(arguments[b]);return a}function Hd(a,b,c,d){for(var e=0;e<b();e++){var f=a(c(e)),g=Ka(f,d);if(g instanceof za){if(g.type==="break")break;if(g.type==="return")return g}}}
function Id(a,b,c){if(typeof b==="string")return Hd(a,function(){return b.length},function(f){return f},c);if(b instanceof Na||b instanceof md||b instanceof bd||b instanceof fd){var d=b.na(),e=d.length;return Hd(a,function(){return e},function(f){return d[f]},c)}}function Jd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.J;return Id(function(k){g.set(d,k);return g},e,f)}
function Kd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.J;return Id(function(k){var m=Ga(g);Fa(m,d,k,!0);return m},e,f)}function Ld(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.J;return Id(function(k){var m=Ga(g);m.add(d,k);return m},e,f)}function Md(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.J;return Nd(function(k){g.set(d,k);return g},e,f)}
function Od(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.J;return Nd(function(k){var m=Ga(g);Fa(m,d,k,!0);return m},e,f)}function Pd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.J;return Nd(function(k){var m=Ga(g);m.add(d,k);return m},e,f)}
function Nd(a,b,c){if(typeof b==="string")return Hd(a,function(){return b.length},function(d){return b[d]},c);if(b instanceof bd)return Hd(a,function(){return b.length()},function(d){return b.get(d)},c);throw Ja(Error("The value is not iterable."));}
function Qd(a,b,c,d){function e(q,r){for(var v=0;v<f.length();v++){var u=f.get(v);r.add(u,q.get(u))}}var f=this.evaluate(a);if(!(f instanceof bd))throw Error("TypeError: Non-List argument given to ForLet instruction.");var g=this.J,k=this.evaluate(d),m=Ga(g);for(e(g,m);La(m,b);){var n=Ka(m,k);if(n instanceof za){if(n.type==="break")break;if(n.type==="return")return n}var p=Ga(g);e(m,p);La(p,c);m=p}}
function Rd(a,b){var c=xa.apply(2,arguments),d=this.J,e=this.evaluate(b);if(!(e instanceof bd))throw Error("Error: non-List value given for Fn argument names.");return new fd(a,function(){return function(){var f=xa.apply(0,arguments),g=Ga(d);g.C===void 0&&(g.C=this.J.C);for(var k=[],m=0;m<f.length;m++){var n=this.evaluate(f[m]);k[m]=n}for(var p=e.get("length"),q=0;q<p;q++)q<k.length?g.add(e.get(q),k[q]):g.add(e.get(q),void 0);g.add("arguments",new bd(k));var r=Ka(g,c);if(r instanceof za)return r.type===
"return"?r.data:r}}())}function Sd(a){var b=this.evaluate(a),c=this.J;if(Td&&!c.has(b))throw new ReferenceError(b+" is not defined.");return c.get(b)}
function Ud(a,b){var c,d=this.evaluate(a),e=this.evaluate(b);if(d===void 0||d===null)throw Ja(Error("TypeError: Cannot read properties of "+d+" (reading '"+e+"')"));if(d instanceof Na||d instanceof md||d instanceof bd||d instanceof fd)c=d.get(e);else if(typeof d==="string")e==="length"?c=d.length:ad(e)&&(c=d[e]);else if(d instanceof kd)return;return c}function Vd(a,b){return this.evaluate(a)>this.evaluate(b)}function Wd(a,b){return this.evaluate(a)>=this.evaluate(b)}
function Xd(a,b){var c=this.evaluate(a),d=this.evaluate(b);c instanceof kd&&(c=c.getValue());d instanceof kd&&(d=d.getValue());return c===d}function Yd(a,b){return!Xd.call(this,a,b)}function Zd(a,b,c){var d=[];this.evaluate(a)?d=this.evaluate(b):c&&(d=this.evaluate(c));var e=Ka(this.J,d);if(e instanceof za)return e}var Td=!1;
function $d(a,b){return this.evaluate(a)<this.evaluate(b)}function ae(a,b){return this.evaluate(a)<=this.evaluate(b)}function be(){for(var a=new bd,b=0;b<arguments.length;b++){var c=this.evaluate(arguments[b]);a.push(c)}return a}function ce(){for(var a=new Na,b=0;b<arguments.length-1;b+=2){var c=String(this.evaluate(arguments[b])),d=this.evaluate(arguments[b+1]);a.set(c,d)}return a}function de(a,b){return this.evaluate(a)%this.evaluate(b)}
function ee(a,b){return this.evaluate(a)*this.evaluate(b)}function fe(a){return-this.evaluate(a)}function ge(a){return!this.evaluate(a)}function he(a,b){return!Fd.call(this,a,b)}function ie(){return null}function je(a,b){return this.evaluate(a)||this.evaluate(b)}function ke(a,b){var c=this.evaluate(a);this.evaluate(b);return c}function le(a){return this.evaluate(a)}function me(){return xa.apply(0,arguments)}function ne(a){return new za("return",this.evaluate(a))}
function oe(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(d===null||d===void 0)throw Ja(Error("TypeError: Can't set property "+e+" of "+d+"."));(d instanceof fd||d instanceof bd||d instanceof Na)&&d.set(String(e),f);return f}function pe(a,b){return this.evaluate(a)-this.evaluate(b)}
function qe(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(!Array.isArray(e)||!Array.isArray(f))throw Error("Error: Malformed switch instruction.");for(var g,k=!1,m=0;m<e.length;m++)if(k||d===this.evaluate(e[m]))if(g=this.evaluate(f[m]),g instanceof za){var n=g.type;if(n==="break")return;if(n==="return"||n==="continue")return g}else k=!0;if(f.length===e.length+1&&(g=this.evaluate(f[f.length-1]),g instanceof za&&(g.type==="return"||g.type==="continue")))return g}
function re(a,b,c){return this.evaluate(a)?this.evaluate(b):this.evaluate(c)}function se(a){var b=this.evaluate(a);return b instanceof fd?"function":typeof b}function te(){for(var a=this.J,b=0;b<arguments.length;b++){var c=arguments[b];typeof c!=="string"||a.add(c,void 0)}}
function ue(a,b,c,d){var e=this.evaluate(d);if(this.evaluate(c)){var f=Ka(this.J,e);if(f instanceof za){if(f.type==="break")return;if(f.type==="return")return f}}for(;this.evaluate(a);){var g=Ka(this.J,e);if(g instanceof za){if(g.type==="break")break;if(g.type==="return")return g}this.evaluate(b)}}function ve(a){return~Number(this.evaluate(a))}function we(a,b){return Number(this.evaluate(a))<<Number(this.evaluate(b))}function xe(a,b){return Number(this.evaluate(a))>>Number(this.evaluate(b))}
function ye(a,b){return Number(this.evaluate(a))>>>Number(this.evaluate(b))}function ze(a,b){return Number(this.evaluate(a))&Number(this.evaluate(b))}function Ae(a,b){return Number(this.evaluate(a))^Number(this.evaluate(b))}function Be(a,b){return Number(this.evaluate(a))|Number(this.evaluate(b))}function Ce(){}
function De(a,b,c){try{var d=this.evaluate(b);if(d instanceof za)return d}catch(k){if(!(k instanceof Ia&&k.zk))throw k;var e=Ga(this.J);a!==""&&(k instanceof Ia&&(k=k.Mk),e.add(a,new kd(k)));var f=this.evaluate(c),g=Ka(e,f);if(g instanceof za)return g}}function Ee(a,b){var c,d;try{d=this.evaluate(a)}catch(f){if(!(f instanceof Ia&&f.zk))throw f;c=f}var e=this.evaluate(b);if(e instanceof za)return e;if(c)throw c;if(d instanceof za)return d};var Ge=function(){this.C=new Ma;Fe(this)};Ge.prototype.execute=function(a){return this.C.Mi(a)};var Fe=function(a){var b=function(c,d){var e=new gd(String(c),d);e.Ma();a.C.C.set(String(c),e)};b("map",ce);b("and",Oc);b("contains",Rc);b("equals",Pc);b("or",Qc);b("startsWith",Sc);b("variable",Tc)};var Ie=function(){this.H=!1;this.C=new Ma;He(this);this.H=!0};Ie.prototype.execute=function(a){return Je(this.C.Mi(a))};var Ke=function(a,b,c){return Je(a.C.Tl(b,c))};Ie.prototype.Ma=function(){this.C.Ma()};
var He=function(a){var b=function(c,d){var e=String(c),f=new gd(e,d);f.Ma();a.C.C.set(e,f)};b(0,td);b(1,ud);b(2,vd);b(3,wd);b(56,ze);b(57,we);b(58,ve);b(59,Be);b(60,xe);b(61,ye);b(62,Ae);b(53,xd);b(4,yd);b(5,zd);b(68,De);b(52,Ad);b(6,Bd);b(49,Cd);b(7,be);b(8,ce);b(9,zd);b(50,Dd);b(10,Ed);b(12,Fd);b(13,Gd);b(67,Ee);b(51,Rd);b(47,Jd);b(54,Kd);b(55,Ld);b(63,Qd);b(64,Md);b(65,Od);b(66,Pd);b(15,Sd);b(16,Ud);b(17,Ud);b(18,Vd);b(19,Wd);b(20,Xd);b(21,Yd);b(22,Zd);b(23,$d);b(24,ae);b(25,de);b(26,ee);b(27,
fe);b(28,ge);b(29,he);b(45,ie);b(30,je);b(32,ke);b(33,ke);b(34,le);b(35,le);b(46,me);b(36,ne);b(43,oe);b(37,pe);b(38,qe);b(39,re);b(40,se);b(44,Ce);b(41,te);b(42,ue)};Ie.prototype.Sd=function(){return this.C.Sd()};function Je(a){if(a instanceof za||a instanceof fd||a instanceof bd||a instanceof Na||a instanceof md||a instanceof kd||a===null||a===void 0||typeof a==="string"||typeof a==="number"||typeof a==="boolean")return a};var Le=function(a){this.message=a};function Me(a){var b="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[a];return b===void 0?new Le("Value "+a+" can not be encoded in web-safe base64 dictionary."):b};function Ne(a){switch(a){case 1:return"1";case 2:case 4:return"0";default:return"-"}};var Re=/^[1-9a-zA-Z_-][1-9a-c][1-9a-v]\d$/;function Se(a,b){for(var c="",d=!0;a>7;){var e=a&31;a>>=5;d?d=!1:e|=32;c=""+Me(e)+c}a<<=2;d||(a|=32);return c=""+Me(a|b)+c};var Te=function(){function a(b){return{toString:function(){return b}}}return{ql:a("consent"),Wi:a("convert_case_to"),Xi:a("convert_false_to"),Yi:a("convert_null_to"),Zi:a("convert_true_to"),aj:a("convert_undefined_to"),lo:a("debug_mode_metadata"),xa:a("function"),Gh:a("instance_name"),Wl:a("live_only"),Xl:a("malware_disabled"),METADATA:a("metadata"),am:a("original_activity_id"),xo:a("original_vendor_template_id"),wo:a("once_on_load"),Zl:a("once_per_event"),fk:a("once_per_load"),yo:a("priority_override"),
zo:a("respected_consent_types"),pk:a("setup_tags"),sg:a("tag_id"),sk:a("teardown_tags")}}();var pf;var qf=[],rf=[],sf=[],tf=[],uf=[],vf,wf,xf;function yf(a){xf=xf||a}
function zf(){for(var a=data.resource||{},b=a.macros||[],c=0;c<b.length;c++)qf.push(b[c]);for(var d=a.tags||[],e=0;e<d.length;e++)tf.push(d[e]);for(var f=a.predicates||[],g=0;g<f.length;g++)sf.push(f[g]);for(var k=a.rules||[],m=0;m<k.length;m++){for(var n=k[m],p={},q=0;q<n.length;q++){var r=n[q][0];p[r]=Array.prototype.slice.call(n[q],1);r!=="if"&&r!=="unless"||Af(p[r])}rf.push(p)}}
function Af(a){}var Bf,Cf=[],Df=[];function Ef(a,b){var c={};c[Te.xa]="__"+a;for(var d in b)b.hasOwnProperty(d)&&(c["vtp_"+d]=b[d]);return c}
function Ff(a,b,c){try{return wf(Gf(a,b,c))}catch(d){JSON.stringify(a)}return 2}function Hf(a){var b=a[Te.xa];if(!b)throw Error("Error: No function name given for function call.");return!!vf[b]}
var Gf=function(a,b,c){c=c||[];var d={},e;for(e in a)a.hasOwnProperty(e)&&(d[e]=If(a[e],b,c));return d},If=function(a,b,c){if(Array.isArray(a)){var d;switch(a[0]){case "function_id":return a[1];case "list":d=[];for(var e=1;e<a.length;e++)d.push(If(a[e],b,c));return d;case "macro":var f=a[1];if(c[f])return;var g=qf[f];if(!g||b.isBlocked(g))return;c[f]=!0;var k=String(g[Te.Gh]);try{var m=Gf(g,b,c);m.vtp_gtmEventId=b.id;b.priorityId&&(m.vtp_gtmPriorityId=b.priorityId);d=Jf(m,{event:b,index:f,type:2,
name:k});Bf&&(d=Bf.wm(d,m))}catch(y){b.logMacroError&&b.logMacroError(y,Number(f),k),d=!1}c[f]=!1;return d;case "map":d={};for(var n=1;n<a.length;n+=2)d[If(a[n],b,c)]=If(a[n+1],b,c);return d;case "template":d=[];for(var p=!1,q=1;q<a.length;q++){var r=If(a[q],b,c);xf&&(p=p||xf.rn(r));d.push(r)}return xf&&p?xf.Bm(d):d.join("");case "escape":d=If(a[1],b,c);if(xf&&Array.isArray(a[1])&&a[1][0]==="macro"&&xf.sn(a))return xf.In(d);d=String(d);for(var v=2;v<a.length;v++)$e[a[v]]&&(d=$e[a[v]](d));return d;
case "tag":var u=a[1];if(!tf[u])throw Error("Unable to resolve tag reference "+u+".");return{Dk:a[2],index:u};case "zb":var t={arg0:a[2],arg1:a[3],ignore_case:a[5]};t[Te.xa]=a[1];var w=Ff(t,b,c),x=!!a[4];return x||w!==2?x!==(w===1):null;default:throw Error("Attempting to expand unknown Value type: "+a[0]+".");}}return a},Jf=function(a,b){var c=a[Te.xa],d=b&&b.event;if(!c)throw Error("Error: No function name given for function call.");var e=vf[c],f=b&&b.type===2&&(d==null?void 0:d.reportMacroDiscrepancy)&&
e&&Cf.indexOf(c)!==-1,g={},k={},m;for(m in a)a.hasOwnProperty(m)&&vb(m,"vtp_")&&(e&&(g[m]=a[m]),!e||f)&&(k[m.substring(4)]=a[m]);e&&d&&d.cachedModelValues&&(g.vtp_gtmCachedValues=d.cachedModelValues);if(b){if(b.name==null){var n;a:{var p=b.type,q=b.index;if(q==null)n="";else{var r;switch(p){case 2:r=qf[q];break;case 1:r=tf[q];break;default:n="";break a}var v=r&&r[Te.Gh];n=v?String(v):""}}b.name=n}e&&(g.vtp_gtmEntityIndex=b.index,g.vtp_gtmEntityName=b.name)}var u,t,w;if(f&&Df.indexOf(c)===-1){Df.push(c);
var x=qb();u=e(g);var y=qb()-x,B=qb();t=pf(c,k,b);w=y-(qb()-B)}else if(e&&(u=e(g)),!e||f)t=pf(c,k,b);f&&d&&(d.reportMacroDiscrepancy(d.id,c,void 0,!0),$c(u)?(Array.isArray(u)?Array.isArray(t):Yc(u)?Yc(t):typeof u==="function"?typeof t==="function":u===t)||d.reportMacroDiscrepancy(d.id,c):u!==t&&d.reportMacroDiscrepancy(d.id,c),w!==void 0&&d.reportMacroDiscrepancy(d.id,c,w));return e?u:t};var Kf=function(a,b,c){var d;d=Error.call(this,c);this.message=d.message;"stack"in d&&(this.stack=d.stack);this.permissionId=a;this.parameters=b;this.name="PermissionError"};ra(Kf,Error);Kf.prototype.getMessage=function(){return this.message};function Lf(a,b){if(Array.isArray(a)){Object.defineProperty(a,"context",{value:{line:b[0]}});for(var c=1;c<a.length;c++)Lf(a[c],b[c])}};function Mf(){return function(a,b){var c;var d=Nf;a instanceof Ia?(a.C=d,c=a):c=new Ia(a,d);var e=c;b&&e.debugInfo.push(b);throw e;}}function Nf(a){if(!a.length)return a;a.push({id:"main",line:0});for(var b=a.length-1;b>0;b--)db(a[b].id)&&a.splice(b++,1);for(var c=a.length-1;c>0;c--)a[c].line=a[c-1].line;a.splice(0,1);return a};function Of(a){function b(r){for(var v=0;v<r.length;v++)d[r[v]]=!0}for(var c=[],d=[],e=Pf(a),f=0;f<rf.length;f++){var g=rf[f],k=Qf(g,e);if(k){for(var m=g.add||[],n=0;n<m.length;n++)c[m[n]]=!0;b(g.block||[])}else k===null&&b(g.block||[]);}for(var p=[],q=0;q<tf.length;q++)c[q]&&!d[q]&&(p[q]=!0);return p}
function Qf(a,b){for(var c=a["if"]||[],d=0;d<c.length;d++){var e=b(c[d]);if(e===0)return!1;if(e===2)return null}for(var f=a.unless||[],g=0;g<f.length;g++){var k=b(f[g]);if(k===2)return null;if(k===1)return!1}return!0}function Pf(a){var b=[];return function(c){b[c]===void 0&&(b[c]=Ff(sf[c],a));return b[c]}};function Rf(a,b){b[Te.Wi]&&typeof a==="string"&&(a=b[Te.Wi]===1?a.toLowerCase():a.toUpperCase());b.hasOwnProperty(Te.Yi)&&a===null&&(a=b[Te.Yi]);b.hasOwnProperty(Te.aj)&&a===void 0&&(a=b[Te.aj]);b.hasOwnProperty(Te.Zi)&&a===!0&&(a=b[Te.Zi]);b.hasOwnProperty(Te.Xi)&&a===!1&&(a=b[Te.Xi]);return a};var Sf=function(){this.C={}},Uf=function(a,b){var c=Tf.C,d;(d=c.C)[a]!=null||(d[a]=[]);c.C[a].push(function(){return b.apply(null,ta(xa.apply(0,arguments)))})};function Vf(a,b,c,d){if(a)for(var e=0;e<a.length;e++){var f=void 0,g="A policy function denied the permission request";try{f=a[e](b,c,d),g+="."}catch(k){g=typeof k==="string"?g+(": "+k):k instanceof Error?g+(": "+k.message):g+"."}if(!f)throw new Kf(c,d,g);}}
function Wf(a,b,c){return function(d){if(d){var e=a.C[d],f=a.C.all;if(e||f){var g=c.apply(void 0,[d].concat(ta(xa.apply(1,arguments))));Vf(e,b,d,g);Vf(f,b,d,g)}}}};var $f=function(){var a=data.permissions||{},b=Xf.ctid,c=this;this.H={};this.C=new Sf;var d={},e={},f=Wf(this.C,b,function(g){return g&&d[g]?d[g].apply(void 0,[g].concat(ta(xa.apply(1,arguments)))):{}});jb(a,function(g,k){function m(p){var q=xa.apply(1,arguments);if(!n[p])throw Yf(p,{},"The requested additional permission "+p+" is not configured.");f.apply(null,[p].concat(ta(q)))}var n={};jb(k,function(p,q){var r=Zf(p,q);n[p]=r.assert;d[p]||(d[p]=r.P);r.wk&&!e[p]&&(e[p]=r.wk)});c.H[g]=function(p,
q){var r=n[p];if(!r)throw Yf(p,{},"The requested permission "+p+" is not configured.");var v=Array.prototype.slice.call(arguments,0);r.apply(void 0,v);f.apply(void 0,v);var u=e[p];u&&u.apply(null,[m].concat(ta(v.slice(1))))}})},ag=function(a){return Tf.H[a]||function(){}};
function Zf(a,b){var c=Ef(a,b);c.vtp_permissionName=a;c.vtp_createPermissionError=Yf;try{return Jf(c)}catch(d){return{assert:function(e){throw new Kf(e,{},"Permission "+e+" is unknown.");},P:function(){throw new Kf(a,{},"Permission "+a+" is unknown.");}}}}function Yf(a,b,c){return new Kf(a,b,c)};var bg=!1;var cg={};cg.fl=mb('');cg.Gm=mb('');
var gg=function(a){var b={},c=0;jb(a,function(e,f){if(f!=null){var g=(""+f).replace(/~/g,"~~");if(dg.hasOwnProperty(e))b[dg[e]]=g;else if(eg.hasOwnProperty(e)){var k=eg[e];b.hasOwnProperty(k)||(b[k]=g)}else if(e==="category")for(var m=g.split("/",5),n=0;n<m.length;n++){var p=b,q=fg[n],r=m[n];p.hasOwnProperty(q)||(p[q]=r)}else if(c<27){var v=String.fromCharCode(c<10?48+c:65+c-10);b["k"+v]=(""+String(e)).replace(/~/g,"~~");b["v"+v]=g;c++}}});var d=[];jb(b,function(e,f){d.push(""+e+f)});return d.join("~")},
dg={item_id:"id",item_name:"nm",item_brand:"br",item_category:"ca",item_category2:"c2",item_category3:"c3",item_category4:"c4",item_category5:"c5",item_variant:"va",price:"pr",quantity:"qt",coupon:"cp",item_list_name:"ln",index:"lp",item_list_id:"li",discount:"ds",affiliation:"af",promotion_id:"pi",promotion_name:"pn",creative_name:"cn",creative_slot:"cs",location_id:"lo"},eg={id:"id",name:"nm",brand:"br",variant:"va",list_name:"ln",list_position:"lp",list:"ln",position:"lp",creative:"cn"},fg=["ca",
"c2","c3","c4","c5"];function hg(a,b){if(a==="")return b;var c=Number(a);return isNaN(c)?b:c};var ig=[],jg={};function kg(a){return ig[a]===void 0?!1:ig[a]};var lg=[];function mg(a){switch(a){case 0:return 0;case 49:return 10;case 50:return 11;case 51:return 1;case 52:return 2;case 53:return 7;case 74:return 3;case 75:return 12;case 110:return 4;case 112:return 5;case 130:return 9;case 131:return 6}}function ng(a,b){lg[a]=b;var c=mg(a);c!==void 0&&(ig[c]=b)}function G(a){ng(a,!0)}G(38);G(34);G(35);G(36);
G(55);G(104);G(141);G(18);
G(149);G(140);G(76);G(114);
G(57);G(6);G(105);
G(136);G(98);G(87);G(75);
G(111);G(155);G(127);
G(20);G(72);G(109);
G(150);G(112);ng(23,!1),G(24);
jg[1]=hg('1',6E4);jg[3]=hg('10',1);jg[2]=hg('',50);G(29);
G(10);G(86);G(137);G(117);
G(153);
G(131);G(134);
G(122);G(27);
G(69);G(70);G(130);
G(50);G(49);G(90);
G(97);G(107);
G(148);G(96);
G(129);
G(110);G(91);G(31);
G(22);G(54);G(14);G(146);
G(147);
G(106);G(108);G(92);G(12);G(63);
G(15);G(58);G(144);G(84);G(133);G(118);


G(77);
G(28);
function I(a){return!!lg[a]}function og(a,b){for(var c=!1,d=!1,e=0;c===d;)if(c=((Math.random()*4294967296|0)&1)===0,d=((Math.random()*4294967296|0)&1)===0,e++,e>30)return;c?G(b):G(a)};
var pg=function(){this.events=[];this.C="";this.fa={};this.baseUrl="";this.N=0;this.O=this.H=!1;this.endpoint=0;I(85)&&(this.O=!0)};pg.prototype.add=function(a){return this.U(a)?(this.events.push(a),this.C=a.H,this.fa=a.fa,this.baseUrl=a.baseUrl,this.N+=a.O,this.H=a.N,this.endpoint=a.endpoint,this.destinationId=a.destinationId,this.ba=a.eventId,this.sa=a.priorityId,!0):!1};pg.prototype.U=function(a){return this.events.length?this.events.length>=20||a.O+this.N>=16384?!1:this.baseUrl===a.baseUrl&&this.H===
a.N&&this.jb(a):!0};pg.prototype.jb=function(a){var b=this;if(!this.O)return this.C===a.H;var c=Object.keys(this.fa);return c.length===Object.keys(a.fa).length&&c.every(function(d){return a.fa.hasOwnProperty(d)&&String(b.fa[d])===String(a.fa[d])})};var qg={},rg=(qg.uaa=!0,qg.uab=!0,qg.uafvl=!0,qg.uamb=!0,qg.uam=!0,qg.uap=!0,qg.uapv=!0,qg.uaw=!0,qg);
var ug=function(a,b){var c=a.events;if(c.length===1)return sg(c[0],b);var d=[];a.C&&d.push(a.C);for(var e={},f=0;f<c.length;f++)jb(c[f].hd,function(v,u){u!=null&&(e[v]=e[v]||{},e[v][String(u)]=e[v][String(u)]+1||1)});var g={};jb(e,function(v,u){var t,w=-1,x=0;jb(u,function(y,B){x+=B;var C=(y.length+v.length+2)*(B-1);C>w&&(t=y,w=C)});x===c.length&&(g[v]=t)});tg(g,d);b&&d.push("_s="+b);for(var k=d.join("&"),m=[],n={},p=0;p<c.length;n={zi:void 0},p++){var q=[];n.zi={};jb(c[p].hd,function(v){return function(u,
t){g[u]!==""+t&&(v.zi[u]=t)}}(n));c[p].C&&q.push(c[p].C);tg(n.zi,q);m.push(q.join("&"))}var r=m.join("\r\n");return{params:k,body:r}},sg=function(a,b){var c=[];a.H&&c.push(a.H);b&&c.push("_s="+b);tg(a.hd,c);var d=!1;a.C&&(c.push(a.C),d=!0);var e=c.join("&"),f="",g=e.length+a.baseUrl.length+1;d&&g>2048&&(f=c.pop(),e=c.join("&"));return{params:e,body:f}},tg=function(a,b){jb(a,function(c,d){d!=null&&b.push(encodeURIComponent(c)+"="+encodeURIComponent(d))})};var vg=function(a){var b=[];jb(a,function(c,d){d!=null&&b.push(encodeURIComponent(c)+"="+encodeURIComponent(String(d)))});return b.join("&")},wg=function(a,b,c,d,e,f,g,k){this.baseUrl=b;this.endpoint=c;this.destinationId=f;this.eventId=g;this.priorityId=k;this.fa=a.fa;this.hd=a.hd;this.ii=a.ii;this.N=d;this.H=vg(a.fa);this.C=vg(a.ii);this.O=this.C.length;if(e&&this.O>16384)throw Error("EVENT_TOO_LARGE");};
var zg=function(a,b){for(var c=0;c<b.length;c++){var d=a,e=b[c];if(!xg.exec(e))throw Error("Invalid key wildcard");var f=e.indexOf(".*"),g=f!==-1&&f===e.length-2,k=g?e.slice(0,e.length-2):e,m;a:if(d.length===0)m=!1;else{for(var n=d.split("."),p=0;p<n.length;p++)if(!yg.exec(n[p])){m=!1;break a}m=!0}if(!m||k.length>d.length||!g&&d.length!==e.length?0:g?vb(d,k)&&(d===k||d.charAt(k.length)==="."):d===k)return!0}return!1},yg=/^[a-z$_][\w-$]*$/i,xg=/^(?:[a-z_$][a-z-_$0-9]*\.)*[a-z_$][a-z-_$0-9]*(?:\.\*)?$/i;
var Ag=["matches","webkitMatchesSelector","mozMatchesSelector","msMatchesSelector","oMatchesSelector"];function Bg(a,b){var c=String(a),d=String(b),e=c.length-d.length;return e>=0&&c.indexOf(d,e)===e}function Cg(a,b){return String(a).split(",").indexOf(String(b))>=0}var Dg=new ib;function Eg(a,b,c){var d=c?"i":void 0;try{var e=String(b)+String(d),f=Dg.get(e);f||(f=new RegExp(b,d),Dg.set(e,f));return f.test(a)}catch(g){return!1}}function Fg(a,b){return String(a).indexOf(String(b))>=0}
function Gg(a,b){return String(a)===String(b)}function Hg(a,b){return Number(a)>=Number(b)}function Ig(a,b){return Number(a)<=Number(b)}function Jg(a,b){return Number(a)>Number(b)}function Kg(a,b){return Number(a)<Number(b)}function Lg(a,b){return vb(String(a),String(b))};var Sg=/^([a-z][a-z0-9]*):(!|\?)(\*|string|boolean|number|Fn|PixieMap|List|OpaqueValue)$/i,Tg={Fn:"function",PixieMap:"Object",List:"Array"};
function Ug(a,b){for(var c=["input:!*"],d=0;d<c.length;d++){var e=Sg.exec(c[d]);if(!e)throw Error("Internal Error in "+a);var f=e[1],g=e[2]==="!",k=e[3],m=b[d];if(m==null){if(g)throw Error("Error in "+a+". Required argument "+f+" not supplied.");}else if(k!=="*"){var n=typeof m;m instanceof fd?n="Fn":m instanceof bd?n="List":m instanceof Na?n="PixieMap":m instanceof md?n="PixiePromise":m instanceof kd&&(n="OpaqueValue");if(n!==k)throw Error("Error in "+a+". Argument "+f+" has type "+((Tg[n]||n)+", which does not match required type ")+
((Tg[k]||k)+"."));}}}function J(a,b,c){for(var d=[],e=l(c),f=e.next();!f.done;f=e.next()){var g=f.value;g instanceof fd?d.push("function"):g instanceof bd?d.push("Array"):g instanceof Na?d.push("Object"):g instanceof md?d.push("Promise"):g instanceof kd?d.push("OpaqueValue"):d.push(typeof g)}return Error("Argument error in "+a+". Expected argument types ["+(b.join(",")+"], but received [")+(d.join(",")+"]."))}function Vg(a){return a instanceof Na}function Wg(a){return Vg(a)||a===null||Xg(a)}
function Yg(a){return a instanceof fd}function Zg(a){return Yg(a)||a===null||Xg(a)}function $g(a){return a instanceof bd}function ah(a){return a instanceof kd}function bh(a){return typeof a==="string"}function ch(a){return bh(a)||a===null||Xg(a)}function dh(a){return typeof a==="boolean"}function eh(a){return dh(a)||a===null||Xg(a)}function fh(a){return typeof a==="number"}function Xg(a){return a===void 0};function gh(a){return""+a}
function hh(a,b){var c=[];return c};function ih(a,b){var c=new fd(a,function(){for(var d=Array.prototype.slice.call(arguments,0),e=0;e<d.length;e++)d[e]=this.evaluate(d[e]);try{return b.apply(this,d)}catch(g){throw Ja(g);}});c.Ma();return c}
function jh(a,b){var c=new Na,d;for(d in b)if(b.hasOwnProperty(d)){var e=b[d];ab(e)?c.set(d,ih(a+"_"+d,e)):Yc(e)?c.set(d,jh(a+"_"+d,e)):(db(e)||bb(e)||typeof e==="boolean")&&c.set(d,e)}c.Ma();return c};function kh(a,b){if(!bh(a))throw J(this.getName(),["string"],arguments);if(!ch(b))throw J(this.getName(),["string","undefined"],arguments);var c={},d=new Na;return d=jh("AssertApiSubject",
c)};function lh(a,b){if(!ch(b))throw J(this.getName(),["string","undefined"],arguments);if(a instanceof md)throw Error("Argument actual cannot have type Promise. Assertions on asynchronous code aren't supported.");var c={},d=new Na;return d=jh("AssertThatSubject",c)};function mh(a){return function(){for(var b=xa.apply(0,arguments),c=[],d=this.J,e=0;e<b.length;++e)c.push(nd(b[e],d));return od(a.apply(null,c))}}function nh(){for(var a=Math,b=oh,c={},d=0;d<b.length;d++){var e=b[d];a.hasOwnProperty(e)&&(c[e]=mh(a[e].bind(a)))}return c};function ph(a){return a!=null&&vb(a,"__cvt_")};function qh(a){var b;return b};function rh(a){var b;return b};function sh(a){try{return encodeURI(a)}catch(b){}};function th(a){try{return encodeURIComponent(String(a))}catch(b){}};
var uh=function(a,b){for(var c=0;c<b.length;c++){if(a===void 0)return;a=a[b[c]]}return a},vh=function(a,b){var c=b.preHit;if(c){var d=a[0];switch(d){case "hitData":return a.length<2?void 0:uh(c.getHitData(a[1]),a.slice(2));case "metadata":return a.length<2?void 0:uh(c.getMetadata(a[1]),a.slice(2));case "eventName":return c.getEventName();case "destinationId":return c.getDestinationId();default:throw Error(d+" is not a valid field that can be accessed\n                      from PreHit data.");}}},
xh=function(a,b){if(a){if(a.contextValue!==void 0){var c;a:{var d=a.contextValue,e=d.keyParts;if(e&&e.length!==0){var f=d.namespaceType;switch(f){case 1:c=vh(e,b);break a;case 2:var g=b.macro;c=g?g[e[0]]:void 0;break a;default:throw Error("Unknown Namespace Type used: "+f);}}c=void 0}return c}if(a.booleanExpressionValue!==void 0)return wh(a.booleanExpressionValue,b);if(a.booleanValue!==void 0)return!!a.booleanValue;if(a.stringValue!==void 0)return String(a.stringValue);if(a.integerValue!==void 0)return Number(a.integerValue);
if(a.doubleValue!==void 0)return Number(a.doubleValue);throw Error("Unknown field used for variable of type ExpressionValue:"+a);}},wh=function(a,b){var c=a.args;if(!Array.isArray(c)||c.length===0)throw Error('Invalid boolean expression format. Expected "args":'+c+" property to\n         be non-empty array.");var d=function(g){return xh(g,b)};switch(a.type){case 1:for(var e=0;e<c.length;e++)if(d(c[e]))return!0;return!1;case 2:for(var f=0;f<c.length;f++)if(!d(c[f]))return!1;return c.length>0;case 3:return!d(c[0]);
case 4:return Eg(d(c[0]),d(c[1]),!1);case 5:return Gg(d(c[0]),d(c[1]));case 6:return Lg(d(c[0]),d(c[1]));case 7:return Bg(d(c[0]),d(c[1]));case 8:return Fg(d(c[0]),d(c[1]));case 9:return Kg(d(c[0]),d(c[1]));case 10:return Ig(d(c[0]),d(c[1]));case 11:return Jg(d(c[0]),d(c[1]));case 12:return Hg(d(c[0]),d(c[1]));case 13:return Cg(d(c[0]),String(d(c[1])));default:throw Error('Invalid boolean expression format. Expected "type" property tobe a positive integer which is less than 14.');}};function yh(a){if(!ch(a))throw J(this.getName(),["string|undefined"],arguments);};function zh(a,b){if(!fh(a)||!fh(b))throw J(this.getName(),["number","number"],arguments);return gb(a,b)};function Ah(){return(new Date).getTime()};function Bh(a){if(a===null)return"null";if(a instanceof bd)return"array";if(a instanceof fd)return"function";if(a instanceof kd){var b=a.getValue();if((b==null?void 0:b.constructor)===void 0||b.constructor.name===void 0){var c=String(b);return c.substring(8,c.length-1)}return String(b.constructor.name)}return typeof a};function Ch(a){function b(c){return function(d){try{return c(d)}catch(e){(bg||cg.fl)&&a.call(this,e.message)}}}return{parse:b(function(c){return od(JSON.parse(c))}),stringify:b(function(c){return JSON.stringify(nd(c))}),publicName:"JSON"}};function Dh(a){return lb(nd(a,this.J))};function Eh(a){return Number(nd(a,this.J))};function Fh(a){return a===null?"null":a===void 0?"undefined":a.toString()};function Gh(a,b,c){var d=null,e=!1;return e?d:null};var oh="floor ceil round max min abs pow sqrt".split(" ");function Hh(){var a={};return{Sm:function(b){return a.hasOwnProperty(b)?a[b]:void 0},Zk:function(b,c){a[b]=c},reset:function(){a={}}}}function Ih(a,b){return function(){return fd.prototype.invoke.apply(a,[b].concat(ta(xa.apply(0,arguments))))}}
function Jh(a,b){if(!bh(a))throw J(this.getName(),["string","any"],arguments);}
function Kh(a,b){if(!bh(a)||!Vg(b))throw J(this.getName(),["string","PixieMap"],arguments);};var Lh={};var Mh=function(a){var b=new Na;if(a instanceof bd)for(var c=a.na(),d=0;d<c.length;d++){var e=c[d];a.has(e)&&b.set(e,a.get(e))}else if(a instanceof fd)for(var f=a.na(),g=0;g<f.length;g++){var k=f[g];b.set(k,a.get(k))}else for(var m=0;m<a.length;m++)b.set(m,a[m]);return b};
Lh.keys=function(a){Ug(this.getName(),arguments);if(a instanceof bd||a instanceof fd||typeof a==="string")a=Mh(a);if(a instanceof Na||a instanceof md)return new bd(a.na());return new bd};
Lh.values=function(a){Ug(this.getName(),arguments);if(a instanceof bd||a instanceof fd||typeof a==="string")a=Mh(a);if(a instanceof Na||a instanceof md)return new bd(a.ic());return new bd};
Lh.entries=function(a){Ug(this.getName(),arguments);if(a instanceof bd||a instanceof fd||typeof a==="string")a=Mh(a);if(a instanceof Na||a instanceof md)return new bd(a.Mb().map(function(b){return new bd(b)}));return new bd};
Lh.freeze=function(a){(a instanceof Na||a instanceof md||a instanceof bd||a instanceof fd)&&a.Ma();return a};Lh.delete=function(a,b){if(a instanceof Na&&!a.Ec())return a.remove(b),!0;return!1};function K(a,b){var c=xa.apply(2,arguments),d=a.J.C;if(!d)throw Error("Missing program state.");if(d.On){try{d.xk.apply(null,[b].concat(ta(c)))}catch(e){throw Xa("TAGGING",21),e;}return}d.xk.apply(null,[b].concat(ta(c)))};var Nh=function(){this.H={};this.C={};this.N=!0;};Nh.prototype.get=function(a,b){var c=this.contains(a)?this.H[a]:void 0;return c};Nh.prototype.contains=function(a){return this.H.hasOwnProperty(a)};
Nh.prototype.add=function(a,b,c){if(this.contains(a))throw Error("Attempting to add a function which already exists: "+a+".");if(this.C.hasOwnProperty(a))throw Error("Attempting to add an API with an existing private API name: "+a+".");this.H[a]=c?void 0:ab(b)?ih(a,b):jh(a,b)};function Oh(a,b){var c=void 0;return c};function Ph(a,b){}Ph.K="internal.safeInvoke";function Qh(){var a={};
return a};var M={m:{za:"ad_personalization",R:"ad_storage",T:"ad_user_data",X:"analytics_storage",yb:"region",Sb:"consent_updated",de:"wait_for_update",dj:"app_remove",ej:"app_store_refund",fj:"app_store_subscription_cancel",gj:"app_store_subscription_convert",ij:"app_store_subscription_renew",wl:"consent_update",Qg:"add_payment_info",Rg:"add_shipping_info",Fc:"add_to_cart",Gc:"remove_from_cart",Sg:"view_cart",nc:"begin_checkout",Hc:"select_item",Ab:"view_item_list",Ub:"select_promotion",Bb:"view_promotion",
Pa:"purchase",Ic:"refund",Wa:"view_item",Tg:"add_to_wishlist",xl:"exception",jj:"first_open",kj:"first_visit",ia:"gtag.config",eb:"gtag.get",lj:"in_app_purchase",oc:"page_view",yl:"screen_view",mj:"session_start",zl:"source_update",Al:"timing_complete",Bl:"track_social",Jc:"user_engagement",Cl:"user_id_update",fe:"gclid_link_decoration_source",he:"gclid_storage_source",Cb:"gclgb",Qa:"gclid",nj:"gclid_len",ld:"gclgs",md:"gcllp",nd:"gclst",ka:"ads_data_redaction",ie:"gad_source",je:"gad_source_src",
oj:"ndclid",pj:"ngad_source",qj:"ngbraid",rj:"ngclid",sj:"ngclsrc",Kc:"gclid_url",tj:"gclsrc",ke:"gbraid",od:"wbraid",qa:"allow_ad_personalization_signals",me:"allow_custom_scripts",ne:"allow_direct_google_requests",oe:"allow_display_features",pe:"allow_enhanced_conversions",fb:"allow_google_signals",Ea:"allow_interest_groups",Dl:"app_id",El:"app_installer_id",Fl:"app_name",Gl:"app_version",Db:"auid",uj:"auto_detection_enabled",qc:"aw_remarketing",Rf:"aw_remarketing_only",qe:"discount",se:"aw_feed_country",
te:"aw_feed_language",ja:"items",ue:"aw_merchant_id",Ug:"aw_basket_type",pd:"campaign_content",rd:"campaign_id",sd:"campaign_medium",ud:"campaign_name",vd:"campaign",wd:"campaign_source",xd:"campaign_term",mb:"client_id",vj:"rnd",Vg:"consent_update_type",wj:"content_group",xj:"content_type",nb:"conversion_cookie_prefix",yd:"conversion_id",Aa:"conversion_linker",Wg:"conversion_linker_disabled",rc:"conversion_api",Sf:"cookie_deprecation",Ra:"cookie_domain",Sa:"cookie_expires",Xa:"cookie_flags",sc:"cookie_name",
ob:"cookie_path",Ja:"cookie_prefix",Vb:"cookie_update",Lc:"country",Fa:"currency",Xg:"customer_buyer_stage",zd:"customer_lifetime_value",Yg:"customer_loyalty",Zg:"customer_ltv_bucket",Bd:"custom_map",ah:"gcldc",Mc:"dclid",bh:"debug_mode",la:"developer_id",yj:"disable_merchant_reported_purchases",uc:"dc_custom_params",zj:"dc_natural_search",eh:"dynamic_event_settings",fh:"affiliation",ve:"checkout_option",Tf:"checkout_step",gh:"coupon",Cd:"item_list_name",Uf:"list_name",Aj:"promotions",Dd:"shipping",
Vf:"tax",we:"engagement_time_msec",xe:"enhanced_client_id",ye:"enhanced_conversions",hh:"enhanced_conversions_automatic_settings",ze:"estimated_delivery_date",Wf:"euid_logged_in_state",Ed:"event_callback",Hl:"event_category",pb:"event_developer_id_string",Il:"event_label",vc:"event",Ae:"event_settings",Be:"event_timeout",Jl:"description",Kl:"fatal",Bj:"experiments",Xf:"firebase_id",Nc:"first_party_collection",Ce:"_x_20",Fb:"_x_19",Cj:"fledge_drop_reason",ih:"fledge",jh:"flight_error_code",kh:"flight_error_message",
Dj:"fl_activity_category",Ej:"fl_activity_group",lh:"fl_advertiser_id",Fj:"fl_ar_dedupe",De:"match_id",Gj:"fl_random_number",Hj:"tran",Ij:"u",Ee:"gac_gclid",Oc:"gac_wbraid",mh:"gac_wbraid_multiple_conversions",nh:"ga_restrict_domain",oh:"ga_temp_client_id",Ll:"ga_temp_ecid",wc:"gdpr_applies",ph:"geo_granularity",Wb:"value_callback",Gb:"value_key",Pc:"google_analysis_params",Qc:"_google_ng",Rc:"google_signals",qh:"google_tld",Fe:"groups",rh:"gsa_experiment_id",Ge:"gtag_event_feature_usage",Jj:"gtm_up",
Xb:"iframe_state",Fd:"ignore_referrer",Yf:"internal_traffic_results",xc:"is_legacy_converted",Yb:"is_legacy_loaded",He:"is_passthrough",Sc:"_lps",Ya:"language",Ie:"legacy_developer_id_string",Ba:"linker",Tc:"accept_incoming",Hb:"decorate_forms",Z:"domains",Zb:"url_position",Zf:"merchant_feed_label",cg:"merchant_feed_language",dg:"merchant_id",sh:"method",Ml:"name",Kj:"navigation_type",Gd:"new_customer",Je:"non_interaction",Lj:"optimize_id",th:"page_hostname",Hd:"page_path",Ga:"page_referrer",hb:"page_title",
uh:"passengers",vh:"phone_conversion_callback",Mj:"phone_conversion_country_code",wh:"phone_conversion_css_class",Nj:"phone_conversion_ids",xh:"phone_conversion_number",yh:"phone_conversion_options",Nl:"_platinum_request_status",zh:"_protected_audience_enabled",Id:"quantity",Ke:"redact_device_info",eg:"referral_exclusion_definition",no:"_request_start_time",rb:"restricted_data_processing",Oj:"retoken",Ol:"sample_rate",fg:"screen_name",ac:"screen_resolution",Pj:"_script_source",Qj:"search_term",Ta:"send_page_view",
yc:"send_to",Uc:"server_container_url",Jd:"session_duration",Le:"session_engaged",gg:"session_engaged_time",Ib:"session_id",Me:"session_number",Ne:"_shared_user_id",Kd:"delivery_postal_code",oo:"_tag_firing_delay",po:"_tag_firing_time",Pl:"temporary_client_id",Ah:"_timezone",hg:"topmost_url",Rj:"tracking_id",ig:"traffic_type",Ha:"transaction_id",Jb:"transport_url",Bh:"trip_type",Ac:"update",ib:"url_passthrough",Sj:"uptgs",Oe:"_user_agent_architecture",Pe:"_user_agent_bitness",Qe:"_user_agent_full_version_list",
Re:"_user_agent_mobile",Se:"_user_agent_model",Te:"_user_agent_platform",Ue:"_user_agent_platform_version",Ve:"_user_agent_wow64",Ia:"user_data",jg:"user_data_auto_latency",kg:"user_data_auto_meta",lg:"user_data_auto_multi",mg:"user_data_auto_selectors",ng:"user_data_auto_status",sb:"user_data_mode",We:"user_data_settings",Ca:"user_id",tb:"user_properties",Tj:"_user_region",Ld:"us_privacy_string",ra:"value",Ch:"wbraid_multiple_conversions",Md:"_fpm_parameters",Eh:"_host_name",bk:"_in_page_command",
dk:"_ip_override",ek:"_is_passthrough_cid",bc:"non_personalized_ads",bf:"_sst_parameters",Eb:"conversion_label",oa:"page_location",qb:"global_developer_id_string",zc:"tc_privacy_string"}},Rh={},Sh=Object.freeze((Rh[M.m.qa]=1,Rh[M.m.oe]=1,Rh[M.m.pe]=1,Rh[M.m.fb]=1,Rh[M.m.ja]=1,Rh[M.m.Ra]=1,Rh[M.m.Sa]=1,Rh[M.m.Xa]=1,Rh[M.m.sc]=1,Rh[M.m.ob]=1,Rh[M.m.Ja]=1,Rh[M.m.Vb]=1,Rh[M.m.Bd]=1,Rh[M.m.la]=1,Rh[M.m.eh]=1,Rh[M.m.Ed]=1,Rh[M.m.Ae]=1,Rh[M.m.Be]=1,Rh[M.m.Nc]=1,Rh[M.m.nh]=1,Rh[M.m.Pc]=1,Rh[M.m.Rc]=1,Rh[M.m.qh]=
1,Rh[M.m.Fe]=1,Rh[M.m.Yf]=1,Rh[M.m.xc]=1,Rh[M.m.Yb]=1,Rh[M.m.Ba]=1,Rh[M.m.eg]=1,Rh[M.m.rb]=1,Rh[M.m.Ta]=1,Rh[M.m.yc]=1,Rh[M.m.Uc]=1,Rh[M.m.Jd]=1,Rh[M.m.gg]=1,Rh[M.m.Kd]=1,Rh[M.m.Jb]=1,Rh[M.m.Ac]=1,Rh[M.m.We]=1,Rh[M.m.tb]=1,Rh[M.m.bf]=1,Rh));Object.freeze([M.m.oa,M.m.Ga,M.m.hb,M.m.Ya,M.m.fg,M.m.Ca,M.m.Xf,M.m.wj]);
var Th={},Uh=Object.freeze((Th[M.m.dj]=1,Th[M.m.ej]=1,Th[M.m.fj]=1,Th[M.m.gj]=1,Th[M.m.ij]=1,Th[M.m.jj]=1,Th[M.m.kj]=1,Th[M.m.lj]=1,Th[M.m.mj]=1,Th[M.m.Jc]=1,Th)),Vh={},Wh=Object.freeze((Vh[M.m.Qg]=1,Vh[M.m.Rg]=1,Vh[M.m.Fc]=1,Vh[M.m.Gc]=1,Vh[M.m.Sg]=1,Vh[M.m.nc]=1,Vh[M.m.Hc]=1,Vh[M.m.Ab]=1,Vh[M.m.Ub]=1,Vh[M.m.Bb]=1,Vh[M.m.Pa]=1,Vh[M.m.Ic]=1,Vh[M.m.Wa]=1,Vh[M.m.Tg]=1,Vh)),Xh=Object.freeze([M.m.qa,M.m.ne,M.m.fb,M.m.Vb,M.m.Nc,M.m.Fd,M.m.Ta,M.m.Ac]),Yh=Object.freeze([].concat(ta(Xh))),Zh=Object.freeze([M.m.Sa,
M.m.Be,M.m.Jd,M.m.gg,M.m.we]),$h=Object.freeze([].concat(ta(Zh))),ai={},bi=(ai[M.m.R]="1",ai[M.m.X]="2",ai[M.m.T]="3",ai[M.m.za]="4",ai),ci={},di=Object.freeze((ci.search="s",ci.youtube="y",ci.playstore="p",ci.shopping="h",ci.ads="a",ci.maps="m",ci));Object.freeze(M.m);var ei={},fi=(ei[M.m.Sb]="gcu",ei[M.m.Cb]="gclgb",ei[M.m.Qa]="gclaw",ei[M.m.nj]="gclid_len",ei[M.m.ld]="gclgs",ei[M.m.md]="gcllp",ei[M.m.nd]="gclst",ei[M.m.oj]="ndclid",ei[M.m.pj]="ngad_source",ei[M.m.qj]="ngbraid",ei[M.m.rj]="ngclid",ei[M.m.sj]="ngclsrc",ei[M.m.Db]="auid",ei[M.m.qe]="dscnt",ei[M.m.se]="fcntr",ei[M.m.te]="flng",ei[M.m.ue]="mid",ei[M.m.Ug]="bttype",ei[M.m.mb]="gacid",ei[M.m.Eb]="label",ei[M.m.rc]="capi",ei[M.m.Sf]="pscdl",ei[M.m.Fa]="currency_code",ei[M.m.Xg]="clobs",ei[M.m.zd]="vdltv",
ei[M.m.Yg]="clolo",ei[M.m.Zg]="clolb",ei[M.m.bh]="_dbg",ei[M.m.ze]="oedeld",ei[M.m.pb]="edid",ei[M.m.Cj]="fdr",ei[M.m.ih]="fledge",ei[M.m.Ee]="gac",ei[M.m.Oc]="gacgb",ei[M.m.mh]="gacmcov",ei[M.m.wc]="gdpr",ei[M.m.qb]="gdid",ei[M.m.Qc]="_ng",ei[M.m.rh]="gsaexp",ei[M.m.Ge]="_tu",ei[M.m.Xb]="frm",ei[M.m.He]="gtm_up",ei[M.m.Sc]="lps",ei[M.m.Ie]="did",ei[M.m.Zf]="fcntr",ei[M.m.cg]="flng",ei[M.m.dg]="mid",ei[M.m.Gd]=void 0,ei[M.m.hb]="tiba",ei[M.m.rb]="rdp",ei[M.m.Ib]="ecsid",ei[M.m.Ne]="ga_uid",ei[M.m.Kd]=
"delopc",ei[M.m.zc]="gdpr_consent",ei[M.m.Ha]="oid",ei[M.m.Sj]="uptgs",ei[M.m.Oe]="uaa",ei[M.m.Pe]="uab",ei[M.m.Qe]="uafvl",ei[M.m.Re]="uamb",ei[M.m.Se]="uam",ei[M.m.Te]="uap",ei[M.m.Ue]="uapv",ei[M.m.Ve]="uaw",ei[M.m.jg]="ec_lat",ei[M.m.kg]="ec_meta",ei[M.m.lg]="ec_m",ei[M.m.mg]="ec_sel",ei[M.m.ng]="ec_s",ei[M.m.sb]="ec_mode",ei[M.m.Ca]="userId",ei[M.m.Ld]="us_privacy",ei[M.m.ra]="value",ei[M.m.Ch]="mcov",ei[M.m.Eh]="hn",ei[M.m.bk]="gtm_ee",ei[M.m.bc]="npa",ei[M.m.yd]=null,ei[M.m.ac]=null,ei[M.m.Ya]=
null,ei[M.m.ja]=null,ei[M.m.oa]=null,ei[M.m.Ga]=null,ei[M.m.hg]=null,ei[M.m.Md]=null,ei[M.m.fe]=null,ei[M.m.he]=null,ei[M.m.Pc]=null,ei);function gi(a,b){if(a){var c=a.split("x");c.length===2&&(hi(b,"u_w",c[0]),hi(b,"u_h",c[1]))}}
function ii(a){var b=ji;b=b===void 0?ki:b;var c;var d=b;if(a&&a.length){for(var e=[],f=0;f<a.length;++f){var g=a[f];g&&e.push({item_id:d(g),quantity:g.quantity,value:g.price,start_date:g.start_date,end_date:g.end_date})}c=e}else c=[];var k;var m=c;if(m){for(var n=[],p=0;p<m.length;p++){var q=m[p],r=[];q&&(r.push(li(q.value)),r.push(li(q.quantity)),r.push(li(q.item_id)),r.push(li(q.start_date)),r.push(li(q.end_date)),n.push("("+r.join("*")+")"))}k=n.length>0?n.join(""):""}else k="";return k}
function ki(a){return mi(a.item_id,a.id,a.item_name)}function mi(){for(var a=l(xa.apply(0,arguments)),b=a.next();!b.done;b=a.next()){var c=b.value;if(c!==null&&c!==void 0)return c}}function ni(a){if(a&&a.length){for(var b=[],c=0;c<a.length;++c){var d=a[c];d&&d.estimated_delivery_date?b.push(""+d.estimated_delivery_date):b.push("")}return b.join(",")}}function hi(a,b,c){c===void 0||c===null||c===""&&!rg[b]||(a[b]=c)}function li(a){return typeof a!=="number"&&typeof a!=="string"?"":a.toString()};function ri(a){return si?A.querySelectorAll(a):null}
function ti(a,b){if(!si)return null;if(Element.prototype.closest)try{return a.closest(b)}catch(e){return null}var c=Element.prototype.matches||Element.prototype.webkitMatchesSelector||Element.prototype.mozMatchesSelector||Element.prototype.msMatchesSelector||Element.prototype.oMatchesSelector,d=a;if(!A.documentElement.contains(d))return null;do{try{if(c.call(d,b))return d}catch(e){break}d=d.parentElement||d.parentNode}while(d!==null&&d.nodeType===1);return null}var ui=!1;
if(A.querySelectorAll)try{var vi=A.querySelectorAll(":root");vi&&vi.length==1&&vi[0]==A.documentElement&&(ui=!0)}catch(a){}var si=ui;function wi(a){switch(a){case 0:break;case 9:return"e4";case 6:return"e5";case 14:return"e6";default:return"e7"}};var xi=/^[0-9A-Fa-f]{64}$/;function yi(a){try{return(new TextEncoder).encode(a)}catch(e){for(var b=[],c=0;c<a.length;c++){var d=a.charCodeAt(c);d<128?b.push(d):d<2048?b.push(192|d>>6,128|d&63):d<55296||d>=57344?b.push(224|d>>12,128|d>>6&63,128|d&63):(d=65536+((d&1023)<<10|a.charCodeAt(++c)&1023),b.push(240|d>>18,128|d>>12&63,128|d>>6&63,128|d&63))}return new Uint8Array(b)}}
function zi(a){if(a===""||a==="e0")return Promise.resolve(a);var b;if((b=z.crypto)==null?0:b.subtle){if(xi.test(a))return Promise.resolve(a);try{var c=yi(a);return z.crypto.subtle.digest("SHA-256",c).then(function(d){var e=Array.from(new Uint8Array(d)).map(function(f){return String.fromCharCode(f)}).join("");return z.btoa(e).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}).catch(function(){return"e2"})}catch(d){return Promise.resolve("e2")}}else return Promise.resolve("e1")};var Ai={sl:'100',tl:'100',vl:'1000',im:'102509682~102788824~102803279~102813109~102887800~102926062~102975949~103016951~103021830~103027016~103047562~103050889~103051953'},Bi={fi:Number(Ai.sl)||0,lf:Number(Ai.tl)||0,Fm:Number(Ai.vl)||0,io:Ai.im};function O(a){Xa("GTM",a)};
var Fi=function(a,b){var c=["tv.1"],d=Ci(a);if(d)return c.push(d),{La:!1,Ni:c.join("~"),Lf:{}};var e={},f=0;var g=Di(a,function(p,q,r){var v=p.value,u;if(r){var t=q+"__"+f++;u="${userData."+t+"|sha256}";e[t]=v}else u=encodeURIComponent(encodeURIComponent(v));var w;c.push(""+q+((w=p.index)!=null?w:"")+"."+u)}).La;I(63)||(g=f>0);var k=c.join("~"),m={userData:e},n=b===2;return b===1||n?{La:g,Ni:k,Lf:m,Em:n?
"tv.9~${"+(k+"|encryptRsa}"):"tv.1~${"+(k+"|encrypt}"),encryptionKeyString:n?'MIIBojANBgkqhkiG9w0BAQEFAAOCAY8AMIIBigKCAYEAvMBNun6iQWLRC7leE+bbdzvSfi/vuWbUVnHQbRZGCQu9gU8gUhDTQvTCJ6vIl+PvFNutjUQo3svAxeWk9LyQdMWml3w8hLNKy2oaiCBwi5xPmpzrCWeYG4JaGpBom2PAojrRZdzNnrtutX5XvkcQ1ao/Z8CtYrC6cf9bhdVn46zTQaOBS2uokc4ihM9s0p3yESKcdaihK0wlFie0XvNwp/wR4mKlIwWOfDfnz3QUVDJiuFirBjZNoYsa3TmRRaJA3iih9I1fVwh4p7RSXHg6a+8ERQlJxx6HNm+GBh4VhzPwfRXGQX6sCVLVpbF9N/jr3DbE08lghW07/soO4Lq8IOWmaoo0kGvWwebbXSx9UpPCofGxXrbrDbuKaoFrrtnmqBsiaVOHxcg07N23bnxv9NfgjIuUBGaR2vykgWvWqViN3yrfAHmhXurjQtFu/csE8W95D3yP7a9rywXpELv047MSD+YthoXxGQmSOB4A1SG3SmJgbs8Ee8x/JBmBOylTAgMBAAE\x3d':Ei()}:{La:g,Ni:k,Lf:m}},Hi=function(a){if(!(a!=null&&Object.keys(a).length>0))return!1;var b=Gi(a);return Di(b,function(){}).La},Di=function(a,b){b=b===void 0?function(){}:b;for(var c=!1,d=!1,e=l(a),f=e.next();!f.done;f=e.next()){var g=f.value;if(g.value){var k=Ii[g.name];if(k){var m=Ji(g);m&&(c=!0);d=!0;b(g,k,m)}}}return{La:d,ni:c}},Ji=function(a){var b=Ki.indexOf(a.name)!==
-1,c=/^e\d+$/.test(a.value),d;if(d=b&&!c){var e=a.value;d=!(Li.test(e)||xi.test(e))}return d},Ei=function(){return'{\x22keys\x22:[{\x22hpkePublicKey\x22:{\x22params\x22:{\x22aead\x22:\x22AES_128_GCM\x22,\x22kdf\x22:\x22HKDF_SHA256\x22,\x22kem\x22:\x22DHKEM_P256_HKDF_SHA256\x22},\x22publicKey\x22:\x22BBuBYcz4oB+th/VckzFr3BfO9LmmBTr970aSFgTv1YgRb5vOhAlhowSzpNq31ta3YfgChEYy0kDk6FC0MsXwBt8\x3d\x22,\x22version\x22:0},\x22id\x22:\x22f88e2484-697a-4c9a-90c0-cba6a1ea30e2\x22},{\x22hpkePublicKey\x22:{\x22params\x22:{\x22aead\x22:\x22AES_128_GCM\x22,\x22kdf\x22:\x22HKDF_SHA256\x22,\x22kem\x22:\x22DHKEM_P256_HKDF_SHA256\x22},\x22publicKey\x22:\x22BPi8tdu86MDUL6a+RYcd8Ma8+pA76B1aJmYBBE7ak1RPmcvhDPua3364afXXSz51BetB/DutFSesxbOODZN+DYg\x3d\x22,\x22version\x22:0},\x22id\x22:\x22abc582df-1b8d-440d-a408-9eb31793cdd8\x22},{\x22hpkePublicKey\x22:{\x22params\x22:{\x22aead\x22:\x22AES_128_GCM\x22,\x22kdf\x22:\x22HKDF_SHA256\x22,\x22kem\x22:\x22DHKEM_P256_HKDF_SHA256\x22},\x22publicKey\x22:\x22BHoopgrwqmbHrzQMjJAkwT4nlE/5FvGkifzrfjCI0hQrOQpatpwRbqIOWbY7xtLTjOJ3cnf4fku3FvtVzlk7zzw\x3d\x22,\x22version\x22:0},\x22id\x22:\x229d3a7140-5fd2-4550-9869-f84609bd2696\x22},{\x22hpkePublicKey\x22:{\x22params\x22:{\x22aead\x22:\x22AES_128_GCM\x22,\x22kdf\x22:\x22HKDF_SHA256\x22,\x22kem\x22:\x22DHKEM_P256_HKDF_SHA256\x22},\x22publicKey\x22:\x22BMnDyi6C7Lqmu8h7ErRYOy93W5Ukv+hRnl1ynxFuGpqoMTcQRU4wogF0Uc1Gd5PSrobtW4ShDPnjg/yLE+lbfRM\x3d\x22,\x22version\x22:0},\x22id\x22:\x220cbf34cf-eb7e-4648-84d0-dc06543fd827\x22},{\x22hpkePublicKey\x22:{\x22params\x22:{\x22aead\x22:\x22AES_128_GCM\x22,\x22kdf\x22:\x22HKDF_SHA256\x22,\x22kem\x22:\x22DHKEM_P256_HKDF_SHA256\x22},\x22publicKey\x22:\x22BOnnymipKUyckstkfGymPEHIvWjhvMnR896/925/5kQKycEjszdAoiTC88Ko0jlIsWaSUro1kXDr41y6Ytc3WdY\x3d\x22,\x22version\x22:0},\x22id\x22:\x2203f71530-0e48-4a7b-9d67-3d741a43ada9\x22}]}'},Oi=function(a){if(z.Promise){var b=void 0;return b}},Si=function(a,b,c){if(z.Promise)try{var d=Gi(a),e=Pi(d).then(Qi);return e}catch(k){}},Ni=function(a,b){var c=void 0;
return c},Qi=function(a){var b=a.Yd,c=a.time,d=["tv.1"],e=Ci(b);if(e)return d.push(e),{Za:encodeURIComponent(d.join("~")),ni:!1,La:!1,time:c,mi:!0};var f=b.filter(function(n){return!Ji(n)}),g=Di(f,function(n,p){var q=n.value,r=n.index;r!==void 0&&(p+=r);d.push(p+"."+q)}),k=g.ni,m=g.La;return{Za:encodeURIComponent(d.join("~")),ni:k,La:m,time:c,mi:!1}},Ci=function(a){if(a.length===1&&a[0].name==="error_code")return Ii.error_code+"."+a[0].value},Ri=function(a){if(a.length===1&&a[0].name==="error_code")return!1;
for(var b=l(a),c=b.next();!c.done;c=b.next()){var d=c.value;if(Ii[d.name]&&d.value)return!0}return!1},Gi=function(a){function b(r,v,u,t){var w=Ti(r);w!==""&&(xi.test(w)?k.push({name:v,value:w,index:t}):k.push({name:v,value:u(w),index:t}))}function c(r,v){var u=r;if(bb(u)||Array.isArray(u)){u=eb(r);for(var t=0;t<u.length;++t){var w=Ti(u[t]),x=xi.test(w);v&&!x&&O(89);!v&&x&&O(88)}}}function d(r,v){var u=r[v];c(u,!1);var t=Ui[v];r[t]&&(r[v]&&O(90),u=r[t],c(u,!0));return u}function e(r,v,u){for(var t=
eb(d(r,v)),w=0;w<t.length;++w)b(t[w],v,u)}function f(r,v,u,t){var w=d(r,v);b(w,v,u,t)}function g(r){return function(v){O(64);return r(v)}}var k=[];if(z.location.protocol!=="https:")return k.push({name:"error_code",value:"e3",index:void 0}),k;e(a,"email",Vi);e(a,"phone_number",Wi);e(a,"first_name",g(Xi));e(a,"last_name",g(Xi));var m=a.home_address||{};e(m,"street",g(Yi));e(m,"city",g(Yi));e(m,"postal_code",g(Zi));e(m,"region",g(Yi));e(m,"country",g(Zi));for(var n=eb(a.address||{}),p=0;p<n.length;p++){var q=
n[p];f(q,"first_name",Xi,p);f(q,"last_name",Xi,p);f(q,"street",Yi,p);f(q,"city",Yi,p);f(q,"postal_code",Zi,p);f(q,"region",Yi,p);f(q,"country",Zi,p)}return k},$i=function(a){var b=a?Gi(a):[];return Qi({Yd:b})},aj=function(a){return a&&a!=null&&Object.keys(a).length>0&&z.Promise?Gi(a).some(function(b){return b.value&&Ki.indexOf(b.name)!==-1&&!xi.test(b.value)}):!1},Ti=function(a){return a==null?"":bb(a)?ob(String(a)):"e0"},Zi=function(a){return a.replace(bj,"")},Xi=function(a){return Yi(a.replace(/\s/g,
""))},Yi=function(a){return ob(a.replace(cj,"").toLowerCase())},Wi=function(a){a=a.replace(/[\s-()/.]/g,"");a.charAt(0)!=="+"&&(a="+"+a);return dj.test(a)?a:"e0"},Vi=function(a){var b=a.toLowerCase().split("@");if(b.length===2){var c=b[0];/^(gmail|googlemail)\./.test(b[1])&&(c=c.replace(/\./g,""));c=c+"@"+b[1];if(ej.test(c))return c}return"e0"},Pi=function(a){if(!a.some(function(c){return c.value&&Ki.indexOf(c.name)!==-1}))return Promise.resolve({Yd:a});if(!z.Promise)return Promise.resolve({Yd:[]});
var b;I(60)&&(b=Kc());return Promise.all(a.map(function(c){return c.value&&Ki.indexOf(c.name)!==-1?zi(c.value).then(function(d){c.value=d}):Promise.resolve()})).then(function(){var c={Yd:a};if(b!==void 0){var d=Kc();b&&d&&(c.time=Math.round(d)-Math.round(b))}return c}).catch(function(){return{Yd:[]}})},cj=/[0-9`~!@#$%^&*()_\-+=:;<>,.?|/\\[\]]/g,ej=/^\S+@\S+\.\S+$/,dj=/^\+\d{10,15}$/,bj=/[.~]/g,Li=/^[0-9A-Za-z_-]{43}$/,fj={},Ii=(fj.email="em",fj.phone_number="pn",fj.first_name="fn",fj.last_name="ln",
fj.street="sa",fj.city="ct",fj.region="rg",fj.country="co",fj.postal_code="pc",fj.error_code="ec",fj),gj={},Ui=(gj.email="sha256_email_address",gj.phone_number="sha256_phone_number",gj.first_name="sha256_first_name",gj.last_name="sha256_last_name",gj.street="sha256_street",gj);var Ki=Object.freeze(["email","phone_number","first_name","last_name","street"]);var hj={},ij=(hj[M.m.Ea]=1,hj[M.m.Uc]=2,hj[M.m.Jb]=2,hj[M.m.ka]=3,hj[M.m.zd]=4,hj[M.m.me]=5,hj[M.m.Vb]=6,hj[M.m.Ja]=6,hj[M.m.Ra]=6,hj[M.m.sc]=6,hj[M.m.ob]=6,hj[M.m.Xa]=6,hj[M.m.Sa]=7,hj[M.m.rb]=9,hj[M.m.oe]=10,hj[M.m.fb]=11,hj),jj={},kj=(jj.unknown=13,jj.standard=14,jj.unique=15,jj.per_session=16,jj.transactions=17,jj.items_sold=18,jj);var lj=[];function mj(a,b){b=b===void 0?!1:b;for(var c=Object.keys(a),d=l(Object.keys(ij)),e=d.next();!e.done;e=d.next()){var f=e.value;if(c.includes(f)){var g=ij[f],k=b;k=k===void 0?!1:k;Xa("GTAG_EVENT_FEATURE_CHANNEL",g);k&&(lj[g]=!0)}}};var nj=function(){this.C=new Set},pj=function(a){var b=oj.jb;a=a===void 0?[]:a;return Array.from(b.C).concat(a)},qj=function(){var a=oj.jb,b=Bi.io;a.C=new Set;if(b!=="")for(var c=l(b.split("~")),d=c.next();!d.done;d=c.next()){var e=Number(d.value);isNaN(e)||a.C.add(e)}};var rj={Kh:"5490"};rj.Jh=Number("2")||0;rj.zb="dataLayer";rj.ko="ChAI8LXYvwYQz7WckNq7r5YfEiUAh80uAl/jcaSdOhxpBUdYBVkH4ILJfFsbUqHwEo6PNLd1RaRRGgK+dw\x3d\x3d";var sj={__cl:1,__ecl:1,__ehl:1,__evl:1,__fal:1,__fil:1,__fsl:1,__hl:1,__jel:1,__lcl:1,__sdl:1,__tl:1,__ytl:1},tj={__paused:1,__tg:1},uj;for(uj in sj)sj.hasOwnProperty(uj)&&(tj[uj]=1);var vj=mb("true"),wj=!1,xj,yj=!1;yj=!0;xj=yj;var zj,Aj=!1;zj=Aj;var Bj,Cj=!1;Bj=Cj;rj.Qf="www.googletagmanager.com";var Dj=""+rj.Qf+(xj?"/gtag/js":"/gtm.js"),Ej=null,Fj=null,Gj={},Hj={};rj.rl="true";var Ij="";rj.Lh=Ij;
var oj=new function(){this.jb=new nj;this.C=!1;this.H=0;this.ba=this.sa=this.Bc=this.O="";this.U=this.N=!1};function Jj(a){a=a===void 0?[]:a;return pj(a).join("~")}function Kj(){var a=oj.O.length;return oj.O[a-1]==="/"?oj.O.substring(0,a-1):oj.O}function Lj(){return oj.C?I(82)?oj.H===0:oj.H!==1:!1}function Mj(a){for(var b={},c=l(a.split("|")),d=c.next();!d.done;d=c.next())b[d.value]=!0;return b};var Nj=new ib,Oj={},Pj={},Sj={name:rj.zb,set:function(a,b){Zc(xb(a,b),Oj);Qj()},get:function(a){return Rj(a,2)},reset:function(){Nj=new ib;Oj={};Qj()}};function Rj(a,b){return b!=2?Nj.get(a):Tj(a)}function Tj(a,b){var c=a.split(".");b=b||[];for(var d=Oj,e=0;e<c.length;e++){if(d===null)return!1;if(d===void 0)break;d=d[c[e]];if(b.indexOf(d)!==-1)return}return d}function Uj(a,b){Pj.hasOwnProperty(a)||(Nj.set(a,b),Zc(xb(a,b),Oj),Qj())}
function Vj(){for(var a=["gtm.allowlist","gtm.blocklist","gtm.whitelist","gtm.blacklist","tagTypeBlacklist"],b=0;b<a.length;b++){var c=a[b],d=Rj(c,1);if(Array.isArray(d)||Yc(d))d=Zc(d,null);Pj[c]=d}}function Qj(a){jb(Pj,function(b,c){Nj.set(b,c);Zc(xb(b),Oj);Zc(xb(b,c),Oj);a&&delete Pj[b]})}function Wj(a,b){var c,d=(b===void 0?2:b)!==1?Tj(a):Nj.get(a);Wc(d)==="array"||Wc(d)==="object"?c=Zc(d,null):c=d;return c};
var Xj=function(a,b,c){if(!c)return!1;for(var d=String(c.value),e,f=d.replace(/\["?'?/g,".").replace(/"?'?\]/g,"").split(","),g=0;g<f.length;g++){var k=f[g].trim();if(k&&!vb(k,"#")&&!vb(k,".")){if(vb(k,"dataLayer."))e=Rj(k.substring(10));else{var m=k.split(".");e=z[m.shift()];for(var n=0;n<m.length;n++)e=e&&e[m[n]];I(59)&&e===void 0&&(e=Rj(k))}if(e!==void 0)break}}if(e===void 0&&si)try{var p=ri(d);if(p&&p.length>0){e=[];for(var q=0;q<p.length&&q<(b==="email"||b==="phone_number"?5:1);q++)e.push(Ac(p[q])||
ob(p[q].value));e=e.length===1?e[0]:e}}catch(r){O(149)}return e?(a[b]=e,!0):!1},Yj=function(a){if(a){var b={},c=!1;c=Xj(b,"email",a.email)||c;c=Xj(b,"phone_number",a.phone)||c;b.address=[];for(var d=a.name_and_address||[],e=0;e<d.length;e++){var f={};c=Xj(f,"first_name",d[e].first_name)||c;c=Xj(f,"last_name",d[e].last_name)||c;c=Xj(f,"street",d[e].street)||c;c=Xj(f,"city",d[e].city)||c;c=Xj(f,"region",d[e].region)||c;c=Xj(f,"country",d[e].country)||c;c=Xj(f,"postal_code",d[e].postal_code)||c;b.address.push(f)}return c?
b:void 0}},Zj=function(a,b){switch(a.enhanced_conversions_mode){case "manual":if(b&&Yc(b))return b;var c=a.enhanced_conversions_manual_var;if(c!==void 0)return c;var d=z.enhanced_conversion_data;d&&Xa("GTAG_EVENT_FEATURE_CHANNEL",8);return d;case "automatic":return Yj(a[M.m.hh])}},ak=function(a){return Yc(a)?!!a.enable_code:!1};var bk=/:[0-9]+$/,ck=/^\d+\.fls\.doubleclick\.net$/;function dk(a,b,c,d){for(var e=[],f=l(a.split("&")),g=f.next();!g.done;g=f.next()){var k=l(g.value.split("=")),m=k.next().value,n=sa(k);if(decodeURIComponent(m.replace(/\+/g," "))===b){var p=n.join("=");if(!c)return d?p:decodeURIComponent(p.replace(/\+/g," "));e.push(d?p:decodeURIComponent(p.replace(/\+/g," ")))}}return c?e:void 0}function ek(a){try{return decodeURIComponent(a)}catch(b){}}
function fk(a,b,c,d,e){b&&(b=String(b).toLowerCase());if(b==="protocol"||b==="port")a.protocol=gk(a.protocol)||gk(z.location.protocol);b==="port"?a.port=String(Number(a.hostname?a.port:z.location.port)||(a.protocol==="http"?80:a.protocol==="https"?443:"")):b==="host"&&(a.hostname=(a.hostname||z.location.hostname).replace(bk,"").toLowerCase());return hk(a,b,c,d,e)}
function hk(a,b,c,d,e){var f,g=gk(a.protocol);b&&(b=String(b).toLowerCase());switch(b){case "url_no_fragment":f=ik(a);break;case "protocol":f=g;break;case "host":f=a.hostname.replace(bk,"").toLowerCase();if(c){var k=/^www\d*\./.exec(f);k&&k[0]&&(f=f.substring(k[0].length))}break;case "port":f=String(Number(a.port)||(g==="http"?80:g==="https"?443:""));break;case "path":a.pathname||a.hostname||Xa("TAGGING",1);f=a.pathname.substring(0,1)==="/"?a.pathname:"/"+a.pathname;var m=f.split("/");(d||[]).indexOf(m[m.length-
1])>=0&&(m[m.length-1]="");f=m.join("/");break;case "query":f=a.search.replace("?","");e&&(f=dk(f,e,!1));break;case "extension":var n=a.pathname.split(".");f=n.length>1?n[n.length-1]:"";f=f.split("/")[0];break;case "fragment":f=a.hash.replace("#","");break;default:f=a&&a.href}return f}function gk(a){return a?a.replace(":","").toLowerCase():""}function ik(a){var b="";if(a&&a.href){var c=a.href.indexOf("#");b=c<0?a.href:a.href.substring(0,c)}return b}var jk={},kk=0;
function lk(a){var b=jk[a];if(!b){var c=A.createElement("a");a&&(c.href=a);var d=c.pathname;d[0]!=="/"&&(a||Xa("TAGGING",1),d="/"+d);var e=c.hostname.replace(bk,"");b={href:c.href,protocol:c.protocol,host:c.host,hostname:e,pathname:d,search:c.search,hash:c.hash,port:c.port};kk<5&&(jk[a]=b,kk++)}return b}function mk(a,b,c){var d=lk(a);return Cb(b,d,c)}
function nk(a){var b=lk(z.location.href),c=fk(b,"host",!1);if(c&&c.match(ck)){var d=fk(b,"path");if(d){var e=d.split(a+"=");if(e.length>1)return e[1].split(";")[0].split("?")[0]}}};var ok={"https://www.google.com":"/g","https://www.googleadservices.com":"/as","https://pagead2.googlesyndication.com":"/gs"},pk=["/as/d/ccm/conversion","/g/d/ccm/conversion","/gs/ccm/conversion","/d/ccm/form-data"];function qk(a,b){if(a){var c=""+a;c.indexOf("http://")!==0&&c.indexOf("https://")!==0&&(c="https://"+c);c[c.length-1]==="/"&&(c=c.substring(0,c.length-1));return lk(""+c+b).href}}function rk(a,b){if(Lj()||zj)return qk(a,b)}
function sk(){return!!rj.Lh&&rj.Lh.split("@@").join("")!=="SGTM_TOKEN"}function tk(a){for(var b=l([M.m.Uc,M.m.Jb]),c=b.next();!c.done;c=b.next()){var d=P(a,c.value);if(d)return d}}function uk(a,b,c){c=c===void 0?"":c;if(!Lj())return a;var d=b?ok[a]||"":"";d==="/gs"&&(c="");return""+Kj()+d+c}function vk(a){if(!Lj())return a;for(var b=l(pk),c=b.next();!c.done;c=b.next())if(vb(a,""+Kj()+c.value))return a+"&_uip="+encodeURIComponent("::");return a};function wk(a){var b=String(a[Te.xa]||"").replace(/_/g,"");return vb(b,"cvt")?"cvt":b}var xk=z.location.search.indexOf("?gtm_latency=")>=0||z.location.search.indexOf("&gtm_latency=")>=0;var yk={sampleRate:"0.005000",nl:"",ho:"0.01"},zk=Math.random(),Ak;if(!(Ak=xk)){var Bk=yk.sampleRate;Ak=zk<Number(Bk)}var Ck=Ak,Dk=(jc==null?void 0:jc.includes("gtm_debug=d"))||xk||zk>=1-Number(yk.ho);var Ek=function(a,b){var c=function(){};c.prototype=a.prototype;var d=new c;a.apply(d,Array.prototype.slice.call(arguments,1));return d},Fk=function(a){var b=a;return function(){if(b){var c=b;b=null;c()}}};var Gk=function(a,b,c){return a.addEventListener?(a.addEventListener(b,c,!1),!0):!1},Hk=function(a,b,c){a.removeEventListener&&a.removeEventListener(b,c,!1)};var Ik,Jk;a:{for(var Kk=["CLOSURE_FLAGS"],Lk=ya,Mk=0;Mk<Kk.length;Mk++)if(Lk=Lk[Kk[Mk]],Lk==null){Jk=null;break a}Jk=Lk}var Nk=Jk&&Jk[610401301];Ik=Nk!=null?Nk:!1;function Ok(){var a=ya.navigator;if(a){var b=a.userAgent;if(b)return b}return""}var Pk,Qk=ya.navigator;Pk=Qk?Qk.userAgentData||null:null;function Rk(a){if(!Ik||!Pk)return!1;for(var b=0;b<Pk.brands.length;b++){var c=Pk.brands[b].brand;if(c&&c.indexOf(a)!=-1)return!0}return!1}function Sk(a){return Ok().indexOf(a)!=-1};function Tk(){return Ik?!!Pk&&Pk.brands.length>0:!1}function Uk(){return Tk()?!1:Sk("Opera")}function Vk(){return Sk("Firefox")||Sk("FxiOS")}function Wk(){return Tk()?Rk("Chromium"):(Sk("Chrome")||Sk("CriOS"))&&!(Tk()?0:Sk("Edge"))||Sk("Silk")};var Xk=function(a){Xk[" "](a);return a};Xk[" "]=function(){};var Yk=function(a){return decodeURIComponent(a.replace(/\+/g," "))};var Zk=function(a,b,c,d){for(var e=b,f=c.length;(e=a.indexOf(c,e))>=0&&e<d;){var g=a.charCodeAt(e-1);if(g==38||g==63){var k=a.charCodeAt(e+f);if(!k||k==61||k==38||k==35)return e}e+=f+1}return-1},$k=/#|$/,al=function(a,b){var c=a.search($k),d=Zk(a,0,b,c);if(d<0)return null;var e=a.indexOf("&",d);if(e<0||e>c)e=c;d+=b.length+1;return Yk(a.slice(d,e!==-1?e:0))},bl=/[?&]($|#)/,cl=function(a,b,c){for(var d,e=a.search($k),f=0,g,k=[];(g=Zk(a,f,b,e))>=0;)k.push(a.substring(f,g)),f=Math.min(a.indexOf("&",g)+
1||e,e);k.push(a.slice(f));d=k.join("").replace(bl,"$1");var m,n=c!=null?"="+encodeURIComponent(String(c)):"";var p=b+n;if(p){var q,r=d.indexOf("#");r<0&&(r=d.length);var v=d.indexOf("?"),u;v<0||v>r?(v=r,u=""):u=d.substring(v+1,r);q=[d.slice(0,v),u,d.slice(r)];var t=q[1];q[1]=p?t?t+"&"+p:p:t;m=q[0]+(q[1]?"?"+q[1]:"")+q[2]}else m=d;return m};function dl(){return Ik?!!Pk&&!!Pk.platform:!1}function el(){return Sk("iPhone")&&!Sk("iPod")&&!Sk("iPad")}function fl(){el()||Sk("iPad")||Sk("iPod")};Uk();Tk()||Sk("Trident")||Sk("MSIE");Sk("Edge");!Sk("Gecko")||Ok().toLowerCase().indexOf("webkit")!=-1&&!Sk("Edge")||Sk("Trident")||Sk("MSIE")||Sk("Edge");Ok().toLowerCase().indexOf("webkit")!=-1&&!Sk("Edge")&&Sk("Mobile");dl()||Sk("Macintosh");dl()||Sk("Windows");(dl()?Pk.platform==="Linux":Sk("Linux"))||dl()||Sk("CrOS");dl()||Sk("Android");el();Sk("iPad");Sk("iPod");fl();Ok().toLowerCase().indexOf("kaios");var gl=function(a){try{var b;if(b=!!a&&a.location.href!=null)a:{try{Xk(a.foo);b=!0;break a}catch(c){}b=!1}return b}catch(c){return!1}},hl=function(a,b){if(a)for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(a[c],c,a)},il=function(a){if(z.top==z)return 0;if(a===void 0?0:a){var b=z.location.ancestorOrigins;if(b)return b[b.length-1]==z.location.origin?1:2}return gl(z.top)?1:2},jl=function(a){a=a===void 0?document:a;return a.createElement("img")},kl=function(){for(var a=z,b=a;a&&a!=a.parent;)a=
a.parent,gl(a)&&(b=a);return b};function ll(a){var b;b=b===void 0?document:b;var c;return!((c=b.featurePolicy)==null||!c.allowedFeatures().includes(a))};function ml(){return ll("join-ad-interest-group")&&ab(gc.joinAdInterestGroup)}
function nl(a,b,c){var d=jg[3]===void 0?1:jg[3],e='iframe[data-tagging-id="'+b+'"]',f=[];try{if(d===1){var g=A.querySelector(e);g&&(f=[g])}else f=Array.from(A.querySelectorAll(e))}catch(r){}var k;a:{try{k=A.querySelectorAll('iframe[allow="join-ad-interest-group"][data-tagging-id*="-"]');break a}catch(r){}k=void 0}var m=k,n=((m==null?void 0:m.length)||0)>=(jg[2]===void 0?50:jg[2]),p;if(p=f.length>=1){var q=Number(f[f.length-1].dataset.loadTime);q!==void 0&&qb()-q<(jg[1]===void 0?6E4:jg[1])?(Xa("TAGGING",
9),p=!0):p=!1}if(p)return!1;if(d===1)if(f.length>=1)ol(f[0]);else{if(n)return Xa("TAGGING",10),!1}else f.length>=d?ol(f[0]):n&&ol(m[0]);uc(a,c,{allow:"join-ad-interest-group"},{taggingId:b,loadTime:qb()});return!0}function ol(a){try{a.parentNode.removeChild(a)}catch(b){}}function pl(){return"https://td.doubleclick.net"};function ql(a,b,c){var d,e=a.GooglebQhCsO;e||(e={},a.GooglebQhCsO=e);d=e;if(d[b])return!1;d[b]=[];d[b][0]=c;return!0};var rl=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e<128?b[c++]=e:(e<2048?b[c++]=e>>6|192:((e&64512)==55296&&d+1<a.length&&(a.charCodeAt(d+1)&64512)==56320?(e=65536+((e&1023)<<10)+(a.charCodeAt(++d)&1023),b[c++]=e>>18|240,b[c++]=e>>12&63|128):b[c++]=e>>12|224,b[c++]=e>>6&63|128),b[c++]=e&63|128)}return b};Vk();el()||Sk("iPod");Sk("iPad");!Sk("Android")||Wk()||Vk()||Uk()||Sk("Silk");Wk();!Sk("Safari")||Wk()||(Tk()?0:Sk("Coast"))||Uk()||(Tk()?0:Sk("Edge"))||(Tk()?Rk("Microsoft Edge"):Sk("Edg/"))||(Tk()?Rk("Opera"):Sk("OPR"))||Vk()||Sk("Silk")||Sk("Android")||fl();var sl={},tl=null,ul=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e>255&&(b[c++]=e&255,e>>=8);b[c++]=e}var f=4;f===void 0&&(f=0);if(!tl){tl={};for(var g="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),k=["+/=","+/","-_=","-_.","-_"],m=0;m<5;m++){var n=g.concat(k[m].split(""));sl[m]=n;for(var p=0;p<n.length;p++){var q=n[p];tl[q]===void 0&&(tl[q]=p)}}}for(var r=sl[f],v=Array(Math.floor(b.length/3)),u=r[64]||"",t=0,w=0;t<b.length-2;t+=3){var x=b[t],
y=b[t+1],B=b[t+2],C=r[x>>2],E=r[(x&3)<<4|y>>4],H=r[(y&15)<<2|B>>6],N=r[B&63];v[w++]=""+C+E+H+N}var L=0,T=u;switch(b.length-t){case 2:L=b[t+1],T=r[(L&15)<<2]||u;case 1:var F=b[t];v[w]=""+r[F>>2]+r[(F&3)<<4|L>>4]+T+u}return v.join("")};function vl(a,b,c,d,e,f){var g=al(c,"fmt");if(d){var k=al(c,"random"),m=al(c,"label")||"";if(!k)return!1;var n=ul(Yk(m)+":"+Yk(k));if(!ql(a,n,d))return!1}g&&Number(g)!==4&&(c=cl(c,"rfmt",g));var p=cl(c,"fmt",4);sc(p,function(){a.google_noFurtherRedirects&&d&&(a.google_noFurtherRedirects=null,d())},e,f,b.getElementsByTagName("script")[0].parentElement||void 0);return!0};var wl={},xl=(wl[1]={},wl[2]={},wl[3]={},wl[4]={},wl);function yl(a,b,c){var d=zl(b,c);if(d){var e=xl[b][d];e||(e=xl[b][d]=[]);e.push(Object.assign({},a))}}function Al(a,b){var c=zl(a,b);if(c){var d=xl[a][c];d&&(xl[a][c]=d.filter(function(e){return!e.Uk}))}}function Bl(a){switch(a){case "script-src":case "script-src-elem":return 1;case "frame-src":return 4;case "connect-src":return 2;case "img-src":return 3}}
function zl(a,b){var c=b;if(b[0]==="/"){var d;c=((d=z.location)==null?void 0:d.origin)+b}try{var e=new URL(c);return a===4?e.origin:e.origin+e.pathname}catch(f){}}function Cl(a){var b=xa.apply(1,arguments);I(54)&&Dk&&(yl(a,2,b[0]),yl(a,3,b[0]));Dc.apply(null,ta(b))}function Dl(a){var b=xa.apply(1,arguments);I(54)&&Dk&&yl(a,2,b[0]);return Ec.apply(null,ta(b))}function El(a){var b=xa.apply(1,arguments);I(54)&&Dk&&yl(a,3,b[0]);vc.apply(null,ta(b))}
function Fl(a){var b=xa.apply(1,arguments),c=b[0];I(54)&&Dk&&(yl(a,2,c),yl(a,3,c));return Gc.apply(null,ta(b))}function Gl(a){var b=xa.apply(1,arguments);I(54)&&Dk&&yl(a,1,b[0]);sc.apply(null,ta(b))}function Hl(a){var b=xa.apply(1,arguments);b[0]&&I(54)&&Dk&&yl(a,4,b[0]);uc.apply(null,ta(b))}function Il(a){var b=xa.apply(1,arguments);I(54)&&Dk&&yl(a,1,b[2]);return vl.apply(null,ta(b))}function Jl(a){var b=xa.apply(1,arguments);I(54)&&Dk&&yl(a,4,b[0]);nl.apply(null,ta(b))};var Kl=/gtag[.\/]js/,Ll=/gtm[.\/]js/,Ml=!1;function Nl(a){if(Ml)return"1";var b,c=(b=a.scriptElement)==null?void 0:b.src;if(c){if(Kl.test(c))return"3";if(Ll.test(c))return"2"}return"0"};function Ol(a,b){var c=Pl();c.pending||(c.pending=[]);fb(c.pending,function(d){return d.target.ctid===a.ctid&&d.target.isDestination===a.isDestination})||c.pending.push({target:a,onLoad:b})}function Ql(){var a=z.google_tags_first_party;Array.isArray(a)||(a=[]);for(var b={},c=l(a),d=c.next();!d.done;d=c.next())b[d.value]=!0;return Object.freeze(b)}
var Rl=function(){this.container={};this.destination={};this.canonical={};this.pending=[];this.siloed=[];this.injectedFirstPartyContainers={};this.injectedFirstPartyContainers=Ql()};
function Pl(){var a=kc("google_tag_data",{}),b=a.tidr;b&&typeof b==="object"||(b=new Rl,a.tidr=b);var c=b;c.container||(c.container={});c.destination||(c.destination={});c.canonical||(c.canonical={});c.pending||(c.pending=[]);c.siloed||(c.siloed=[]);c.injectedFirstPartyContainers||(c.injectedFirstPartyContainers=Ql());return c};var Sl={},Tl=!1,Ul=void 0,Xf={ctid:"G-QDZY9PPZZ7",canonicalContainerId:"108270429",Nk:"G-QDZY9PPZZ7|GT-KDZTSW3",Ok:"G-QDZY9PPZZ7"};Sl.Xe=mb("");function Vl(){return Sl.Xe&&Wl().some(function(a){return a===Xf.ctid})}function Xl(){var a=Yl();return Tl?a.map(Zl):a}function $l(){var a=Wl();return Tl?a.map(Zl):a}
function am(){var a=$l();if(!Tl)for(var b=l([].concat(ta(a))),c=b.next();!c.done;c=b.next()){var d=Zl(c.value),e=Pl().destination[d];e&&e.state!==0||a.push(d)}return a}function bm(){return cm(Xf.ctid)}function dm(){return cm(Xf.canonicalContainerId||"_"+Xf.ctid)}function Yl(){return Xf.Nk?Xf.Nk.split("|"):[Xf.ctid]}function Wl(){return Xf.Ok?Xf.Ok.split("|"):[]}function em(){var a=fm(gm()),b=a&&a.parent;if(b)return fm(b)}
function fm(a){var b=Pl();return a.isDestination?b.destination[a.ctid]:b.container[a.ctid]}function cm(a){return Tl?Zl(a):a}function Zl(a){return"siloed_"+a}function hm(a){a=String(a);return vb(a,"siloed_")?a.substring(7):a}function im(){if(oj.N){var a=Pl();if(a.siloed){for(var b=[],c=Yl().map(Zl),d=Wl().map(Zl),e={},f=0;f<a.siloed.length;e={xg:void 0},f++)e.xg=a.siloed[f],!Tl&&fb(e.xg.isDestination?d:c,function(g){return function(k){return k===g.xg.ctid}}(e))?Tl=!0:b.push(e.xg);a.siloed=b}}}
function jm(){var a=Pl();if(a.pending){for(var b,c=[],d=!1,e=Xl(),f=Ul?Ul:am(),g={},k=0;k<a.pending.length;g={Gf:void 0},k++)g.Gf=a.pending[k],fb(g.Gf.target.isDestination?f:e,function(m){return function(n){return n===m.Gf.target.ctid}}(g))?d||(b=g.Gf.onLoad,d=!0):c.push(g.Gf);a.pending=c;if(b)try{b(dm())}catch(m){}}}
function km(){var a=Xf.ctid,b=Xl(),c=am();Ul=c;for(var d=function(n,p){var q={canonicalContainerId:Xf.canonicalContainerId,scriptContainerId:a,state:2,containers:b.slice(),destinations:c.slice()};ic&&(q.scriptElement=ic);jc&&(q.scriptSource=jc);if(em()===void 0){var r;a:{if((q.scriptContainerId||"").indexOf("GTM-")>=0){var v;b:{var u,t=(u=q.scriptElement)==null?void 0:u.src;if(t){for(var w=oj.C,x=lk(t),y=w?x.pathname:""+x.hostname+x.pathname,B=A.scripts,C="",E=0;E<B.length;++E){var H=B[E];if(!(H.innerHTML.length===
0||!w&&H.innerHTML.indexOf(q.scriptContainerId||"SHOULD_NOT_BE_SET")<0||H.innerHTML.indexOf(y)<0)){if(H.innerHTML.indexOf("(function(w,d,s,l,i)")>=0){v=String(E);break b}C=String(E)}}if(C){v=C;break b}}v=void 0}var N=v;if(N){Ml=!0;r=N;break a}}var L=[].slice.call(A.scripts);r=q.scriptElement?String(L.indexOf(q.scriptElement)):"-1"}q.htmlLoadOrder=r;q.loadScriptType=Nl(q)}var T=p?e.destination:e.container,F=T[n];F?(p&&F.state===0&&O(93),Object.assign(F,q)):T[n]=q},e=Pl(),f=l(b),g=f.next();!g.done;g=
f.next())d(g.value,!1);for(var k=l(c),m=k.next();!m.done;m=k.next())d(m.value,!0);e.canonical[dm()]={};jm()}function lm(){var a=dm();return!!Pl().canonical[a]}function mm(a){return!!Pl().container[a]}function nm(a){var b=Pl().destination[a];return!!b&&!!b.state}function gm(){return{ctid:bm(),isDestination:Sl.Xe}}function om(a,b,c){b.siloed&&pm({ctid:a,isDestination:!1});var d=gm();Pl().container[a]={state:1,context:b,parent:d};Ol({ctid:a,isDestination:!1},c)}
function pm(a){var b=Pl();(b.siloed=b.siloed||[]).push(a)}function qm(){var a=Pl().container,b;for(b in a)if(a.hasOwnProperty(b)&&a[b].state===1)return!0;return!1}function rm(){var a={};jb(Pl().destination,function(b,c){c.state===0&&(a[hm(b)]=c)});return a}function sm(a){return!!(a&&a.parent&&a.context&&a.context.source===1&&a.parent.ctid.indexOf("GTM-")!==0)}function tm(){for(var a=Pl(),b=l(Xl()),c=b.next();!c.done;c=b.next())if(a.injectedFirstPartyContainers[c.value])return!0;return!1}
function um(a){var b=Pl();return b.destination[a]?1:b.destination[Zl(a)]?2:0};function vm(){var a=kc("google_tag_data",{});return a.ics=a.ics||new wm}var wm=function(){this.entries={};this.waitPeriodTimedOut=this.wasSetLate=this.accessedAny=this.accessedDefault=this.usedImplicit=this.usedUpdate=this.usedDefault=this.usedDeclare=this.active=!1;this.C=[]};
wm.prototype.default=function(a,b,c,d,e,f,g){this.usedDefault||this.usedDeclare||!this.accessedDefault&&!this.accessedAny||(this.wasSetLate=!0);this.usedDefault=this.active=!0;Xa("TAGGING",19);b==null?Xa("TAGGING",18):xm(this,a,b==="granted",c,d,e,f,g)};wm.prototype.waitForUpdate=function(a,b,c){for(var d=0;d<a.length;d++)xm(this,a[d],void 0,void 0,"","",b,c)};
var xm=function(a,b,c,d,e,f,g,k){var m=a.entries,n=m[b]||{},p=n.region,q=d&&bb(d)?d.toUpperCase():void 0;e=e.toUpperCase();f=f.toUpperCase();if(e===""||q===f||(q===e?p!==f:!q&&!p)){var r=!!(g&&g>0&&n.update===void 0),v={region:q,declare_region:n.declare_region,implicit:n.implicit,default:c!==void 0?c:n.default,declare:n.declare,update:n.update,quiet:r};if(e!==""||n.default!==!1)m[b]=v;r&&z.setTimeout(function(){m[b]===v&&v.quiet&&(Xa("TAGGING",2),a.waitPeriodTimedOut=!0,a.clearTimeout(b,void 0,k),
a.notifyListeners())},g)}};h=wm.prototype;h.clearTimeout=function(a,b,c){var d=[a],e=c.delegatedConsentTypes,f;for(f in e)e.hasOwnProperty(f)&&e[f]===a&&d.push(f);var g=this.entries[a]||{},k=this.getConsentState(a,c);if(g.quiet){g.quiet=!1;for(var m=l(d),n=m.next();!n.done;n=m.next())ym(this,n.value)}else if(b!==void 0&&k!==b)for(var p=l(d),q=p.next();!q.done;q=p.next())ym(this,q.value)};
h.update=function(a,b,c){this.usedDefault||this.usedDeclare||this.usedUpdate||!this.accessedAny||(this.wasSetLate=!0);this.usedUpdate=this.active=!0;if(b!=null){var d=this.getConsentState(a,c),e=this.entries;(e[a]=e[a]||{}).update=b==="granted";this.clearTimeout(a,d,c)}};
h.declare=function(a,b,c,d,e){this.usedDeclare=this.active=!0;var f=this.entries,g=f[a]||{},k=g.declare_region,m=c&&bb(c)?c.toUpperCase():void 0;d=d.toUpperCase();e=e.toUpperCase();if(d===""||m===e||(m===d?k!==e:!m&&!k)){var n={region:g.region,declare_region:m,declare:b==="granted",implicit:g.implicit,default:g.default,update:g.update,quiet:g.quiet};if(d!==""||g.declare!==!1)f[a]=n}};
h.implicit=function(a,b){this.usedImplicit=!0;var c=this.entries,d=c[a]=c[a]||{};d.implicit!==!1&&(d.implicit=b==="granted")};
h.getConsentState=function(a,b){var c=this.entries,d=c[a]||{},e=d.update;if(e!==void 0)return e?1:2;if(b.usedContainerScopedDefaults){var f=b.containerScopedDefaults[a];if(f===3)return 1;if(f===2)return 2}else if(e=d.default,e!==void 0)return e?1:2;if(b==null?0:b.delegatedConsentTypes.hasOwnProperty(a)){var g=b.delegatedConsentTypes[a],k=c[g]||{};e=k.update;if(e!==void 0)return e?1:2;if(b.usedContainerScopedDefaults){var m=b.containerScopedDefaults[g];if(m===3)return 1;if(m===2)return 2}else if(e=
k.default,e!==void 0)return e?1:2}e=d.declare;if(e!==void 0)return e?1:2;e=d.implicit;return e!==void 0?e?3:4:0};h.addListener=function(a,b){this.C.push({consentTypes:a,Rd:b})};var ym=function(a,b){for(var c=0;c<a.C.length;++c){var d=a.C[c];Array.isArray(d.consentTypes)&&d.consentTypes.indexOf(b)!==-1&&(d.Pk=!0)}};wm.prototype.notifyListeners=function(a,b){for(var c=0;c<this.C.length;++c){var d=this.C[c];if(d.Pk){d.Pk=!1;try{d.Rd({consentEventId:a,consentPriorityId:b})}catch(e){}}}};var zm=!1,Am=!1,Bm={},Cm={delegatedConsentTypes:{},corePlatformServices:{},usedCorePlatformServices:!1,selectedAllCorePlatformServices:!1,containerScopedDefaults:(Bm.ad_storage=1,Bm.analytics_storage=1,Bm.ad_user_data=1,Bm.ad_personalization=1,Bm),usedContainerScopedDefaults:!1};function Dm(a){var b=vm();b.accessedAny=!0;return(bb(a)?[a]:a).every(function(c){switch(b.getConsentState(c,Cm)){case 1:case 3:return!0;case 2:case 4:return!1;default:return!0}})}
function Em(a){var b=vm();b.accessedAny=!0;return b.getConsentState(a,Cm)}function Fm(a){for(var b={},c=l(a),d=c.next();!d.done;d=c.next()){var e=d.value;b[e]=Cm.corePlatformServices[e]!==!1}return b}function Gm(a){var b=vm();b.accessedAny=!0;return!(b.entries[a]||{}).quiet}
function Hm(){if(!kg(8))return!1;var a=vm();a.accessedAny=!0;if(a.active)return!0;if(!Cm.usedContainerScopedDefaults)return!1;for(var b=l(Object.keys(Cm.containerScopedDefaults)),c=b.next();!c.done;c=b.next())if(Cm.containerScopedDefaults[c.value]!==1)return!0;return!1}function Im(a,b){vm().addListener(a,b)}function Jm(a,b){vm().notifyListeners(a,b)}
function Km(a,b){function c(){for(var e=0;e<b.length;e++)if(!Gm(b[e]))return!0;return!1}if(c()){var d=!1;Im(b,function(e){d||c()||(d=!0,a(e))})}else a({})}
function Lm(a,b){function c(){for(var k=[],m=0;m<e.length;m++){var n=e[m];Dm(n)&&!f[n]&&k.push(n)}return k}function d(k){for(var m=0;m<k.length;m++)f[k[m]]=!0}var e=bb(b)?[b]:b,f={},g=c();g.length!==e.length&&(d(g),Im(e,function(k){function m(q){q.length!==0&&(d(q),k.consentTypes=q,a(k))}var n=c();if(n.length!==0){var p=Object.keys(f).length;n.length+p>=e.length?m(n):z.setTimeout(function(){m(c())},500)}}))};var Mm={},Nm=(Mm[0]=0,Mm[1]=0,Mm[2]=0,Mm[3]=0,Mm),Om=function(a,b){this.C=a;this.consentTypes=b};Om.prototype.isConsentGranted=function(){switch(this.C){case 0:return this.consentTypes.every(function(a){return Dm(a)});case 1:return this.consentTypes.some(function(a){return Dm(a)});default:Zb(this.C,"consentsRequired had an unknown type")}};var Pm={},Qm=(Pm[0]=new Om(0,[]),Pm[1]=new Om(0,["ad_storage"]),Pm[2]=new Om(0,["analytics_storage"]),Pm[3]=new Om(1,["ad_storage","analytics_storage"]),Pm);var Sm=function(a){var b=this;this.type=a;this.C=[];Im(Qm[a].consentTypes,function(){Rm(b)||b.flush()})};Sm.prototype.flush=function(){for(var a=l(this.C),b=a.next();!b.done;b=a.next()){var c=b.value;c()}this.C=[]};var Rm=function(a){return Nm[a.type]===2&&!Qm[a.type].isConsentGranted()},Tm=function(a,b){Rm(a)?a.C.push(b):b()},Um=new Map;function Vm(a){Um.has(a)||Um.set(a,new Sm(a));return Um.get(a)};var Wm="/td?id="+Xf.ctid,Xm="v t pid dl tdp exp".split(" "),Ym=["mcc"],Zm={},$m={},an=!1;function bn(a,b,c){$m[a]=b;(c===void 0||c)&&cn(a)}function cn(a,b){if(Zm[a]===void 0||(b===void 0?0:b))Zm[a]=!0}function dn(a){a=a===void 0?!1:a;var b=Object.keys(Zm).filter(function(c){return Zm[c]===!0&&$m[c]!==void 0&&(a||!Ym.includes(c))}).map(function(c){var d=$m[c];typeof d==="function"&&(d=d());return d?"&"+c+"="+d:""}).join("");return""+uk("https://www.googletagmanager.com")+Wm+(""+b+"&z=0")}
function en(){Object.keys(Zm).forEach(function(a){Xm.indexOf(a)<0&&(Zm[a]=!1)})}function fn(a){a=a===void 0?!1:a;if(oj.U&&Dk&&Xf.ctid){if(I(106)){var b=Vm(3);if(Rm(b)){an||(an=!0,Tm(b,fn));return}}var c=dn(a),d={destinationId:Xf.ctid,endpoint:56};a?Fl(d,c):El(d,c);en();an=!1}}var gn={};function hn(){Object.keys(Zm).filter(function(a){return Zm[a]&&!Xm.includes(a)}).length>0&&fn(!0)}var jn=gb();function kn(){jn=gb()}
function ln(){bn("v","3");bn("t","t");bn("pid",function(){return String(jn)});I(58)&&bn("exp",Jj());xc(z,"pagehide",hn);z.setInterval(kn,864E5)};var mn=["ad_storage","analytics_storage","ad_user_data","ad_personalization"],nn=[M.m.Uc,M.m.Jb,M.m.Nc,M.m.mb,M.m.Ib,M.m.Ca,M.m.Ba,M.m.Ja,M.m.Ra,M.m.ob],on=!1,pn=!1,qn={},rn={};function sn(){!pn&&on&&(mn.some(function(a){return Cm.containerScopedDefaults[a]!==1})||tn("mbc"));pn=!0}function tn(a){Dk&&(bn(a,"1"),fn())}function un(a,b){if(!qn[b]&&(qn[b]=!0,rn[b]))for(var c=l(nn),d=c.next();!d.done;d=c.next())if(a.hasOwnProperty(d.value)){tn("erc");break}};function vn(a){Xa("HEALTH",a)};var wn={nk:"service_worker_endpoint",Mh:"shared_user_id",Nh:"shared_user_id_requested",cf:"shared_user_id_source",Pf:"cookie_deprecation_label",ol:"aw_user_data_cache",Rl:"ga4_user_data_cache",Ql:"fl_user_data_cache",hk:"pt_listener_set",af:"pt_data",Hh:"ip_geo_fetch_in_progress",pg:"ip_geo_data_cache"},xn;function yn(a){if(!xn){xn={};for(var b=l(Object.keys(wn)),c=b.next();!c.done;c=b.next())xn[wn[c.value]]=!0}return!!xn[a]}
function zn(a,b){b=b===void 0?!1:b;if(yn(a)){var c,d,e=(d=(c=kc("google_tag_data",{})).xcd)!=null?d:c.xcd={};if(e[a])return e[a];if(b){var f=void 0,g=1,k={},m={set:function(n){f=n;m.notify()},get:function(){return f},subscribe:function(n){k[String(g)]=n;return g++},unsubscribe:function(n){var p=String(n);return k.hasOwnProperty(p)?(delete k[p],!0):!1},notify:function(){for(var n=l(Object.keys(k)),p=n.next();!p.done;p=n.next()){var q=p.value;try{k[q](a,f)}catch(r){}}}};return e[a]=m}}}
function An(a,b){var c=zn(a,!0);c&&c.set(b)}function Bn(a){var b;return(b=zn(a))==null?void 0:b.get()}function Cn(a,b){if(typeof b==="function"){var c;return(c=zn(a,!0))==null?void 0:c.subscribe(b)}}function Dn(a,b){var c=zn(a);return c?c.unsubscribe(b):!1};var En={Rm:"eyIwIjoiQ04iLCIxIjoiQ04tMTEiLCIyIjp0cnVlLCIzIjoiZ29vZ2xlLmNuIiwiNCI6IiIsIjUiOnRydWUsIjYiOmZhbHNlLCI3IjoiYWRfc3RvcmFnZXxhbmFseXRpY3Nfc3RvcmFnZXxhZF91c2VyX2RhdGF8YWRfcGVyc29uYWxpemF0aW9uIn0"},Fn={};function Gn(){function a(){c!==void 0&&Dn(wn.pg,c);b()}var b=Hn,c=void 0,d=Bn(wn.pg);d?a(d):(c=Cn(wn.pg,a),In())}
function In(){function a(c){An(wn.pg,c||"{}");An(wn.Hh,!1)}if(!Bn(wn.Hh)){An(wn.Hh,!0);var b="";z.fetch(b,{method:"GET",cache:"no-store",mode:"cors",credentials:"omit"}).then(function(c){c.ok?c.text().then(function(d){a(d)},function(){a()}):a()},function(){a()})}}
function Jn(){var a=En.Rm;try{return JSON.parse(Va(a))}catch(b){return O(123),vn(2),{}}}function Kn(){return Fn["0"]||""}function Ln(){return Fn["1"]||""}function Mn(){var a=!1;a=!!Fn["2"];return a}function Nn(){return Fn["6"]!==!1}function On(){var a="";a=Fn["4"]||"";return a}
function Pn(){var a=!1;a=!!Fn["5"];return a}function Qn(){var a="";a=Fn["3"]||"";return a};function Rn(a){return a&&a.indexOf("pending:")===0?Sn(a.substr(8)):!1}function Sn(a){if(a==null||a.length===0)return!1;var b=Number(a),c=qb();return b<c+3E5&&b>c-9E5};var Tn=!1,Un=!1,Vn=!1,Wn=0,Xn=!1,Yn=[];function Zn(a){if(Wn===0)Xn&&Yn&&(Yn.length>=100&&Yn.shift(),Yn.push(a));else if($n()){var b=kc('google.tagmanager.ta.prodqueue',[]);b.length>=50&&b.shift();b.push(a)}}function ao(){bo();yc(A,"TAProdDebugSignal",ao)}function bo(){if(!Un){Un=!0;co();var a=Yn;Yn=void 0;a==null||a.forEach(function(b){Zn(b)})}}
function co(){var a=A.documentElement.getAttribute("data-tag-assistant-prod-present");Sn(a)?Wn=1:!Rn(a)||Tn||Vn?Wn=2:(Vn=!0,xc(A,"TAProdDebugSignal",ao,!1),z.setTimeout(function(){bo();Tn=!0},200))}function $n(){if(!Xn)return!1;switch(Wn){case 1:case 0:return!0;case 2:return!1;default:return!1}};var eo=!1;function fo(a,b){var c=Yl(),d=Wl();if($n()){var e=go("INIT");e.containerLoadSource=a!=null?a:0;b&&(e.parentTargetReference=b);e.aliases=c;e.destinations=d;Zn(e)}}function ho(a){var b,c,d,e;b=a.targetId;c=a.request;d=a.Ka;e=a.isBatched;if($n()){var f=go("GTAG_HIT",{eventId:d.eventId,priorityId:d.priorityId});f.target=b;f.url=c.url;c.postBody&&(f.postBody=c.postBody);f.parameterEncoding=c.parameterEncoding;f.endpoint=c.endpoint;e!==void 0&&(f.isBatched=e);Zn(f)}}
function io(a){$n()&&ho(a())}function go(a,b){b=b===void 0?{}:b;b.groupId=jo;var c,d=b,e={publicId:ko};d.eventId!=null&&(e.eventId=d.eventId);d.priorityId!=null&&(e.priorityId=d.priorityId);d.eventName&&(e.eventName=d.eventName);d.groupId&&(e.groupId=d.groupId);d.tagName&&(e.tagName=d.tagName);c={containerProduct:"GTM",key:e,version:'1',messageType:a};c.containerProduct=eo?"OGT":"GTM";c.key.targetRef=lo;return c}var ko="",lo={ctid:"",isDestination:!1},jo;
function mo(a){var b=Xf.ctid,c=Vl();Wn=0;Xn=!0;co();jo=a;ko=b;eo=xj;lo={ctid:b,isDestination:c}};var no=[M.m.R,M.m.X,M.m.T,M.m.za],oo,po;function qo(a){for(var b=a[M.m.yb],c=Array.isArray(b)?b:[b],d={wf:0};d.wf<c.length;d={wf:d.wf},++d.wf)jb(a,function(e){return function(f,g){if(f!==M.m.yb){var k=c[e.wf],m=Kn(),n=Ln();Am=!0;zm&&Xa("TAGGING",20);vm().declare(f,g,k,m,n)}}}(d))}
function ro(a){sn();!po&&oo&&tn("crc");po=!0;var b=a[M.m.yb];b&&O(40);var c=a[M.m.de];c&&O(41);for(var d=Array.isArray(b)?b:[b],e={xf:0};e.xf<d.length;e={xf:e.xf},++e.xf)jb(a,function(f){return function(g,k){if(g!==M.m.yb&&g!==M.m.de){var m=d[f.xf],n=Number(c),p=Kn(),q=Ln();n=n===void 0?0:n;zm=!0;Am&&Xa("TAGGING",20);vm().default(g,k,m,p,q,n,Cm)}}}(e))}
function so(a){Cm.usedContainerScopedDefaults=!0;var b=a[M.m.yb];if(b){var c=Array.isArray(b)?b:[b];if(!c.includes(Ln())&&!c.includes(Kn()))return}jb(a,function(d,e){switch(d){case "ad_storage":case "analytics_storage":case "ad_user_data":case "ad_personalization":break;default:return}Cm.usedContainerScopedDefaults=!0;Cm.containerScopedDefaults[d]=e==="granted"?3:2})}function to(a,b){sn();oo=!0;jb(a,function(c,d){zm=!0;Am&&Xa("TAGGING",20);vm().update(c,d,Cm)});Jm(b.eventId,b.priorityId)}
function uo(a){a.hasOwnProperty("all")&&(Cm.selectedAllCorePlatformServices=!0,jb(di,function(b){Cm.corePlatformServices[b]=a.all==="granted";Cm.usedCorePlatformServices=!0}));jb(a,function(b,c){b!=="all"&&(Cm.corePlatformServices[b]=c==="granted",Cm.usedCorePlatformServices=!0)})}function R(a){Array.isArray(a)||(a=[a]);return a.every(function(b){return Dm(b)})}function vo(a,b){Im(a,b)}function wo(a,b){Lm(a,b)}function xo(a,b){Km(a,b)}
function yo(){var a=[M.m.R,M.m.za,M.m.T];vm().waitForUpdate(a,500,Cm)}function zo(a){for(var b=l(a),c=b.next();!c.done;c=b.next()){var d=c.value;vm().clearTimeout(d,void 0,Cm)}Jm()}function Ao(){if(!Bj)for(var a=Nn()?Mj(oj.sa):Mj(oj.Bc),b=0;b<no.length;b++){var c=no[b],d=c,e=a[c]?"granted":"denied";vm().implicit(d,e)}};var Bo=!1,Co=[];function Do(){if(!Bo){Bo=!0;for(var a=Co.length-1;a>=0;a--)Co[a]();Co=[]}};var Eo=z.google_tag_manager=z.google_tag_manager||{};function Fo(a,b){return Eo[a]=Eo[a]||b()}function Go(){var a=bm(),b=Ho;Eo[a]=Eo[a]||b}function Io(){var a=rj.zb;return Eo[a]=Eo[a]||{}}function Jo(){var a=Eo.sequence||1;Eo.sequence=a+1;return a};function Ko(){if(Eo.pscdl!==void 0)Bn(wn.Pf)===void 0&&An(wn.Pf,Eo.pscdl);else{var a=function(c){Eo.pscdl=c;An(wn.Pf,c)},b=function(){a("error")};try{gc.cookieDeprecationLabel?(a("pending"),gc.cookieDeprecationLabel.getValue().then(a).catch(b)):a("noapi")}catch(c){b(c)}}};function Lo(a,b){b&&jb(b,function(c,d){typeof d!=="object"&&d!==void 0&&(a["1p."+c]=String(d))})};var Mo=/^(?:siloed_)?(?:AW|DC|G|GF|GT|HA|MC|UA)$/,No=/\s/;
function Oo(a,b){if(bb(a)){a=ob(a);var c=a.indexOf("-");if(!(c<0)){var d=a.substring(0,c);if(Mo.test(d)){var e=a.substring(c+1),f;if(b){var g=function(n){var p=n.indexOf("/");return p<0?[n]:[n.substring(0,p),n.substring(p+1)]};f=g(e);if(d==="DC"&&f.length===2){var k=g(f[1]);k.length===2&&(f[1]=k[0],f.push(k[1]))}}else{f=e.split("/");for(var m=0;m<f.length;m++)if(!f[m]||No.test(f[m])&&(d!=="AW"||m!==1))return}return{id:a,prefix:d,destinationId:d+"-"+f[0],ids:f}}}}}
function Po(a,b){for(var c={},d=0;d<a.length;++d){var e=Oo(a[d],b);e&&(c[e.id]=e)}Qo(c);var f=[];jb(c,function(g,k){f.push(k)});return f}function Qo(a){var b=[],c;for(c in a)if(a.hasOwnProperty(c)){var d=a[c];d.prefix==="AW"&&d.ids[Ro[1]]&&b.push(d.destinationId)}for(var e=0;e<b.length;++e)delete a[b[e]]}var So={},Ro=(So[0]=0,So[1]=1,So[2]=2,So[3]=0,So[4]=1,So[5]=0,So[6]=0,So[7]=0,So);var To=Number('')||500,Uo={},Yo={},Zo={initialized:11,complete:12,interactive:13},$o={},ap=Object.freeze(($o[M.m.Ta]=!0,$o)),bp=void 0;function cp(a,b){if(b.length&&Dk){var c;(c=Uo)[a]!=null||(c[a]=[]);Yo[a]!=null||(Yo[a]=[]);var d=b.filter(function(e){return!Yo[a].includes(e)});Uo[a].push.apply(Uo[a],ta(d));Yo[a].push.apply(Yo[a],ta(d));!bp&&d.length>0&&(cn("tdc",!0),bp=z.setTimeout(function(){fn();Uo={};bp=void 0},To))}}
function dp(a,b){var c={},d;for(d in b)b.hasOwnProperty(d)&&(c[d]=!0);for(var e in a)a.hasOwnProperty(e)&&(c[e]=!0);return c}
function ep(a,b,c,d){c=c===void 0?{}:c;d=d===void 0?"":d;if(a===b)return[];var e=function(r,v){var u;Wc(v)==="object"?u=v[r]:Wc(v)==="array"&&(u=v[r]);return u===void 0?ap[r]:u},f=dp(a,b),g;for(g in f)if(f.hasOwnProperty(g)){var k=(d?d+".":"")+g,m=e(g,a),n=e(g,b),p=Wc(m)==="object"||Wc(m)==="array",q=Wc(n)==="object"||Wc(n)==="array";if(p&&q)ep(m,n,c,k);else if(p||q||m!==n)c[k]=!0}return Object.keys(c)}
function fp(){bn("tdc",function(){bp&&(z.clearTimeout(bp),bp=void 0);var a=[],b;for(b in Uo)Uo.hasOwnProperty(b)&&a.push(b+"*"+Uo[b].join("."));return a.length?a.join("!"):void 0},!1)};var gp=function(a,b,c,d,e,f,g,k,m,n,p){this.eventId=a;this.priorityId=b;this.C=c;this.U=d;this.N=e;this.O=f;this.H=g;this.eventMetadata=k;this.onSuccess=m;this.onFailure=n;this.isGtmEvent=p},hp=function(a,b){var c=[];switch(b){case 3:c.push(a.C);c.push(a.U);c.push(a.N);c.push(a.O);c.push(a.H);break;case 2:c.push(a.C);break;case 1:c.push(a.U);c.push(a.N);c.push(a.O);c.push(a.H);break;case 4:c.push(a.C),c.push(a.U),c.push(a.N),c.push(a.O)}return c},P=function(a,b,c,d){for(var e=l(hp(a,d===void 0?3:
d)),f=e.next();!f.done;f=e.next()){var g=f.value;if(g[b]!==void 0)return g[b]}return c},ip=function(a){for(var b={},c=hp(a,4),d=l(c),e=d.next();!e.done;e=d.next())for(var f=Object.keys(e.value),g=l(f),k=g.next();!k.done;k=g.next())b[k.value]=1;return Object.keys(b)},jp=function(a,b,c){function d(n){Yc(n)&&jb(n,function(p,q){f=!0;e[p]=q})}var e={},f=!1,g=hp(a,c===void 0?3:c);g.reverse();for(var k=l(g),m=k.next();!m.done;m=k.next())d(m.value[b]);return f?e:void 0},kp=function(a){for(var b=[M.m.vd,M.m.pd,
M.m.rd,M.m.sd,M.m.ud,M.m.wd,M.m.xd],c=hp(a,3),d=l(c),e=d.next();!e.done;e=d.next()){for(var f=e.value,g={},k=!1,m=l(b),n=m.next();!n.done;n=m.next()){var p=n.value;f[p]!==void 0&&(g[p]=f[p],k=!0)}var q=k?g:void 0;if(q)return q}return{}},lp=function(a,b){this.eventId=a;this.priorityId=b;this.H={};this.U={};this.C={};this.N={};this.ba={};this.O={};this.eventMetadata={};this.isGtmEvent=!1;this.onSuccess=function(){};this.onFailure=function(){}},mp=function(a,b){a.H=b;return a},np=function(a,b){a.U=b;
return a},op=function(a,b){a.C=b;return a},pp=function(a,b){a.N=b;return a},qp=function(a,b){a.ba=b;return a},rp=function(a,b){a.O=b;return a},sp=function(a,b){a.eventMetadata=b||{};return a},tp=function(a,b){a.onSuccess=b;return a},up=function(a,b){a.onFailure=b;return a},vp=function(a,b){a.isGtmEvent=b;return a},wp=function(a){return new gp(a.eventId,a.priorityId,a.H,a.U,a.C,a.N,a.O,a.eventMetadata,a.onSuccess,a.onFailure,a.isGtmEvent)};var xp={ml:Number("5"),Po:Number("")},yp=[],zp=!1;function Ap(a){yp.push(a)}var Bp="?id="+Xf.ctid,Cp=void 0,Dp={},Ep=void 0,Fp=new function(){var a=5;xp.ml>0&&(a=xp.ml);this.H=a;this.C=0;this.N=[]},Gp=1E3;
function Hp(a,b){var c=Cp;if(c===void 0)if(b)c=Jo();else return"";for(var d=[uk("https://www.googletagmanager.com"),"/a",Bp],e=l(yp),f=e.next();!f.done;f=e.next())for(var g=f.value,k=g({eventId:c,kd:!!a}),m=l(k),n=m.next();!n.done;n=m.next()){var p=l(n.value),q=p.next().value,r=p.next().value;d.push("&"+q+"="+r)}d.push("&z=0");return d.join("")}
function Ip(){if(oj.U&&(Ep&&(z.clearTimeout(Ep),Ep=void 0),Cp!==void 0&&Jp)){if(I(106)){var a=Vm(3);if(Rm(a)){zp||(zp=!0,Tm(a,Ip));return}}var b;if(!(b=Dp[Cp])){var c=Fp;b=c.C<c.H?!1:qb()-c.N[c.C%c.H]<1E3}if(b||Gp--<=0)O(1),Dp[Cp]=!0;else{var d=Fp,e=d.C++%d.H;d.N[e]=qb();var f=Hp(!0);El({destinationId:Xf.ctid,endpoint:56,eventId:Cp},f);zp=Jp=!1}}}function Kp(){if(Ck&&oj.U){var a=Hp(!0,!0);El({destinationId:Xf.ctid,endpoint:56,eventId:Cp},a)}}var Jp=!1;
function Lp(a){Dp[a]||(a!==Cp&&(Ip(),Cp=a),Jp=!0,Ep||(Ep=z.setTimeout(Ip,500)),Hp().length>=2022&&Ip())}var Mp=gb();function Np(){Mp=gb()}function Op(){return[["v","3"],["t","t"],["pid",String(Mp)]]};var Pp={};function Qp(a,b,c){Ck&&a!==void 0&&(Pp[a]=Pp[a]||[],Pp[a].push(c+b),Lp(a))}function Rp(a){var b=a.eventId,c=a.kd,d=[],e=Pp[b]||[];e.length&&d.push(["epr",e.join(".")]);c&&delete Pp[b];return d};function Sp(a,b,c){var d=Oo(cm(a),!0);d&&Tp.register(d,b,c)}function Up(a,b,c,d){var e=Oo(c,d.isGtmEvent);e&&(wj&&(d.deferrable=!0),Tp.push("event",[b,a],e,d))}function Vp(a,b,c,d){var e=Oo(c,d.isGtmEvent);e&&Tp.push("get",[a,b],e,d)}function Wp(a){var b=Oo(cm(a),!0),c;b?c=Xp(Tp,b).C:c={};return c}function Yp(a,b){var c=Oo(cm(a),!0);if(c){var d=Tp,e=Zc(b,null);Zc(Xp(d,c).C,e);Xp(d,c).C=e}}
var Zp=function(){this.U={};this.C={};this.H={};this.ba=null;this.O={};this.N=!1;this.status=1},$p=function(a,b,c,d){this.H=qb();this.C=b;this.args=c;this.messageContext=d;this.type=a},aq=function(){this.destinations={};this.C={};this.commands=[]},Xp=function(a,b){var c=b.destinationId;Tl||(c=hm(c));return a.destinations[c]=a.destinations[c]||new Zp},bq=function(a,b,c,d){if(d.C){var e=Xp(a,d.C),f=e.ba;if(f){var g=d.C.id;Tl||(g=hm(g));var k=Zc(c,null),m=Zc(e.U[g],null),n=Zc(e.O,null),p=Zc(e.C,null),
q=Zc(a.C,null),r={};if(Ck)try{r=Zc(Oj,null)}catch(x){O(72)}var v=d.C.prefix,u=function(x){Qp(d.messageContext.eventId,v,x)},t=wp(vp(up(tp(sp(qp(pp(rp(op(np(mp(new lp(d.messageContext.eventId,d.messageContext.priorityId),k),m),n),p),q),r),d.messageContext.eventMetadata),function(){if(u){var x=u;u=void 0;x("2");if(d.messageContext.onSuccess)d.messageContext.onSuccess()}}),function(){if(u){var x=u;u=void 0;x("3");if(d.messageContext.onFailure)d.messageContext.onFailure()}}),!!d.messageContext.isGtmEvent)),
w=function(){try{Qp(d.messageContext.eventId,v,"1");var x=d.type,y=d.C.id;if(Dk&&x==="config"){var B,C=(B=Oo(y))==null?void 0:B.ids;if(!(C&&C.length>1)){var E,H=kc("google_tag_data",{});H.td||(H.td={});E=H.td;var N=Zc(t.O);Zc(t.C,N);var L=[],T;for(T in E)E.hasOwnProperty(T)&&ep(E[T],N).length&&L.push(T);L.length&&(cp(y,L),Xa("TAGGING",Zo[A.readyState]||14));E[y]=N}}f(d.C.id,b,d.H,t)}catch(F){Qp(d.messageContext.eventId,v,"4")}};b==="gtag.get"?w():I(106)?Tm(e.sa,w):w()}}};
aq.prototype.register=function(a,b,c){var d=Xp(this,a);d.status!==3&&(d.ba=b,d.status=3,I(106)&&(d.sa=Vm(c)),this.flush())};
aq.prototype.push=function(a,b,c,d){c!==void 0&&(Xp(this,c).status===1&&(Xp(this,c).status=2,this.push("require",[{}],c,{})),Xp(this,c).N&&(d.deferrable=!1),d.eventMetadata||(d.eventMetadata={}),d.eventMetadata.send_to_destinations||(d.eventMetadata.send_to_destinations=[c.destinationId]),d.eventMetadata.send_to_targets||(d.eventMetadata.send_to_targets=[c.id]));this.commands.push(new $p(a,c,b,d));d.deferrable||this.flush()};
aq.prototype.flush=function(a){for(var b=this,c=[],d=!1,e={};this.commands.length;e={fc:void 0,yg:void 0}){var f=this.commands[0],g=f.C;if(f.messageContext.deferrable)!g||Xp(this,g).N?(f.messageContext.deferrable=!1,this.commands.push(f)):c.push(f),this.commands.shift();else{switch(f.type){case "require":if(Xp(this,g).status!==3&&!a){this.commands.push.apply(this.commands,c);return}break;case "set":var k=f.args[0];jb(k,function(u,t){Zc(xb(u,t),b.C)});mj(k,!0);break;case "config":var m=Xp(this,g);
e.fc={};jb(f.args[0],function(u){return function(t,w){Zc(xb(t,w),u.fc)}}(e));var n=!!e.fc[M.m.Ac];delete e.fc[M.m.Ac];var p=g.destinationId===g.id;mj(e.fc,!0);n||(p?m.O={}:m.U[g.id]={});m.N&&n||bq(this,M.m.ia,e.fc,f);m.N=!0;p?Zc(e.fc,m.O):(Zc(e.fc,m.U[g.id]),O(70));d=!0;un(e.fc,g.id);on=!0;break;case "event":e.yg={};jb(f.args[0],function(u){return function(t,w){Zc(xb(t,w),u.yg)}}(e));mj(e.yg);bq(this,f.args[1],e.yg,f);var q=void 0;!f.C||((q=f.messageContext.eventMetadata)==null?0:q.em_event)||(rn[f.C.id]=
!0);on=!0;break;case "get":var r={},v=(r[M.m.Gb]=f.args[0],r[M.m.Wb]=f.args[1],r);bq(this,M.m.eb,v,f);on=!0}this.commands.shift();cq(this,f)}}this.commands.push.apply(this.commands,c);d&&this.flush()};var cq=function(a,b){if(b.type!=="require")if(b.C)for(var c=Xp(a,b.C).H[b.type]||[],d=0;d<c.length;d++)c[d]();else for(var e in a.destinations)if(a.destinations.hasOwnProperty(e)){var f=a.destinations[e];if(f&&f.H)for(var g=f.H[b.type]||[],k=0;k<g.length;k++)g[k]()}},Tp=new aq;function dq(a,b,c,d){d=d===void 0?!1:d;a.google_image_requests||(a.google_image_requests=[]);var e=jl(a.document);if(c){var f=function(){if(c){var g=a.google_image_requests,k=dc(g,e);k>=0&&Array.prototype.splice.call(g,k,1)}Hk(e,"load",f);Hk(e,"error",f)};Gk(e,"load",f);Gk(e,"error",f)}d&&(e.attributionSrc="");e.src=b;a.google_image_requests.push(e)}
function eq(a){var b;b=b===void 0?!1:b;var c="https://pagead2.googlesyndication.com/pagead/gen_204?id=tcfe";hl(a,function(d,e){if(d||d===0)c+="&"+e+"="+encodeURIComponent(String(d))});fq(c,b)}
function fq(a,b){var c=window,d;b=b===void 0?!1:b;d=d===void 0?!1:d;if(c.fetch){var e={keepalive:!0,credentials:"include",redirect:"follow",method:"get",mode:"no-cors"};d&&(e.mode="cors","setAttributionReporting"in XMLHttpRequest.prototype?e.attributionReporting={eventSourceEligible:"true",triggerEligible:"false"}:e.headers={"Attribution-Reporting-Eligible":"event-source"});c.fetch(a,e)}else dq(c,a,b===void 0?!1:b,d===void 0?!1:d)};var gq=function(){this.U=this.U;this.H=this.H};gq.prototype.U=!1;gq.prototype.dispose=function(){this.U||(this.U=!0,this.sa())};gq.prototype[Symbol.dispose]=function(){this.dispose()};gq.prototype.addOnDisposeCallback=function(a,b){this.U?b!==void 0?a.call(b):a():(this.H||(this.H=[]),b&&(a=a.bind(b)),this.H.push(a))};gq.prototype.sa=function(){if(this.H)for(;this.H.length;)this.H.shift()()};function hq(a){a.addtlConsent!==void 0&&typeof a.addtlConsent!=="string"&&(a.addtlConsent=void 0);a.gdprApplies!==void 0&&typeof a.gdprApplies!=="boolean"&&(a.gdprApplies=void 0);return a.tcString!==void 0&&typeof a.tcString!=="string"||a.listenerId!==void 0&&typeof a.listenerId!=="number"?2:a.cmpStatus&&a.cmpStatus!=="error"?0:3}
var iq=function(a,b){b=b===void 0?{}:b;gq.call(this);this.C=null;this.ba={};this.rg=0;this.O=null;this.N=a;var c;this.Bc=(c=b.ao)!=null?c:500;var d;this.jb=(d=b.Fo)!=null?d:!1};ra(iq,gq);iq.prototype.sa=function(){this.ba={};this.O&&(Hk(this.N,"message",this.O),delete this.O);delete this.ba;delete this.N;delete this.C;gq.prototype.sa.call(this)};var kq=function(a){return typeof a.N.__tcfapi==="function"||jq(a)!=null};
iq.prototype.addEventListener=function(a){var b=this,c={internalBlockOnErrors:this.jb},d=Fk(function(){return a(c)}),e=0;this.Bc!==-1&&(e=setTimeout(function(){c.tcString="tcunavailable";c.internalErrorState=1;d()},this.Bc));var f=function(g,k){clearTimeout(e);g?(c=g,c.internalErrorState=hq(c),c.internalBlockOnErrors=b.jb,k&&c.internalErrorState===0||(c.tcString="tcunavailable",k||(c.internalErrorState=3))):(c.tcString="tcunavailable",c.internalErrorState=3);a(c)};try{lq(this,"addEventListener",f)}catch(g){c.tcString=
"tcunavailable",c.internalErrorState=3,e&&(clearTimeout(e),e=0),d()}};iq.prototype.removeEventListener=function(a){a&&a.listenerId&&lq(this,"removeEventListener",null,a.listenerId)};
var nq=function(a,b,c){var d;d=d===void 0?"755":d;var e;a:{if(a.publisher&&a.publisher.restrictions){var f=a.publisher.restrictions[b];if(f!==void 0){e=f[d===void 0?"755":d];break a}}e=void 0}var g=e;if(g===0)return!1;var k=c;c===2?(k=0,g===2&&(k=1)):c===3&&(k=1,g===1&&(k=0));var m;if(k===0)if(a.purpose&&a.vendor){var n=mq(a.vendor.consents,d===void 0?"755":d);m=n&&b==="1"&&a.purposeOneTreatment&&a.publisherCC==="CH"?!0:n&&mq(a.purpose.consents,b)}else m=!0;else m=k===1?a.purpose&&a.vendor?mq(a.purpose.legitimateInterests,
b)&&mq(a.vendor.legitimateInterests,d===void 0?"755":d):!0:!0;return m},mq=function(a,b){return!(!a||!a[b])},lq=function(a,b,c,d){c||(c=function(){});var e=a.N;if(typeof e.__tcfapi==="function"){var f=e.__tcfapi;f(b,2,c,d)}else if(jq(a)){oq(a);var g=++a.rg;a.ba[g]=c;if(a.C){var k={};a.C.postMessage((k.__tcfapiCall={command:b,version:2,callId:g,parameter:d},k),"*")}}else c({},!1)},jq=function(a){if(a.C)return a.C;var b;a:{for(var c=a.N,d=0;d<50;++d){var e;try{e=!(!c.frames||!c.frames.__tcfapiLocator)}catch(k){e=
!1}if(e){b=c;break a}var f;b:{try{var g=c.parent;if(g&&g!=c){f=g;break b}}catch(k){}f=null}if(!(c=f))break}b=null}a.C=b;return a.C},oq=function(a){if(!a.O){var b=function(c){try{var d;d=(typeof c.data==="string"?JSON.parse(c.data):c.data).__tcfapiReturn;a.ba[d.callId](d.returnValue,d.success)}catch(e){}};a.O=b;Gk(a.N,"message",b)}},pq=function(a){if(a.gdprApplies===!1)return!0;a.internalErrorState===void 0&&(a.internalErrorState=hq(a));return a.cmpStatus==="error"||a.internalErrorState!==0?a.internalBlockOnErrors?
(eq({e:String(a.internalErrorState)}),!1):!0:a.cmpStatus!=="loaded"||a.eventStatus!=="tcloaded"&&a.eventStatus!=="useractioncomplete"?!1:!0};var qq={1:0,3:0,4:0,7:3,9:3,10:3};function rq(){return Fo("tcf",function(){return{}})}var sq=function(){return new iq(z,{ao:-1})};
function tq(){var a=rq(),b=sq();kq(b)&&!uq()&&!vq()&&O(124);if(!a.active&&kq(b)){uq()&&(a.active=!0,a.purposes={},a.cmpId=0,a.tcfPolicyVersion=0,vm().active=!0,a.tcString="tcunavailable");yo();try{b.addEventListener(function(c){if(c.internalErrorState!==0)wq(a),zo([M.m.R,M.m.za,M.m.T]),vm().active=!0;else if(a.gdprApplies=c.gdprApplies,a.cmpId=c.cmpId,a.enableAdvertiserConsentMode=c.enableAdvertiserConsentMode,vq()&&(a.active=!0),!xq(c)||uq()||vq()){a.tcfPolicyVersion=c.tcfPolicyVersion;var d;if(c.gdprApplies===
!1){var e={},f;for(f in qq)qq.hasOwnProperty(f)&&(e[f]=!0);d=e;b.removeEventListener(c)}else if(xq(c)){var g={},k;for(k in qq)if(qq.hasOwnProperty(k))if(k==="1"){var m,n=c,p={Qm:!0};p=p===void 0?{}:p;m=pq(n)?n.gdprApplies===!1?!0:n.tcString==="tcunavailable"?!p.Fk:(p.Fk||n.gdprApplies!==void 0||p.Qm)&&(p.Fk||typeof n.tcString==="string"&&n.tcString.length)?nq(n,"1",0):!0:!1;g["1"]=m}else g[k]=nq(c,k,qq[k]);d=g}if(d){a.tcString=c.tcString||"tcempty";a.purposes=d;var q={},r=(q[M.m.R]=a.purposes["1"]?
"granted":"denied",q);a.gdprApplies!==!0?(zo([M.m.R,M.m.za,M.m.T]),vm().active=!0):(r[M.m.za]=a.purposes["3"]&&a.purposes["4"]?"granted":"denied",typeof a.tcfPolicyVersion==="number"&&a.tcfPolicyVersion>=4?r[M.m.T]=a.purposes["1"]&&a.purposes["7"]?"granted":"denied":zo([M.m.T]),to(r,{eventId:0},{gdprApplies:a?a.gdprApplies:void 0,tcString:yq()||""}))}}else zo([M.m.R,M.m.za,M.m.T])})}catch(c){wq(a),zo([M.m.R,M.m.za,M.m.T]),vm().active=!0}}}function wq(a){a.type="e";a.tcString="tcunavailable"}
function xq(a){return a.eventStatus==="tcloaded"||a.eventStatus==="useractioncomplete"||a.eventStatus==="cmpuishown"}function uq(){return z.gtag_enable_tcf_support===!0}function vq(){return rq().enableAdvertiserConsentMode===!0}function yq(){var a=rq();if(a.active)return a.tcString}function zq(){var a=rq();if(a.active&&a.gdprApplies!==void 0)return a.gdprApplies?"1":"0"}function Aq(a){if(!qq.hasOwnProperty(String(a)))return!0;var b=rq();return b.active&&b.purposes?!!b.purposes[String(a)]:!0};var Bq=[M.m.R,M.m.X,M.m.T,M.m.za],Cq={},Dq=(Cq[M.m.R]=1,Cq[M.m.X]=2,Cq);function Eq(a){if(a===void 0)return 0;switch(P(a,M.m.qa)){case void 0:return 1;case !1:return 3;default:return 2}}function Fq(a){if(Ln()==="US-CO"&&gc.globalPrivacyControl===!0)return!1;var b=Eq(a);if(b===3)return!1;switch(Em(M.m.za)){case 1:case 3:return!0;case 2:return!1;case 4:return b===2;case 0:return!0;default:return!1}}function Gq(){return Hm()||!Dm(M.m.R)||!Dm(M.m.X)}
function Hq(){var a={},b;for(b in Dq)Dq.hasOwnProperty(b)&&(a[Dq[b]]=Em(b));return"G1"+Ne(a[1]||0)+Ne(a[2]||0)}var Iq={},Jq=(Iq[M.m.R]=0,Iq[M.m.X]=1,Iq[M.m.T]=2,Iq[M.m.za]=3,Iq);function Kq(a){switch(a){case void 0:return 1;case !0:return 3;case !1:return 2;default:return 0}}
function Lq(a){for(var b="1",c=0;c<Bq.length;c++){var d=b,e,f=Bq[c],g=Cm.delegatedConsentTypes[f];e=g===void 0?0:Jq.hasOwnProperty(g)?12|Jq[g]:8;var k=vm();k.accessedAny=!0;var m=k.entries[f]||{};e=e<<2|Kq(m.implicit);b=d+(""+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[e]+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[Kq(m.declare)<<4|Kq(m.default)<<2|Kq(m.update)])}var n=b,p=(Ln()==="US-CO"&&gc.globalPrivacyControl===!0?1:0)<<3,q=(Hm()?1:0)<<2,r=Eq(a);b=
n+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[p|q|r];return b+=""+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[Cm.containerScopedDefaults.ad_storage<<4|Cm.containerScopedDefaults.analytics_storage<<2|Cm.containerScopedDefaults.ad_user_data]+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[(Cm.usedContainerScopedDefaults?1:0)<<2|Cm.containerScopedDefaults.ad_personalization]}
function Mq(){if(!Dm(M.m.T))return"-";for(var a=Object.keys(di),b=Fm(a),c="",d=l(a),e=d.next();!e.done;e=d.next()){var f=e.value;b[f]&&(c+=di[f])}(Cm.usedCorePlatformServices?Cm.selectedAllCorePlatformServices:1)&&(c+="o");return c||"-"}function Nq(){return Nn()||(uq()||vq())&&zq()==="1"?"1":"0"}function Oq(){return(Nn()?!0:!(!uq()&&!vq())&&zq()==="1")||!Dm(M.m.T)}
function Pq(){var a="0",b="0",c;var d=rq();c=d.active?d.cmpId:void 0;typeof c==="number"&&c>=0&&c<=4095&&(a="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[c>>6&63],b="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[c&63]);var e="0",f;var g=rq();f=g.active?g.tcfPolicyVersion:void 0;typeof f==="number"&&f>=0&&f<=63&&(e="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[f]);var k=0;Nn()&&(k|=1);zq()==="1"&&(k|=2);uq()&&(k|=4);var m;var n=rq();m=n.enableAdvertiserConsentMode!==
void 0?n.enableAdvertiserConsentMode?"1":"0":void 0;m==="1"&&(k|=8);vm().waitPeriodTimedOut&&(k|=16);return"1"+a+b+e+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[k]}function Qq(){return Ln()==="US-CO"};function Rq(){var a=!1;return a};var Sq={UA:1,AW:2,DC:3,G:4,GF:5,GT:12,GTM:14,HA:6,MC:7};
function Tq(a){a=a===void 0?{}:a;var b=Xf.ctid.split("-")[0].toUpperCase(),c={ctid:Xf.ctid,Nn:rj.Jh,Pn:rj.Kh,vn:Sl.Xe?2:1,Vn:a.Yk,hf:Xf.canonicalContainerId};c.hf!==a.Da&&(c.Da=a.Da);var d=em();c.Dn=d?d.canonicalContainerId:void 0;xj?(c.Ig=Sq[b],c.Ig||(c.Ig=0)):c.Ig=Bj?13:10;oj.C?(c.Gg=0,c.rm=2):zj?c.Gg=1:Rq()?c.Gg=2:c.Gg=3;var e={};e[6]=Tl;oj.H===2?e[7]=!0:oj.H===1&&(e[2]=!0);if(jc){var f=fk(lk(jc),"host");f&&(e[8]=f.match(/^(www\.)?googletagmanager\.com$/)===null)}c.vm=e;var g=a.ug,k;var m=c.Ig,
n=c.Gg;m===void 0?k="":(n||(n=0),k=""+Se(1,1)+Me(m<<2|n));var p=c.rm,q="4"+k+(p?""+Se(2,1)+Me(p):""),r,v=c.Pn;r=v&&Re.test(v)?""+Se(3,2)+v:"";var u,t=c.Nn;u=t?""+Se(4,1)+Me(t):"";var w;var x=c.ctid;if(x&&g){var y=x.split("-"),B=y[0].toUpperCase();if(B!=="GTM"&&B!=="OPT")w="";else{var C=y[1];w=""+Se(5,3)+Me(1+C.length)+(c.vn||0)+C}}else w="";var E=c.Vn,H=c.hf,N=c.Da,L=c.Mo,T=q+r+u+w+(E?""+Se(6,1)+Me(E):"")+(H?""+Se(7,3)+Me(H.length)+H:"")+(N?""+Se(8,3)+Me(N.length)+N:"")+(L?""+Se(9,3)+Me(L.length)+
L:""),F;var S=c.vm;S=S===void 0?{}:S;for(var ba=[],ha=l(Object.keys(S)),Y=ha.next();!Y.done;Y=ha.next()){var Q=Y.value;ba[Number(Q)]=S[Q]}if(ba.length){var ia=Se(10,3),la;if(ba.length===0)la=Me(0);else{for(var ma=[],Ea=0,Sa=!1,Ha=0;Ha<ba.length;Ha++){Sa=!0;var Ta=Ha%6;ba[Ha]&&(Ea|=1<<Ta);Ta===5&&(ma.push(Me(Ea)),Ea=0,Sa=!1)}Sa&&ma.push(Me(Ea));la=ma.join("")}var cb=la;F=""+ia+Me(cb.length)+cb}else F="";var Lb=c.Dn;return T+F+(Lb?""+Se(11,3)+Me(Lb.length)+Lb:"")};function Uq(a){var b=1,c,d,e;if(a)for(b=0,d=a.length-1;d>=0;d--)e=a.charCodeAt(d),b=(b<<6&268435455)+e+(e<<14),c=b&266338304,b=c!==0?b^c>>21:b;return b};var Vq={M:{dm:0,Ri:1,Of:2,Ui:3,Mg:4,Si:5,Ti:6,Vi:7,Ng:8,Vj:9,Uj:10,Dh:11,Wj:12,og:13,Yj:14,Ye:15,bm:16,Nd:17,Qh:18,Rh:19,Sh:20,rk:21,Th:22,Og:23,cj:24}};Vq.M[Vq.M.dm]="RESERVED_ZERO";Vq.M[Vq.M.Ri]="ADS_CONVERSION_HIT";Vq.M[Vq.M.Of]="CONTAINER_EXECUTE_START";Vq.M[Vq.M.Ui]="CONTAINER_SETUP_END";Vq.M[Vq.M.Mg]="CONTAINER_SETUP_START";Vq.M[Vq.M.Si]="CONTAINER_BLOCKING_END";Vq.M[Vq.M.Ti]="CONTAINER_EXECUTE_END";Vq.M[Vq.M.Vi]="CONTAINER_YIELD_END";Vq.M[Vq.M.Ng]="CONTAINER_YIELD_START";Vq.M[Vq.M.Vj]="EVENT_EXECUTE_END";
Vq.M[Vq.M.Uj]="EVENT_EVALUATION_END";Vq.M[Vq.M.Dh]="EVENT_EVALUATION_START";Vq.M[Vq.M.Wj]="EVENT_SETUP_END";Vq.M[Vq.M.og]="EVENT_SETUP_START";Vq.M[Vq.M.Yj]="GA4_CONVERSION_HIT";Vq.M[Vq.M.Ye]="PAGE_LOAD";Vq.M[Vq.M.bm]="PAGEVIEW";Vq.M[Vq.M.Nd]="SNIPPET_LOAD";Vq.M[Vq.M.Qh]="TAG_CALLBACK_ERROR";Vq.M[Vq.M.Rh]="TAG_CALLBACK_FAILURE";Vq.M[Vq.M.Sh]="TAG_CALLBACK_SUCCESS";Vq.M[Vq.M.rk]="TAG_EXECUTE_END";Vq.M[Vq.M.Th]="TAG_EXECUTE_START";Vq.M[Vq.M.Og]="CUSTOM_PERFORMANCE_START";Vq.M[Vq.M.cj]="CUSTOM_PERFORMANCE_END";var Wq=[],Xq={},Yq={};var Zq=["1"];function $q(a){return a.origin!=="null"};function ar(a,b,c,d){if(!br(d))return[];if(Wq.includes("1")){var e;(e=Mc())==null||e.mark("1-"+Vq.M.Og+"-"+(Yq["1"]||0))}for(var f=[],g=String(b||cr()).split(";"),k=0;k<g.length;k++){var m=g[k].split("="),n=m[0].replace(/^\s*|\s*$/g,"");if(n&&n===a){var p;(p=m.slice(1).join("=").replace(/^\s*|\s*$/g,""))&&c&&(p=decodeURIComponent(p));f.push(p)}}if(Wq.includes("1")){var q="1-"+Vq.M.cj+"-"+(Yq["1"]||0),r={start:"1-"+Vq.M.Og+"-"+(Yq["1"]||0),end:q},v;(v=Mc())==null||v.mark(q);var u,t,w=(t=(u=Mc())==
null?void 0:u.measure(q,r))==null?void 0:t.duration;w!==void 0&&(Yq["1"]=(Yq["1"]||0)+1,Xq["1"]=w+(Xq["1"]||0))}return f}function dr(a,b,c,d,e){if(br(e)){var f=er(a,d,e);if(f.length===1)return f[0];if(f.length!==0){f=fr(f,function(g){return g.Dm},b);if(f.length===1)return f[0];f=fr(f,function(g){return g.Gn},c);return f[0]}}}function gr(a,b,c,d){var e=cr(),f=window;$q(f)&&(f.document.cookie=a);var g=cr();return e!==g||c!==void 0&&ar(b,g,!1,d).indexOf(c)>=0}
function hr(a,b,c,d){function e(w,x,y){if(y==null)return delete k[x],w;k[x]=y;return w+"; "+x+"="+y}function f(w,x){if(x==null)return w;k[x]=!0;return w+"; "+x}if(!br(c.Qb))return 2;var g;b==null?g=a+"=deleted; expires="+(new Date(0)).toUTCString():(c.encode&&(b=encodeURIComponent(b)),b=ir(b),g=a+"="+b);var k={};g=e(g,"path",c.path);var m;c.expires instanceof Date?m=c.expires.toUTCString():c.expires!=null&&(m=""+c.expires);g=e(g,"expires",m);g=e(g,"max-age",c.zn);g=e(g,"samesite",c.Qn);c.secure&&
(g=f(g,"secure"));var n=c.domain;if(n&&n.toLowerCase()==="auto"){for(var p=jr(),q=void 0,r=!1,v=0;v<p.length;++v){var u=p[v]!=="none"?p[v]:void 0,t=e(g,"domain",u);t=f(t,c.flags);try{d&&d(a,k)}catch(w){q=w;continue}r=!0;if(!kr(u,c.path)&&gr(t,a,b,c.Qb))return 0}if(q&&!r)throw q;return 1}n&&n.toLowerCase()!=="none"&&(g=e(g,"domain",n));g=f(g,c.flags);d&&d(a,k);return kr(n,c.path)?1:gr(g,a,b,c.Qb)?0:1}function lr(a,b,c){c.path==null&&(c.path="/");c.domain||(c.domain="auto");return hr(a,b,c)}
function fr(a,b,c){for(var d=[],e=[],f,g=0;g<a.length;g++){var k=a[g],m=b(k);m===c?d.push(k):f===void 0||m<f?(e=[k],f=m):m===f&&e.push(k)}return d.length>0?d:e}function er(a,b,c){for(var d=[],e=ar(a,void 0,void 0,c),f=0;f<e.length;f++){var g=e[f].split("."),k=g.shift();if(!b||!k||b.indexOf(k)!==-1){var m=g.shift();if(m){var n=m.split("-");d.push({xm:e[f],ym:g.join("."),Dm:Number(n[0])||1,Gn:Number(n[1])||1})}}}return d}function ir(a){a&&a.length>1200&&(a=a.substring(0,1200));return a}
var mr=/^(www\.)?google(\.com?)?(\.[a-z]{2})?$/,nr=/(^|\.)doubleclick\.net$/i;function kr(a,b){return a!==void 0&&(nr.test(window.document.location.hostname)||b==="/"&&mr.test(a))}function or(a){if(!a)return 1;var b=a;kg(7)&&a==="none"&&(b=window.document.location.hostname);b=b.indexOf(".")===0?b.substring(1):b;return b.split(".").length}function pr(a){if(!a||a==="/")return 1;a[0]!=="/"&&(a="/"+a);a[a.length-1]!=="/"&&(a+="/");return a.split("/").length-1}
function qr(a,b){var c=""+or(a),d=pr(b);d>1&&(c+="-"+d);return c}
var cr=function(){return $q(window)?window.document.cookie:""},br=function(a){return a&&kg(8)?(Array.isArray(a)?a:[a]).every(function(b){return Gm(b)&&Dm(b)}):!0},jr=function(){var a=[],b=window.document.location.hostname.split(".");if(b.length===4){var c=b[b.length-1];if(Number(c).toString()===c)return["none"]}for(var d=b.length-2;d>=0;d--)a.push(b.slice(d).join("."));var e=window.document.location.hostname;nr.test(e)||mr.test(e)||a.push("none");return a};function rr(a){var b=Math.round(Math.random()*2147483647);return a?String(b^Uq(a)&2147483647):String(b)}function sr(a){return[rr(a),Math.round(qb()/1E3)].join(".")}function tr(a,b,c,d,e){var f=or(b),g;return(g=dr(a,f,pr(c),d,e))==null?void 0:g.ym}function ur(a,b,c,d){return[b,qr(c,d),a].join(".")};function vr(a,b,c,d){var e,f=Number(a.Pb!=null?a.Pb:void 0);f!==0&&(e=new Date((b||qb())+1E3*(f||7776E3)));return{path:a.path,domain:a.domain,flags:a.flags,encode:!!c,expires:e,Qb:d}};var wr=["ad_storage","ad_user_data"];function xr(a,b){if(!a)return 10;if(b===null||b===void 0||b==="")return 11;var c=yr(!1);if(c.error!==0)return c.error;if(!c.value)return 2;c.value[a]=b;return zr(c)}function Ar(a){if(!a)return{error:10};var b=yr();if(b.error!==0)return b;if(!b.value)return{error:2};if(!(a in b.value))return{value:void 0,error:15};var c=b.value[a];return c===null||c===void 0||c===""?{value:void 0,error:11}:{value:c,error:0}}
function yr(a){a=a===void 0?!0:a;if(!Dm(wr))return{error:3};try{if(!z.localStorage)return{error:1}}catch(f){return{error:14}}var b={schema:"gcl",version:1},c=void 0;try{c=z.localStorage.getItem("_gcl_ls")}catch(f){return{error:13}}try{if(c){var d=JSON.parse(c);if(d&&typeof d==="object")b=d;else return{error:12}}}catch(f){return{error:8}}if(b.schema!=="gcl")return{error:4};if(b.version!==1)return{error:5};try{var e=Br(b);a&&e&&zr({value:b,error:0})}catch(f){return{error:8}}return{value:b,error:0}}
function Br(a){if(!a||typeof a!=="object")return!1;if("expires"in a&&"value"in a){var b;typeof a.expires==="number"?b=a.expires:b=typeof a.expires==="string"?Number(a.expires):NaN;if(isNaN(b)||!(Date.now()<=b))return a.value=null,a.error=9,!0}else{for(var c=!1,d=l(Object.keys(a)),e=d.next();!e.done;e=d.next())c=Br(a[e.value])||c;return c}return!1}
function zr(a){if(a.error)return a.error;if(!a.value)return 2;var b=a.value,c;try{c=JSON.stringify(b)}catch(d){return 6}try{z.localStorage.setItem("_gcl_ls",c)}catch(d){return 7}return 0};function Cr(){if(!Dr())return-1;var a=Er();return a!==-1&&Fr(a+1)?a+1:-1}function Er(){if(!Dr())return-1;var a=Ar("gcl_ctr");if(!a||a.error!==0||!a.value||typeof a.value!=="object")return-1;var b=a.value;try{if(!("value"in b&&b.value)||typeof b.value!=="object")return-1;var c=b.value.value;return c==null||Number.isNaN(c)?-1:Number(c)}catch(d){return-1}}function Dr(){return Dm(["ad_storage","ad_user_data"])?kg(11):!1}
function Fr(a,b){b=b||{};var c=qb();return xr("gcl_ctr",{value:{value:a,creationTimeMs:c},expires:Number(vr(b,c,!0).expires)})===0?!0:!1};var Gr;function Hr(){function a(g){c(g.target||g.srcElement||{})}function b(g){d(g.target||g.srcElement||{})}var c=Ir,d=Jr,e=Kr();if(!e.init){xc(A,"mousedown",a);xc(A,"keyup",a);xc(A,"submit",b);var f=HTMLFormElement.prototype.submit;HTMLFormElement.prototype.submit=function(){d(this);f.call(this)};e.init=!0}}function Lr(a,b,c,d,e){var f={callback:a,domains:b,fragment:c===2,placement:c,forms:d,sameHost:e};Kr().decorators.push(f)}
function Mr(a,b,c){for(var d=Kr().decorators,e={},f=0;f<d.length;++f){var g=d[f],k;if(k=!c||g.forms)a:{var m=g.domains,n=a,p=!!g.sameHost;if(m&&(p||n!==A.location.hostname))for(var q=0;q<m.length;q++)if(m[q]instanceof RegExp){if(m[q].test(n)){k=!0;break a}}else if(n.indexOf(m[q])>=0||p&&m[q].indexOf(n)>=0){k=!0;break a}k=!1}if(k){var r=g.placement;r===void 0&&(r=g.fragment?2:1);r===b&&tb(e,g.callback())}}return e}
function Kr(){var a=kc("google_tag_data",{}),b=a.gl;b&&b.decorators||(b={decorators:[]},a.gl=b);return b};var Nr=/(.*?)\*(.*?)\*(.*)/,Or=/^https?:\/\/([^\/]*?)\.?cdn\.ampproject\.org\/?(.*)/,Pr=/^(?:www\.|m\.|amp\.)+/,Qr=/([^?#]+)(\?[^#]*)?(#.*)?/;function Rr(a){var b=Qr.exec(a);if(b)return{Di:b[1],query:b[2],fragment:b[3]}}function Sr(a){return new RegExp("(.*?)(^|&)"+a+"=([^&]*)&?(.*)")}
function Tr(a,b){var c=[gc.userAgent,(new Date).getTimezoneOffset(),gc.userLanguage||gc.language,Math.floor(qb()/60/1E3)-(b===void 0?0:b),a].join("*"),d;if(!(d=Gr)){for(var e=Array(256),f=0;f<256;f++){for(var g=f,k=0;k<8;k++)g=g&1?g>>>1^3988292384:g>>>1;e[f]=g}d=e}Gr=d;for(var m=4294967295,n=0;n<c.length;n++)m=m>>>8^Gr[(m^c.charCodeAt(n))&255];return((m^-1)>>>0).toString(36)}
function Ur(a){return function(b){var c=lk(z.location.href),d=c.search.replace("?",""),e=dk(d,"_gl",!1,!0)||"";b.query=Vr(e)||{};var f=fk(c,"fragment"),g;var k=-1;if(vb(f,"_gl="))k=4;else{var m=f.indexOf("&_gl=");m>0&&(k=m+3+2)}if(k<0)g=void 0;else{var n=f.indexOf("&",k);g=n<0?f.substring(k):f.substring(k,n)}b.fragment=Vr(g||"")||{};a&&Wr(c,d,f)}}function Xr(a,b){var c=Sr(a).exec(b),d=b;if(c){var e=c[2],f=c[4];d=c[1];f&&(d=d+e+f)}return d}
function Wr(a,b,c){function d(g,k){var m=Xr("_gl",g);m.length&&(m=k+m);return m}if(fc&&fc.replaceState){var e=Sr("_gl");if(e.test(b)||e.test(c)){var f=fk(a,"path");b=d(b,"?");c=d(c,"#");fc.replaceState({},"",""+f+b+c)}}}function Yr(a,b){var c=Ur(!!b),d=Kr();d.data||(d.data={query:{},fragment:{}},c(d.data));var e={},f=d.data;f&&(tb(e,f.query),a&&tb(e,f.fragment));return e}
var Vr=function(a){try{var b=Zr(a,3);if(b!==void 0){for(var c={},d=b?b.split("*"):[],e=0;e+1<d.length;e+=2){var f=d[e],g=Va(d[e+1]);c[f]=g}Xa("TAGGING",6);return c}}catch(k){Xa("TAGGING",8)}};function Zr(a,b){if(a){var c;a:{for(var d=a,e=0;e<3;++e){var f=Nr.exec(d);if(f){c=f;break a}d=decodeURIComponent(d)}c=void 0}var g=c;if(g&&g[1]==="1"){var k=g[3],m;a:{for(var n=g[2],p=0;p<b;++p)if(n===Tr(k,p)){m=!0;break a}m=!1}if(m)return k;Xa("TAGGING",7)}}}
function $r(a,b,c,d,e){function f(p){p=Xr(a,p);var q=p.charAt(p.length-1);p&&q!=="&"&&(p+="&");return p+n}d=d===void 0?!1:d;e=e===void 0?!1:e;var g=Rr(c);if(!g)return"";var k=g.query||"",m=g.fragment||"",n=a+"="+b;d?m.substring(1).length!==0&&e||(m="#"+f(m.substring(1))):k="?"+f(k.substring(1));return""+g.Di+k+m}
function as(a,b){function c(n,p,q){var r;a:{for(var v in n)if(n.hasOwnProperty(v)){r=!0;break a}r=!1}if(r){var u,t=[],w;for(w in n)if(n.hasOwnProperty(w)){var x=n[w];x!==void 0&&x===x&&x!==null&&x.toString()!=="[object Object]"&&(t.push(w),t.push(Ua(String(x))))}var y=t.join("*");u=["1",Tr(y),y].join("*");d?(kg(3)||kg(1)||!p)&&bs("_gl",u,a,p,q):cs("_gl",u,a,p,q)}}var d=(a.tagName||"").toUpperCase()==="FORM",e=Mr(b,1,d),f=Mr(b,2,d),g=Mr(b,4,d),k=Mr(b,3,d);c(e,!1,!1);c(f,!0,!1);kg(1)&&c(g,!0,!0);for(var m in k)k.hasOwnProperty(m)&&
ds(m,k[m],a)}function ds(a,b,c){c.tagName.toLowerCase()==="a"?cs(a,b,c):c.tagName.toLowerCase()==="form"&&bs(a,b,c)}function cs(a,b,c,d,e){d=d===void 0?!1:d;e=e===void 0?!1:e;var f;if(f=c.href){var g;if(!(g=!kg(5)||d)){var k=z.location.href,m=Rr(c.href),n=Rr(k);g=!(m&&n&&m.Di===n.Di&&m.query===n.query&&m.fragment)}f=g}if(f){var p=$r(a,b,c.href,d,e);Wb.test(p)&&(c.href=p)}}
function bs(a,b,c,d,e){d=d===void 0?!1:d;e=e===void 0?!1:e;if(c){var f=(kg(12)?c.getAttribute("action"):c.action)||"";if(f){var g=(c.method||"").toLowerCase();if(g!=="get"||d){if(g==="get"||g==="post"){var k=$r(a,b,f,d,e);Wb.test(k)&&(c.action=k)}}else{for(var m=c.childNodes||[],n=!1,p=0;p<m.length;p++){var q=m[p];if(q.name===a){q.setAttribute("value",b);n=!0;break}}if(!n){var r=A.createElement("input");r.setAttribute("type","hidden");r.setAttribute("name",a);r.setAttribute("value",b);c.appendChild(r)}}}}}
function Ir(a){try{var b;a:{for(var c=a,d=100;c&&d>0;){if(c.href&&c.nodeName.match(/^a(?:rea)?$/i)){b=c;break a}c=c.parentNode;d--}b=null}var e=b;if(e){var f=e.protocol;f!=="http:"&&f!=="https:"||as(e,e.hostname)}}catch(g){}}function Jr(a){try{var b;if(b=kg(12)?a.getAttribute("action"):a.action){var c=fk(lk(b),"host");as(a,c)}}catch(d){}}function es(a,b,c,d){Hr();var e=c==="fragment"?2:1;d=!!d;Lr(a,b,e,d,!1);e===2&&Xa("TAGGING",23);d&&Xa("TAGGING",24)}
function fs(a,b){Hr();Lr(a,[hk(z.location,"host",!0)],b,!0,!0)}function gs(){var a=A.location.hostname,b=Or.exec(A.referrer);if(!b)return!1;var c=b[2],d=b[1],e="";if(c){var f=c.split("/"),g=f[1];e=g==="s"?decodeURIComponent(f[2]):decodeURIComponent(g)}else if(d){if(d.indexOf("xn--")===0)return!1;e=d.replace(/-/g,".").replace(/\.\./g,"-")}var k=a.replace(Pr,""),m=e.replace(Pr,""),n;if(!(n=k===m)){var p="."+m;n=k.length>=p.length&&k.substring(k.length-p.length,k.length)===p}return n}
function hs(a,b){return a===!1?!1:a||b||gs()};var is=["1"],js={},ks={};function ls(a,b){b=b===void 0?!0:b;var c=ms(a.prefix);if(js[c])ns(a);else if(os(c,a.path,a.domain)){var d=ks[ms(a.prefix)]||{id:void 0,Fg:void 0};b&&ps(a,d.id,d.Fg);ns(a)}else{var e=nk("auiddc");if(e)Xa("TAGGING",17),js[c]=e;else if(b){var f=ms(a.prefix),g=sr();qs(f,g,a);os(c,a.path,a.domain);ns(a,!0)}}}
function ns(a,b){if((b===void 0?0:b)&&Dr()){var c=yr(!1);c.error===0&&c.value&&"gcl_ctr"in c.value&&(delete c.value.gcl_ctr,zr(c))}Dm(["ad_storage","ad_user_data"])&&kg(10)&&Er()===-1&&Fr(0,a)}function ps(a,b,c){var d=ms(a.prefix),e=js[d];if(e){var f=e.split(".");if(f.length===2){var g=Number(f[1])||0;if(g){var k=e;b&&(k=e+"."+b+"."+(c?c:Math.floor(qb()/1E3)));qs(d,k,a,g*1E3)}}}}function qs(a,b,c,d){var e=ur(b,"1",c.domain,c.path),f=vr(c,d);f.Qb=rs();lr(a,e,f)}
function os(a,b,c){var d=tr(a,b,c,is,rs());if(!d)return!1;ss(a,d);return!0}function ss(a,b){var c=b.split(".");c.length===5?(js[a]=c.slice(0,2).join("."),ks[a]={id:c.slice(2,4).join("."),Fg:Number(c[4])||0}):c.length===3?ks[a]={id:c.slice(0,2).join("."),Fg:Number(c[2])||0}:js[a]=b}function ms(a){return(a||"_gcl")+"_au"}function ts(a){function b(){Dm(c)&&a()}var c=rs();Km(function(){b();Dm(c)||Lm(b,c)},c)}
function us(a){var b=Yr(!0),c=ms(a.prefix);ts(function(){var d=b[c];if(d){ss(c,d);var e=Number(js[c].split(".")[1])*1E3;if(e){Xa("TAGGING",16);var f=vr(a,e);f.Qb=rs();var g=ur(d,"1",a.domain,a.path);lr(c,g,f)}}})}function vs(a,b,c,d,e){e=e||{};var f=function(){var g={},k=tr(a,e.path,e.domain,is,rs());k&&(g[a]=k);return g};ts(function(){es(f,b,c,d)})}function rs(){return["ad_storage","ad_user_data"]};var ws={},xs=(ws.k={W:/^[\w-]+$/},ws.b={W:/^[\w-]+$/,Ki:!0},ws.i={W:/^[1-9]\d*$/},ws.h={W:/^\d+$/},ws.t={W:/^[1-9]\d*$/},ws.d={W:/^[A-Za-z0-9_-]+$/},ws.j={W:/^\d+$/},ws.u={W:/^[1-9]\d*$/},ws.l={W:/^[01]$/},ws.o={W:/^[1-9]\d*$/},ws.g={W:/^[01]$/},ws.s={W:/^.+$/},ws);var ys={},Cs=(ys[5]={Kg:{2:zs},xi:"2",vg:["k","i","b","u"]},ys[4]={Kg:{2:zs,GCL:As},xi:"2",vg:["k","i","b"]},ys[2]={Kg:{GS2:zs,GS1:Bs},xi:"GS2",vg:"sogtjlhd".split("")},ys);function Ds(a,b,c){var d=Cs[b];if(d){var e=a.split(".")[0];c==null||c(e);if(e){var f=d.Kg[e];if(f)return f(a,b)}}}
function zs(a,b){var c=a.split(".");if(c.length===3){var d={},e=Cs[b];if(e){for(var f=e.vg,g=l(c[2].split("$")),k=g.next();!k.done;k=g.next()){var m=k.value,n=m[0];if(f.indexOf(n)!==-1)try{var p=decodeURIComponent(m.substring(1)),q=xs[n];q&&(q.Ki?(d[n]=d[n]||[],d[n].push(p)):d[n]=p)}catch(r){}}return d}}}function Es(a,b,c){var d=Cs[b];if(d)return[d.xi,c||"1",Fs(a,b)].join(".")}
function Fs(a,b){var c=Cs[b];if(c){for(var d=[],e=l(c.vg),f=e.next();!f.done;f=e.next()){var g=f.value,k=xs[g];if(k){var m=a[g];if(m!==void 0)if(k.Ki&&Array.isArray(m))for(var n=l(m),p=n.next();!p.done;p=n.next())d.push(encodeURIComponent(""+g+p.value));else d.push(encodeURIComponent(""+g+m))}}return d.join("$")}}function As(a){var b=a.split(".");b.shift();var c=b.shift(),d=b.shift(),e={};return e.k=d,e.i=c,e.b=b,e}
function Bs(a){var b=a.split(".").slice(2);if(!(b.length<5||b.length>7)){var c={};return c.s=b[0],c.o=b[1],c.g=b[2],c.t=b[3],c.j=b[4],c.l=b[5],c.h=b[6],c}};var Gs=new Map([[5,"ad_storage"],[4,["ad_storage","ad_user_data"]],[2,"analytics_storage"]]);function Hs(a,b,c){if(Cs[b]){for(var d=[],e=ar(a,void 0,void 0,Gs.get(b)),f=l(e),g=f.next();!g.done;g=f.next()){var k=Ds(g.value,b,c);k&&d.push(Is(k))}return d}}function Js(a,b,c,d,e){d=d||{};var f=qr(d.domain,d.path),g=Es(b,c,f);if(!g)return 1;var k=vr(d,e,void 0,Gs.get(c));return lr(a,g,k)}function Ks(a,b){var c=b.W;return typeof c==="function"?c(a):c.test(a)}
function Is(a){for(var b=l(Object.keys(a)),c=b.next(),d={};!c.done;d={kf:void 0},c=b.next()){var e=c.value,f=a[e];d.kf=xs[e];d.kf?d.kf.Ki?a[e]=Array.isArray(f)?f.filter(function(g){return function(k){return Ks(k,g.kf)}}(d)):void 0:typeof f==="string"&&Ks(f,d.kf)||(a[e]=void 0):a[e]=void 0}return a};function Ls(a){for(var b=[],c=A.cookie.split(";"),d=new RegExp("^\\s*"+(a||"_gac")+"_(UA-\\d+-\\d+)=\\s*(.+?)\\s*$"),e=0;e<c.length;e++){var f=c[e].match(d);f&&b.push({Pi:f[1],value:f[2],timestamp:Number(f[2].split(".")[1])||0})}b.sort(function(g,k){return k.timestamp-g.timestamp});return b}
function Ms(a,b){var c=Ls(a),d={};if(!c||!c.length)return d;for(var e=0;e<c.length;e++){var f=c[e].value.split(".");if(!(f[0]!=="1"||b&&f.length<3||!b&&f.length!==3)&&Number(f[1])){d[c[e].Pi]||(d[c[e].Pi]=[]);var g={version:f[0],timestamp:Number(f[1])*1E3,da:f[2]};b&&f.length>3&&(g.labels=f.slice(3));d[c[e].Pi].push(g)}}return d};function Ns(){var a=String,b=z.location.hostname,c=z.location.pathname,d=b=Db(b);d.split(".").length>2&&(d=d.replace(/^(www[0-9]*|web|ftp|wap|home|m|w|amp|mobile)\./,""));b=d;c=Db(c);var e=c.split(";")[0];e=e.replace(/\/(ar|slp|web|index)?\/?$/,"");return a(Uq((""+b+e).toLowerCase()))};var Os=/^\w+$/,Ps=/^[\w-]+$/,Qs={},Rs=(Qs.aw="_aw",Qs.dc="_dc",Qs.gf="_gf",Qs.gp="_gp",Qs.gs="_gs",Qs.ha="_ha",Qs.ag="_ag",Qs.gb="_gb",Qs);function Ss(){return["ad_storage","ad_user_data"]}function Ts(a){return!kg(8)||Dm(a)}function Us(a,b){function c(){var d=Ts(b);d&&a();return d}Km(function(){c()||Lm(c,b)},b)}function Vs(a){return Ws(a).map(function(b){return b.da})}function Xs(a){return Ys(a).filter(function(b){return b.da}).map(function(b){return b.da})}
function Ys(a){var b=Zs(a.prefix),c=$s("gb",b),d=$s("ag",b);if(!d||!c)return[];var e=function(k){return function(m){m.type=k;return m}},f=Ws(c).map(e("gb")),g=at(d).map(e("ag"));return f.concat(g).sort(function(k,m){return m.timestamp-k.timestamp})}function bt(a,b,c,d,e,f){var g=fb(a,function(k){return k.da===c});g?(g.timestamp<d&&(g.timestamp=d,g.Wd=f),g.labels=ct(g.labels||[],e||[])):a.push({version:b,da:c,timestamp:d,labels:e,Wd:f})}
function at(a){for(var b=Hs(a,5)||[],c=[],d=l(b),e=d.next();!e.done;e=d.next()){var f=e.value,g=f,k=g.k,m=g.b,n=dt(f);if(n){var p=void 0;kg(9)&&(p=f.u);bt(c,"2",k,n,m||[],p)}}return c.sort(function(q,r){return r.timestamp-q.timestamp})}function Ws(a){for(var b=[],c=ar(a,A.cookie,void 0,Ss()),d=l(c),e=d.next();!e.done;e=d.next()){var f=et(e.value);if(f!=null){var g=f;bt(b,g.version,g.da,g.timestamp,g.labels)}}b.sort(function(k,m){return m.timestamp-k.timestamp});return ft(b)}
function gt(a,b){for(var c=[],d=l(a),e=d.next();!e.done;e=d.next()){var f=e.value;c.includes(f)||c.push(f)}for(var g=l(b),k=g.next();!k.done;k=g.next()){var m=k.value;c.includes(m)||c.push(m)}return c}function ht(a,b){var c=fb(a,function(d){return d.da===b.da});c?(c.Va=c.Va?b.Va?c.timestamp<b.timestamp?b.Va:c.Va:c.Va||0:b.Va||0,c.timestamp<b.timestamp&&(c.timestamp=b.timestamp,c.Wd=b.Wd),c.labels=gt(c.labels||[],b.labels||[]),c.jd=gt(c.jd||[],b.jd||[])):a.push(b)}
function it(){var a=Ar("gclid");if(!a||a.error||!a.value||typeof a.value!=="object")return null;var b=a.value;try{if(!("value"in b&&b.value)||typeof b.value!=="object")return null;var c=b.value,d=c.value;return d&&d.match(Ps)?{version:"",da:d,timestamp:Number(c.creationTimeMs)||0,labels:[],Va:c.linkDecorationSource||0,jd:[2]}:null}catch(e){return null}}
function jt(a){for(var b=[],c=ar(a,A.cookie,void 0,Ss()),d=l(c),e=d.next();!e.done;e=d.next()){var f=et(e.value);f!=null&&(f.Wd=void 0,f.Va=0,f.jd=[1],ht(b,f))}var g=it();g&&(g.Wd=void 0,g.Va=g.Va||0,g.jd=g.jd||[2],ht(b,g));b.sort(function(k,m){return m.timestamp-k.timestamp});return ft(b)}function ct(a,b){if(!a.length)return b;if(!b.length)return a;var c={};return a.concat(b).filter(function(d){return c.hasOwnProperty(d)?!1:c[d]=!0})}
function Zs(a){return a&&typeof a==="string"&&a.match(Os)?a:"_gcl"}function kt(a,b,c){var d=lk(a),e=fk(d,"query",!1,void 0,"gclsrc"),f={value:fk(d,"query",!1,void 0,"gclid"),Va:c?4:2};if(b&&(!f.value||!e)){var g=d.hash.replace("#","");f.value||(f.value=dk(g,"gclid",!1),f.Va=3);e||(e=dk(g,"gclsrc",!1))}return!f.value||e!==void 0&&e!=="aw"&&e!=="aw.ds"?[]:[f]}
function lt(a,b){var c=lk(a),d=fk(c,"query",!1,void 0,"gclid"),e=fk(c,"query",!1,void 0,"gclsrc"),f=fk(c,"query",!1,void 0,"wbraid");f=Bb(f);var g=fk(c,"query",!1,void 0,"gbraid"),k=fk(c,"query",!1,void 0,"gad_source"),m=fk(c,"query",!1,void 0,"dclid");if(b&&!(d&&e&&f&&g)){var n=c.hash.replace("#","");d=d||dk(n,"gclid",!1);e=e||dk(n,"gclsrc",!1);f=f||dk(n,"wbraid",!1);g=g||dk(n,"gbraid",!1);k=k||dk(n,"gad_source",!1)}return mt(d,e,m,f,g,k)}function nt(){return lt(z.location.href,!0)}
function mt(a,b,c,d,e,f){var g={},k=function(m,n){g[n]||(g[n]=[]);g[n].push(m)};g.gclid=a;g.gclsrc=b;g.dclid=c;if(a!==void 0&&a.match(Ps))switch(b){case void 0:k(a,"aw");break;case "aw.ds":k(a,"aw");k(a,"dc");break;case "ds":k(a,"dc");break;case "3p.ds":k(a,"dc");break;case "gf":k(a,"gf");break;case "ha":k(a,"ha")}c&&k(c,"dc");d!==void 0&&Ps.test(d)&&(g.wbraid=d,k(d,"gb"));e!==void 0&&Ps.test(e)&&(g.gbraid=e,k(e,"ag"));f!==void 0&&Ps.test(f)&&(g.gad_source=f,k(f,"gs"));return g}
function ot(a){for(var b=nt(),c=!0,d=l(Object.keys(b)),e=d.next();!e.done;e=d.next())if(b[e.value]!==void 0){c=!1;break}c&&(b=lt(z.document.referrer,!1),b.gad_source=void 0);pt(b,!1,a)}
function qt(a){ot(a);var b=kt(z.location.href,!0,!1);b.length||(b=kt(z.document.referrer,!1,!0));if(b.length){var c=b[0];a=a||{};var d=qb(),e=vr(a,d,!0),f=Ss(),g=function(){Ts(f)&&e.expires!==void 0&&xr("gclid",{value:{value:c.value,creationTimeMs:d,linkDecorationSource:c.Va},expires:Number(e.expires)})};Km(function(){g();Ts(f)||Lm(g,f)},f)}}
function pt(a,b,c,d,e){c=c||{};e=e||[];var f=Zs(c.prefix),g=d||qb(),k=Math.round(g/1E3),m=Ss(),n=!1,p=!1,q=function(){if(Ts(m)){var r=vr(c,g,!0);r.Qb=m;for(var v=function(L,T){var F=$s(L,f);F&&(lr(F,T,r),L!=="gb"&&(n=!0))},u=function(L){var T=["GCL",k,L];e.length>0&&T.push(e.join("."));return T.join(".")},t=l(["aw","dc","gf","ha","gp"]),w=t.next();!w.done;w=t.next()){var x=w.value;a[x]&&v(x,u(a[x][0]))}if(!n&&a.gb){var y=a.gb[0],B=$s("gb",f);!b&&Ws(B).some(function(L){return L.da===y&&L.labels&&L.labels.length>
0})||v("gb",u(y))}}if(!p&&a.gbraid&&Ts("ad_storage")&&(p=!0,!n)){var C=a.gbraid,E=$s("ag",f);if(b||!at(E).some(function(L){return L.da===C&&L.labels&&L.labels.length>0})){var H={},N=(H.k=C,H.i=""+k,H.b=e,H);Js(E,N,5,c,g)}}rt(a,f,g,c)};Km(function(){q();Ts(m)||Lm(q,m)},m)}
function rt(a,b,c,d){if(a.gad_source!==void 0&&Ts("ad_storage")){if(kg(4)){var e=Lc();if(e==="r"||e==="h")return}var f=a.gad_source,g=$s("gs",b);if(g){var k=Math.floor((qb()-(Kc()||0))/1E3),m;if(kg(9)){var n=Ns(),p={};m=(p.k=f,p.i=""+k,p.u=n,p)}else{var q={};m=(q.k=f,q.i=""+k,q)}Js(g,m,5,d,c)}}}
function st(a,b){var c=Yr(!0);Us(function(){for(var d=Zs(b.prefix),e=0;e<a.length;++e){var f=a[e];if(Rs[f]!==void 0){var g=$s(f,d),k=c[g];if(k){var m=Math.min(tt(k),qb()),n;b:{for(var p=m,q=ar(g,A.cookie,void 0,Ss()),r=0;r<q.length;++r)if(tt(q[r])>p){n=!0;break b}n=!1}if(!n){var v=vr(b,m,!0);v.Qb=Ss();lr(g,k,v)}}}}pt(mt(c.gclid,c.gclsrc),!1,b)},Ss())}
function ut(a){var b=["ag"],c=Yr(!0),d=Zs(a.prefix);Us(function(){for(var e=0;e<b.length;++e){var f=$s(b[e],d);if(f){var g=c[f];if(g){var k=Ds(g,5);if(k){var m=dt(k);m||(m=qb());var n;a:{for(var p=m,q=Hs(f,5),r=0;r<q.length;++r)if(dt(q[r])>p){n=!0;break a}n=!1}if(n)break;k.i=""+Math.round(m/1E3);Js(f,k,5,a,m)}}}}},["ad_storage"])}function $s(a,b){var c=Rs[a];if(c!==void 0)return b+c}function tt(a){return vt(a.split(".")).length!==0?(Number(a.split(".")[1])||0)*1E3:0}
function dt(a){return a?(Number(a.i)||0)*1E3:0}function et(a){var b=vt(a.split("."));return b.length===0?null:{version:b[0],da:b[2],timestamp:(Number(b[1])||0)*1E3,labels:b.slice(3)}}function vt(a){return a.length<3||a[0]!=="GCL"&&a[0]!=="1"||!/^\d+$/.test(a[1])||!Ps.test(a[2])?[]:a}
function wt(a,b,c,d,e){if(Array.isArray(b)&&$q(z)){var f=Zs(e),g=function(){for(var k={},m=0;m<a.length;++m){var n=$s(a[m],f);if(n){var p=ar(n,A.cookie,void 0,Ss());p.length&&(k[n]=p.sort()[p.length-1])}}return k};Us(function(){es(g,b,c,d)},Ss())}}
function xt(a,b,c,d){if(Array.isArray(a)&&$q(z)){var e=["ag"],f=Zs(d),g=function(){for(var k={},m=0;m<e.length;++m){var n=$s(e[m],f);if(!n)return{};var p=Hs(n,5);if(p.length){var q=p.sort(function(r,v){return dt(v)-dt(r)})[0];k[n]=Es(q,5)}}return k};Us(function(){es(g,a,b,c)},["ad_storage"])}}function ft(a){return a.filter(function(b){return Ps.test(b.da)})}
function zt(a,b){if($q(z)){for(var c=Zs(b.prefix),d={},e=0;e<a.length;e++)Rs[a[e]]&&(d[a[e]]=Rs[a[e]]);Us(function(){jb(d,function(f,g){var k=ar(c+g,A.cookie,void 0,Ss());k.sort(function(v,u){return tt(u)-tt(v)});if(k.length){var m=k[0],n=tt(m),p=vt(m.split(".")).length!==0?m.split(".").slice(3):[],q={},r;r=vt(m.split(".")).length!==0?m.split(".")[2]:void 0;q[f]=[r];pt(q,!0,b,n,p)}})},Ss())}}
function At(a){var b=["ag"],c=["gbraid"];Us(function(){for(var d=Zs(a.prefix),e=0;e<b.length;++e){var f=$s(b[e],d);if(!f)break;var g=Hs(f,5);if(g.length){var k=g.sort(function(q,r){return dt(r)-dt(q)})[0],m=dt(k),n=k.b,p={};p[c[e]]=k.k;pt(p,!0,a,m,n)}}},["ad_storage"])}function Bt(a,b){for(var c=0;c<b.length;++c)if(a[b[c]])return!0;return!1}
function Ct(a){function b(k,m,n){n&&(k[m]=n)}if(Hm()){var c=nt(),d;a.includes("gad_source")&&(d=c.gad_source!==void 0?c.gad_source:Yr(!1)._gs);if(Bt(c,a)||d){var e={};b(e,"gclid",c.gclid);b(e,"dclid",c.dclid);b(e,"gclsrc",c.gclsrc);b(e,"wbraid",c.wbraid);b(e,"gbraid",c.gbraid);fs(function(){return e},3);var f={},g=(f._up="1",f);b(g,"_gs",d);fs(function(){return g},1)}}}
function Dt(a){if(!kg(1))return null;var b=Yr(!0).gad_source;if(b!=null)return z.location.hash="",b;if(kg(2)){var c=lk(z.location.href);b=fk(c,"query",!1,void 0,"gad_source");if(b!=null)return b;var d=nt();if(Bt(d,a))return"0"}return null}function Et(a){var b=Dt(a);b!=null&&fs(function(){var c={};return c.gad_source=b,c},4)}
function Ft(a,b,c){var d=[];if(b.length===0)return d;for(var e={},f=0;f<b.length;f++){var g=b[f],k=g.type?g.type:"gcl";(g.labels||[]).indexOf(c)===-1?(a.push(0),e[k]||d.push(g)):a.push(1);e[k]=!0}return d}function Gt(a,b,c,d){var e=[];c=c||{};if(!Ts(Ss()))return e;var f=Ws(a),g=Ft(e,f,b);if(g.length&&!d)for(var k=l(g),m=k.next();!m.done;m=k.next()){var n=m.value,p=n.timestamp,q=[n.version,Math.round(p/1E3),n.da].concat(n.labels||[],[b]).join("."),r=vr(c,p,!0);r.Qb=Ss();lr(a,q,r)}return e}
function Ht(a,b){var c=[];b=b||{};var d=Ys(b),e=Ft(c,d,a);if(e.length)for(var f=l(e),g=f.next();!g.done;g=f.next()){var k=g.value,m=Zs(b.prefix),n=$s(k.type,m);if(!n)break;var p=k,q=p.version,r=p.da,v=p.labels,u=p.timestamp,t=Math.round(u/1E3);if(k.type==="ag"){var w={},x=(w.k=r,w.i=""+t,w.b=(v||[]).concat([a]),w);Js(n,x,5,b,u)}else if(k.type==="gb"){var y=[q,t,r].concat(v||[],[a]).join("."),B=vr(b,u,!0);B.Qb=Ss();lr(n,y,B)}}return c}
function It(a,b){var c=Zs(b),d=$s(a,c);if(!d)return 0;var e;e=a==="ag"?at(d):Ws(d);for(var f=0,g=0;g<e.length;g++)f=Math.max(f,e[g].timestamp);return f}function Jt(a){for(var b=0,c=l(Object.keys(a)),d=c.next();!d.done;d=c.next())for(var e=a[d.value],f=0;f<e.length;f++)b=Math.max(b,Number(e[f].timestamp));return b}function Kt(a){var b=Math.max(It("aw",a),Jt(Ts(Ss())?Ms():{})),c=Math.max(It("gb",a),Jt(Ts(Ss())?Ms("_gac_gb",!0):{}));c=Math.max(c,It("ag",a));return c>b};
var Lt=function(a,b){b=b===void 0?!1:b;var c=Fo("ads_pageview",function(){return{}});if(c[a])return!1;b||(c[a]=!0);return!0},Mt=function(a){return mk(a,"gclid dclid gbraid wbraid gclaw gcldc gclha gclgf gclgb _gl".split(" "),"0")},Ut=function(a,b,c,d,e){var f=Zs(a.prefix);if(Lt(f,!0)){var g=nt(),k=[],m=g.gclid,n=g.dclid,p=g.gclsrc||"aw",q=Nt(),r=q.tf,v=q.Ek;!m||p!=="aw.ds"&&p!=="aw"&&p!=="ds"&&p!=="3p.ds"||k.push({da:m,uf:p});n&&k.push({da:n,uf:"ds"});k.length===2&&O(147);k.length===0&&g.wbraid&&
k.push({da:g.wbraid,uf:"gb"});k.length===0&&p==="aw.ds"&&k.push({da:"",uf:"aw.ds"});Ot(function(){var u=R(Pt());if(u){ls(a);var t=[],w=u?js[ms(a.prefix)]:void 0;w&&t.push("auid="+w);if(R(M.m.T)){e&&t.push("userId="+e);var x=Bn(wn.Mh);if(x===void 0)An(wn.Nh,!0);else{var y=Bn(wn.cf);t.push("ga_uid="+y+"."+x)}}var B=A.referrer?fk(lk(A.referrer),"host"):"",C=u||!d?k:[];C.length===0&&(Qt.test(B)||Rt.test(B))&&C.push({da:"",uf:""});if(C.length!==0||r!==void 0){B&&t.push("ref="+encodeURIComponent(B));var E=
St();t.push("url="+encodeURIComponent(E));t.push("tft="+qb());var H=Kc();H!==void 0&&t.push("tfd="+Math.round(H));var N=il(!0);t.push("frm="+N);r!==void 0&&t.push("gad_source="+encodeURIComponent(r));v!==void 0&&t.push("gad_source_src="+encodeURIComponent(v.toString()));if(!c){var L={};c=wp(mp(new lp(0),(L[M.m.qa]=Tp.C[M.m.qa],L)))}t.push("gtm="+Tq({Da:b}));Gq()&&t.push("gcs="+Hq());t.push("gcd="+Lq(c));Oq()&&t.push("dma_cps="+Mq());t.push("dma="+Nq());Fq(c)?t.push("npa=0"):t.push("npa=1");Qq()&&
t.push("_ng=1");kq(sq())&&t.push("tcfd="+Pq());var T=zq();T&&t.push("gdpr="+T);var F=yq();F&&t.push("gdpr_consent="+F);I(23)&&t.push("apve=0");I(117)&&Yr(!1)._up&&t.push("gtm_up=1");Jj()&&t.push("tag_exp="+Jj());if(C.length>0)for(var S=0;S<C.length;S++){var ba=C[S],ha=ba.da,Y=ba.uf;if(!Tt(a.prefix,Y+"."+ha,w!==void 0)){var Q='http://ad.doubleclick.net/pagead/regclk?'+t.join("&");ha!==""?Q=Y==="gb"?Q+"&wbraid="+ha:Q+"&gclid="+ha+"&gclsrc="+Y:Y==="aw.ds"&&(Q+="&gclsrc=aw.ds");Dc(Q)}}else if(r!==void 0&&
!Tt(a.prefix,"gad",w!==void 0)){var ia='http://ad.doubleclick.net/pagead/regclk?'+t.join("&");Dc(ia)}}}})}},Tt=function(a,b,c){var d=Fo("joined_auid",function(){return{}}),e=(c?a||"_gcl":"")+"."+b;if(d[e])return!0;d[e]=!0;return!1},Nt=function(){var a=lk(z.location.href),b=void 0,c=void 0,d=fk(a,"query",!1,void 0,"gad_source"),e,f=a.hash.replace("#","").match(Vt);e=f?f[1]:void 0;d&&e?(b=d,c=1):d?(b=d,c=2):e&&(b=e,c=3);return{tf:b,Ek:c}},St=function(){var a=il(!1)===1?z.top.location.href:z.location.href;
return a=a.replace(/[\?#].*$/,"")},Wt=function(a){var b=[];jb(a,function(c,d){d=ft(d);for(var e=[],f=0;f<d.length;f++)e.push(d[f].da);e.length&&b.push(c+":"+e.join(","))});return b.join(";")},Yt=function(a,b){return Xt("dc",a,b)},Zt=function(a,b){return Xt("aw",a,b)},Xt=function(a,b,c){if(a==="aw"||a==="dc"||a==="gb"){var d=nk("gcl"+a);if(d)return d.split(".")}var e=Zs(b);if(e==="_gcl"){var f=!R(Pt())&&c,g;g=nt()[a]||[];if(g.length>0)return f?["0"]:g}var k=$s(a,e);return k?Vs(k):[]},Ot=function(a){var b=
Pt();xo(function(){a();R(b)||Lm(a,b)},b)},Pt=function(){return[M.m.R,M.m.T]},Qt=/^(?:www\.)?google(?:\.com?)?(?:\.[a-z]{2}t?)?$/,Rt=/^www\.googleadservices\.com$/,Vt=/^gad_source[_=](\d+)$/;function $t(){return Fo("dedupe_gclid",function(){return sr()})};var au=/^(www\.)?google(\.com?)?(\.[a-z]{2}t?)?$/,bu=/^www.googleadservices.com$/;function cu(a){a||(a=du());return a.fo?!1:a.fn||a.gn||a.kn||a.hn||a.tf||a.Pm||a.jn||a.Um?!0:!1}function du(){var a={},b=Yr(!0);a.fo=!!b._up;var c=nt();a.fn=c.aw!==void 0;a.gn=c.dc!==void 0;a.kn=c.wbraid!==void 0;a.hn=c.gbraid!==void 0;a.jn=c.gclsrc==="aw.ds";a.tf=Nt().tf;var d=A.referrer?fk(lk(A.referrer),"host"):"";a.Um=au.test(d);a.Pm=bu.test(d);return a};var eu=["https://www.google.com","https://www.youtube.com"];
function fu(){if(I(113)){if(Bn(wn.af))return O(176),wn.af;if(Bn(wn.hk))return O(170),wn.af;var a=kl();if(!a)O(171);else if(a.opener){var b=function(e){if(eu.includes(e.origin)){e.data.action==="gcl_transfer"&&e.data.gadSource?An(wn.af,{gadSource:e.data.gadSource}):O(173);var f;(f=e.stopImmediatePropagation)==null||f.call(e);Hk(a,"message",b)}else O(172)};if(Gk(a,"message",b)){An(wn.hk,!0);for(var c=l(eu),d=c.next();!d.done;d=c.next())a.opener.postMessage({action:"gcl_setup"},d.value);O(174);return wn.af}O(175)}}}
;var gu=RegExp("^UA-\\d+-\\d+%3A[\\w-]+(?:%2C[\\w-]+)*(?:%3BUA-\\d+-\\d+%3A[\\w-]+(?:%2C[\\w-]+)*)*$"),hu=/^~?[\w-]+(?:\.~?[\w-]+)*$/,iu=/^\d+\.fls\.doubleclick\.net$/,ju=/;gac=([^;?]+)/,ku=/;gacgb=([^;?]+)/;
function lu(a,b){if(iu.test(A.location.host)){var c=A.location.href.match(b);return c&&c.length===2&&c[1].match(gu)?ek(c[1])||"":""}for(var d=[],e=l(Object.keys(a)),f=e.next();!f.done;f=e.next()){for(var g=f.value,k=[],m=a[g],n=0;n<m.length;n++)k.push(m[n].da);d.push(g+":"+k.join(","))}return d.length>0?d.join(";"):""}
function mu(a,b,c){for(var d=Ts(Ss())?Ms("_gac_gb",!0):{},e=[],f=!1,g=l(Object.keys(d)),k=g.next();!k.done;k=g.next()){var m=k.value,n=Gt("_gac_gb_"+m,a,b,c);f=f||n.length!==0&&n.some(function(p){return p===1});e.push(m+":"+n.join(","))}return{Om:f?e.join(";"):"",Nm:lu(d,ku)}}function nu(a){var b=A.location.href.match(new RegExp(";"+a+"=([^;?]+)"));return b&&b.length===2&&b[1].match(hu)?b[1]:void 0}
function ou(a){var b=kg(9),c={},d,e,f;iu.test(A.location.host)&&(d=nu("gclgs"),e=nu("gclst"),b&&(f=nu("gcllp")));if(d&&e&&(!b||f))c.zg=d,c.Bg=e,c.Ag=f;else{var g=qb(),k=at((a||"_gcl")+"_gs"),m=k.map(function(q){return q.da}),n=k.map(function(q){return g-q.timestamp}),p=[];b&&(p=k.map(function(q){return q.Wd}));m.length>0&&n.length>0&&(!b||p.length>0)&&(c.zg=m.join("."),c.Bg=n.join("."),b&&p.length>0&&(c.Ag=p.join(".")))}return c}
function pu(a,b,c,d){d=d===void 0?!1:d;if(iu.test(A.location.host)){var e=nu(c);if(e)return e.split(".").map(function(g){return{da:g}})}else{if(b==="gclid"){var f=(a||"_gcl")+"_aw";return d?jt(f):Ws(f)}if(b==="wbraid")return Ws((a||"_gcl")+"_gb");if(b==="braids")return Ys({prefix:a})}return[]}function qu(a){return iu.test(A.location.host)?!(nu("gclaw")||nu("gac")):Kt(a)}
function ru(a,b,c){var d;d=c?Ht(a,b):Gt((b&&b.prefix||"_gcl")+"_gb",a,b);return d.length===0||d.every(function(e){return e===0})?"":d.join(".")};function su(){var a=z.__uspapi;if(ab(a)){var b="";try{a("getUSPData",1,function(c,d){if(d&&c){var e=c.uspString;e&&RegExp("^[\\da-zA-Z-]{1,20}$").test(e)&&(b=e)}})}catch(c){}return b}};
var xu=function(a){if(a.eventName===M.m.ia&&U(a,"hit_type")==="page_view")if(I(24)){V(a,"redact_click_ids",P(a.D,M.m.ka)!=null&&P(a.D,M.m.ka)!==!1&&!R([M.m.R,M.m.T]));var b=tu(a),c=P(a.D,M.m.Aa)!==!1;c||W(a,M.m.Wg,"1");var d=Zs(b.prefix),e=U(a,"is_server_side_destination");if(!U(a,"consent_updated")&&!U(a,"user_id_updated")&&!U(a,"tunnel_updated")){var f=P(a.D,M.m.ib),g=P(a.D,M.m.Ba)||{};uu({Pd:c,Xd:g,be:f,Dc:b});if(!e&&!Lt(d)){a.isAborted=!0;return}}if(e)a.isAborted=!0;else{W(a,M.m.vc,M.m.oc);if(U(a,
"consent_updated"))W(a,M.m.vc,M.m.wl),W(a,M.m.Sb,"1");else if(U(a,"user_id_updated"))W(a,M.m.vc,M.m.Cl);else if(U(a,"tunnel_updated"))W(a,M.m.vc,M.m.zl);else{var k=nt();W(a,M.m.Kc,k.gclid);W(a,M.m.Mc,k.dclid);W(a,M.m.tj,k.gclsrc);vu(a,M.m.Kc)||vu(a,M.m.Mc)||(W(a,M.m.od,k.wbraid),W(a,M.m.ke,k.gbraid));W(a,M.m.Ga,A.referrer?fk(lk(A.referrer),"host"):"");W(a,M.m.oa,St());if(I(27)&&jc){var m=fk(lk(jc),"host");m&&W(a,M.m.Pj,m)}if(!U(a,"tunnel_updated")){var n=Nt(),p=n.Ek;W(a,M.m.ie,n.tf);W(a,M.m.je,p)}W(a,
M.m.Xb,il(!0));var q=du();cu(q)&&W(a,M.m.Sc,"1");W(a,M.m.vj,$t());Yr(!1)._up==="1"&&W(a,M.m.Jj,"1")}on=!0;W(a,M.m.hb);W(a,M.m.Db);var r=R([M.m.R,M.m.T]);r&&(W(a,M.m.hb,wu()),c&&(ls(b),W(a,M.m.Db,js[ms(b.prefix)])));W(a,M.m.Cb);W(a,M.m.Qa);if(!vu(a,M.m.Kc)&&!vu(a,M.m.Mc)&&qu(d)){var v=Xs(b);v.length>0&&W(a,M.m.Cb,v.join("."))}else if(!vu(a,M.m.od)&&r){var u=Vs(d+"_aw");u.length>0&&W(a,M.m.Qa,u.join("."))}I(31)&&W(a,M.m.Kj,Lc());a.D.isGtmEvent&&(a.D.C[M.m.qa]=Tp.C[M.m.qa]);Fq(a.D)?W(a,M.m.bc,!1):W(a,
M.m.bc,!0);V(a,"add_tag_timing",!0);var t=su();t!==void 0&&W(a,M.m.Ld,t||"error");var w=zq();w&&W(a,M.m.wc,w);if(I(132))try{var x=Intl.DateTimeFormat().resolvedOptions().timeZone;W(a,M.m.Ah,x||"-")}catch(B){W(a,M.m.Ah,"e")}var y=yq();y&&W(a,M.m.zc,y);V(a,"speculative",!1)}}else a.isAborted=!0},tu=function(a){var b={prefix:P(a.D,M.m.nb)||P(a.D,M.m.Ja),domain:P(a.D,M.m.Ra),Pb:P(a.D,M.m.Sa),flags:P(a.D,M.m.Xa)};a.D.isGtmEvent&&(b.path=P(a.D,M.m.ob));return b},yu=function(a,b){var c,d,e,f,g,k,m,n;c=a.Pd;
d=a.Xd;e=a.be;f=a.Da;g=a.D;k=a.Zd;m=a.Ho;n=a.jl;uu({Pd:c,Xd:d,be:e,Dc:b});c&&m!==!0&&(n!=null?n=String(n):n=void 0,Ut(b,f,g,k,n))},zu=function(a,b){if(!U(a,"tunnel_updated")){var c=fu();if(c){var d=Bn(c),e=function(g){V(a,"tunnel_updated",!0);var k=vu(a,M.m.ie),m=vu(a,M.m.je);W(a,M.m.ie,String(g.gadSource));W(a,M.m.je,4);V(a,"consent_updated");V(a,"user_id_updated");W(a,M.m.Sb);b();W(a,M.m.ie,k);W(a,M.m.je,m);V(a,"tunnel_updated",!1)};if(d)e(d);else{var f=void 0;f=Cn(c,function(g,k){e(k);Dn(c,f)})}}}},
uu=function(a){var b,c,d,e;b=a.Pd;c=a.Xd;d=a.be;e=a.Dc;b&&(hs(c[M.m.Tc],!!c[M.m.Z])&&(st(Au,e),ut(e),us(e)),il()!==2?qt(e):ot(e),zt(Au,e),At(e));c[M.m.Z]&&(wt(Au,c[M.m.Z],c[M.m.Zb],!!c[M.m.Hb],e.prefix),xt(c[M.m.Z],c[M.m.Zb],!!c[M.m.Hb],e.prefix),vs(ms(e.prefix),c[M.m.Z],c[M.m.Zb],!!c[M.m.Hb],e),vs("FPAU",c[M.m.Z],c[M.m.Zb],!!c[M.m.Hb],e));d&&(I(96)?Ct(Bu):Ct(Cu));Et(Cu)},Du=function(a,b,c,d){var e,f,g;e=a.kl;f=a.callback;g=a.Hk;if(typeof f==="function")if(e===M.m.Qa&&g===void 0){var k=d(b.prefix,
c);k.length===0?f(void 0):k.length===1?f(k[0]):f(k)}else e===M.m.Db?(O(65),ls(b,!1),f(js[ms(b.prefix)])):f(g)},Eu=function(a,b){Array.isArray(b)||(b=[b]);var c=U(a,"hit_type");return b.indexOf(c)>=0},Au=["aw","dc","gb"],Cu=["aw","dc","gb","ag"],Bu=["aw","dc","gb","ag","gad_source"];
function Fu(a){var b=P(a.D,M.m.Yb),c=P(a.D,M.m.xc);b&&!c?(a.eventName!==M.m.ia&&a.eventName!==M.m.Jc&&O(131),a.isAborted=!0):!b&&c&&(O(132),a.isAborted=!0)}function Gu(a){var b=R(M.m.R)?Eo.pscdl:"denied";b!=null&&W(a,M.m.Sf,b)}function Hu(a){var b=il(!0);W(a,M.m.Xb,b)}function Iu(a){Qq()&&W(a,M.m.Qc,1)}function wu(){var a=A.title;if(a===void 0||a==="")return"";a=encodeURIComponent(a);for(var b=256;b>0&&ek(a.substring(0,b))===void 0;)b--;return ek(a.substring(0,b))||""}
function Ju(a){Ku(a,"ce",P(a.D,M.m.Sa))}function Ku(a,b,c){vu(a,M.m.Md)||W(a,M.m.Md,{});vu(a,M.m.Md)[b]=c}function Lu(a){I(106)&&V(a,"transmission_type",1)}function Mu(a){var b=Ya("GTAG_EVENT_FEATURE_CHANNEL");b&&(W(a,M.m.Ge,b),Wa.GTAG_EVENT_FEATURE_CHANNEL=lj)}function Nu(a){if(I(84)){var b=jp(a.D,M.m.Pc);b&&W(a,M.m.Pc,b)}};function Qu(a){var b,c=z,d=[];try{c.navigation&&c.navigation.entries&&(d=c.navigation.entries())}catch(k){}b=d;for(var e=b.length-1;e>=0;e--){var f=b[e],g=f.url&&f.url.match("[?&#]"+a+"=([^&#]+)");if(g&&g.length===2)return g[1]}};function Zu(a,b,c,d){var e=tc(),f;if(e===1)a:{var g=Dj;g=g.toLowerCase();for(var k="https://"+g,m="http://"+g,n=1,p=A.getElementsByTagName("script"),q=0;q<p.length&&q<100;q++){var r=p[q].src;if(r){r=r.toLowerCase();if(r.indexOf(m)===0){f=3;break a}n===1&&r.indexOf(k)===0&&(n=2)}}f=n}else f=e;return(f===2||d||"http:"!==z.location.protocol?a:b)+c};function $u(a){return typeof a!=="object"||a===null?{}:a}function av(a){return a===void 0||a===null?"":typeof a==="object"?a.toString():String(a)}function bv(a){if(a!==void 0&&a!==null)return av(a)}function cv(a){return typeof a==="number"?a:bv(a)};function ov(a){return{getDestinationId:function(){return a.target.destinationId},getEventName:function(){return a.eventName},setEventName:function(b){a.eventName=b},getHitData:function(b){return vu(a,b)},setHitData:function(b,c){W(a,b,c)},setHitDataIfNotDefined:function(b,c){vu(a,b)===void 0&&W(a,b,c)},copyToHitData:function(b,c){a.copyToHitData(b,c)},getMetadata:function(b){return U(a,b)},setMetadata:function(b,c){V(a,b,c)},isAborted:function(){return a.isAborted},abort:function(){a.isAborted=!0},
getFromEventContext:function(b){return P(a.D,b)},hc:function(){return a},getHitKeys:function(){return Object.keys(a.C)}}};var qv=function(a){var b=pv[Tl?a.target.destinationId:hm(a.target.destinationId)];if(!a.isAborted&&b)for(var c=ov(a),d=0;d<b.length;++d){try{b[d](c)}catch(e){a.isAborted=!0}if(a.isAborted)break}},rv=function(a,b){var c=pv[a];c||(c=pv[a]=[]);c.push(b)},pv={};function vv(a,b){return arguments.length===1?wv("set",a):wv("set",a,b)}function xv(a,b){return arguments.length===1?wv("config",a):wv("config",a,b)}function yv(a,b,c){c=c||{};c[M.m.yc]=a;return wv("event",b,c)}function wv(){return arguments};var Av=function(){this.messages=[];this.C=[]};Av.prototype.enqueue=function(a,b,c){var d=this.messages.length+1;a["gtm.uniqueEventId"]=b;a["gtm.priorityId"]=d;var e=Object.assign({},c,{eventId:b,priorityId:d,fromContainerExecution:!0}),f={message:a,notBeforeEventId:b,priorityId:d,messageContext:e};this.messages.push(f);for(var g=0;g<this.C.length;g++)try{this.C[g](f)}catch(k){}};Av.prototype.listen=function(a){this.C.push(a)};
Av.prototype.get=function(){for(var a={},b=0;b<this.messages.length;b++){var c=this.messages[b],d=a[c.notBeforeEventId];d||(d=[],a[c.notBeforeEventId]=d);d.push(c)}return a};Av.prototype.prune=function(a){for(var b=[],c=[],d=0;d<this.messages.length;d++){var e=this.messages[d];e.notBeforeEventId===a?b.push(e):c.push(e)}this.messages=c;return b};function Bv(a,b,c){c.eventMetadata=c.eventMetadata||{};c.eventMetadata.source_canonical_id=Xf.canonicalContainerId;Cv().enqueue(a,b,c)}
function Dv(){var a=Ev;Cv().listen(a)}function Cv(){return Fo("mb",function(){return new Av})};var Fv,Gv=!1;function Hv(){Gv=!0;Fv=Fv||{}}function Iv(a){Gv||Hv();return Fv[a]};function Jv(){var a=z.screen;return{width:a?a.width:0,height:a?a.height:0}}
function Kv(a){if(A.hidden)return!0;var b=a.getBoundingClientRect();if(b.top===b.bottom||b.left===b.right||!z.getComputedStyle)return!0;var c=z.getComputedStyle(a,null);if(c.visibility==="hidden")return!0;for(var d=a,e=c;d;){if(e.display==="none")return!0;var f=e.opacity,g=e.filter;if(g){var k=g.indexOf("opacity(");k>=0&&(g=g.substring(k+8,g.indexOf(")",k)),g.charAt(g.length-1)==="%"&&(g=g.substring(0,g.length-1)),f=String(Math.min(Number(g),Number(f))))}if(f!==void 0&&Number(f)<=0)return!0;(d=d.parentElement)&&
(e=z.getComputedStyle(d,null))}return!1}
var Mv=function(a){var b=Lv(),c=b.height,d=b.width,e=a.getBoundingClientRect(),f=e.bottom-e.top,g=e.right-e.left;return f&&g?(1-Math.min((Math.max(0-e.left,0)+Math.max(e.right-d,0))/g,1))*(1-Math.min((Math.max(0-e.top,0)+Math.max(e.bottom-c,0))/f,1)):0},Lv=function(){var a=A.body,b=A.documentElement||a&&a.parentElement,c,d;if(A.compatMode&&A.compatMode!=="BackCompat")c=b?b.clientHeight:0,d=b?b.clientWidth:0;else{var e=function(f,g){return f&&g?Math.min(f,g):Math.max(f,g)};c=e(b?b.clientHeight:0,a?
a.clientHeight:0);d=e(b?b.clientWidth:0,a?a.clientWidth:0)}return{width:d,height:c}};var Pv=function(a){if(Nv){if(a>=0&&a<Ov.length&&Ov[a]){var b;(b=Ov[a])==null||b.disconnect();Ov[a]=void 0}}else z.clearInterval(a)},Sv=function(a,b,c){for(var d=0;d<c.length;d++)c[d]>1?c[d]=1:c[d]<0&&(c[d]=0);if(Nv){var e=!1;D(function(){e||Qv(a,b,c)()});return Rv(function(f){e=!0;for(var g={yf:0};g.yf<f.length;g={yf:g.yf},g.yf++)D(function(k){return function(){a(f[k.yf])}}(g))},
b,c)}return z.setInterval(Qv(a,b,c),1E3)},Qv=function(a,b,c){function d(k,m){var n={top:0,bottom:0,right:0,left:0,width:0,height:0},p={boundingClientRect:k.getBoundingClientRect(),intersectionRatio:m,intersectionRect:n,isIntersecting:m>0,rootBounds:n,target:k,time:qb()};D(function(){a(p)})}for(var e=[],f=[],g=0;g<b.length;g++)e.push(0),f.push(-1);c.sort(function(k,m){return k-m});return function(){for(var k=0;k<b.length;k++){var m=Mv(b[k]);if(m>e[k])for(;f[k]<c.length-1&&m>=c[f[k]+1];)d(b[k],m),f[k]++;
else if(m<e[k])for(;f[k]>=0&&m<=c[f[k]];)d(b[k],m),f[k]--;e[k]=m}}},Rv=function(a,b,c){for(var d=new z.IntersectionObserver(a,{threshold:c}),e=0;e<b.length;e++)d.observe(b[e]);for(var f=0;f<Ov.length;f++)if(!Ov[f])return Ov[f]=d,f;return Ov.push(d)-1},Ov=[],Nv=!(!z.IntersectionObserver||!z.IntersectionObserverEntry);
var Uv=function(a){return a.tagName+":"+a.isVisible+":"+a.aa.length+":"+Tv.test(a.aa)},hw=function(a){a=a||{Ud:!0,Vd:!0,Jg:void 0};a.Lb=a.Lb||{email:!0,phone:!1,address:!1};var b=Vv(a),c=Wv[b];if(c&&qb()-c.timestamp<200)return c.result;var d=Xv(),e=d.status,f=[],g,k,m=[];if(!I(33)){if(a.Lb&&a.Lb.email){var n=Yv(d.elements);f=Zv(n,a&&a.nf);g=$v(f);n.length>10&&(e="3")}!a.Jg&&g&&(f=[g]);for(var p=0;p<f.length;p++)m.push(aw(f[p],!!a.Ud,!!a.Vd));m=m.slice(0,10)}else if(a.Lb){}g&&(k=aw(g,!!a.Ud,!!a.Vd));var E={elements:m,
Gi:k,status:e};Wv[b]={timestamp:qb(),result:E};return E},iw=function(a,b){if(a){var c=a.trim().replaceAll(/\s+/g,"").replaceAll(/(\d{2,})\./g,"$1").replaceAll(/-/g,"").replaceAll(/\((\d+)\)/g,"$1");if(b&&c.match(/^\+?\d{3,7}$/))return c;c.charAt(0)!=="+"&&(c="+"+c);if(c.match(/^\+\d{10,15}$/))return c}},uw=function(a){var b=tw(/^(\w|[- ])+$/)(a);if(!b)return b;var c=b.replaceAll(/[- ]+/g,"");return c.length>10?void 0:c},tw=function(a){return function(b){var c=b.match(a);return c?c[0].trim().toLowerCase():
void 0}},gw=function(a,b,c){var d=a.element,e={aa:a.aa,type:a.ma,tagName:d.tagName};b&&(e.querySelector=vw(d));c&&(e.isVisible=!Kv(d));return e},aw=function(a,b,c){return gw({element:a.element,aa:a.aa,ma:fw.Tb},b,c)},Vv=function(a){var b=!(a==null||!a.Ud)+"."+!(a==null||!a.Vd);a&&a.nf&&a.nf.length&&(b+="."+a.nf.join("."));a&&a.Lb&&(b+="."+a.Lb.email+"."+a.Lb.phone+"."+a.Lb.address);return b},$v=function(a){if(a.length!==0){var b;b=ww(a,function(c){return!xw.test(c.aa)});b=ww(b,function(c){return c.element.tagName.toUpperCase()===
"INPUT"});b=ww(b,function(c){return!Kv(c.element)});return b[0]}},Zv=function(a,b){if(!b||b.length===0)return a;for(var c=[],d=0;d<a.length;d++){for(var e=!0,f=0;f<b.length;f++){var g=b[f];if(g&&ti(a[d].element,g)){e=!1;break}}e&&c.push(a[d])}return c},ww=function(a,b){if(a.length<=1)return a;var c=a.filter(b);return c.length===0?a:c},vw=function(a){var b;if(a===A.body)b="body";else{var c;if(a.id)c="#"+a.id;else{var d;if(a.parentElement){var e;a:{var f=a.parentElement;if(f){for(var g=0;g<f.childElementCount;g++)if(f.children[g]===
a){e=g+1;break a}e=-1}else e=1}d=vw(a.parentElement)+">:nth-child("+e.toString()+")"}else d="";c=d}b=c}return b},Yv=function(a){for(var b=[],c=0;c<a.length;c++){var d=a[c],e=d.textContent;d.tagName.toUpperCase()==="INPUT"&&d.value&&(e=d.value);if(e){var f=e.match(yw);if(f){var g=f[0],k;if(z.location){var m=hk(z.location,"host",!0);k=g.toLowerCase().indexOf(m)>=0}else k=!1;k||b.push({element:d,aa:g})}}}return b},Xv=function(){var a=[],b=A.body;if(!b)return{elements:a,status:"4"};for(var c=b.querySelectorAll("*"),
d=0;d<c.length&&d<1E4;d++){var e=c[d];if(!(zw.indexOf(e.tagName.toUpperCase())>=0)&&e.children instanceof HTMLCollection){for(var f=!1,g=0;g<e.childElementCount&&g<1E4;g++)if(!(Aw.indexOf(e.children[g].tagName.toUpperCase())>=0)){f=!0;break}(!f||I(33)&&Bw.indexOf(e.tagName)!==-1)&&a.push(e)}}return{elements:a,status:c.length>1E4?"2":"1"}},Cw=!1;var yw=/[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}/i,
Tv=/@(gmail|googlemail)\./i,xw=/support|noreply/i,zw="SCRIPT STYLE IMG SVG PATH BR NOSCRIPT TEXTAREA".split(" "),Aw=["BR"],Dw=hg('',2),fw={Tb:"1",Xc:"2",Vc:"3",Wc:"4",ee:"5",Ze:"6",qg:"7",Ph:"8",Lg:"9",Ih:"10"},Wv={},Bw=["INPUT","SELECT"],Ew=tw(/^([^\x00-\x40\x5b-\x60\x7b-\xff]|[.-]|\s)+$/);
var cx=function(a,b,c){vu(a,M.m.bf)||W(a,M.m.bf,{});vu(a,M.m.bf)[b]=c},ex=function(a,b){var c=dx(a,M.m.Ae,a.D.H[M.m.Ae]);if(c&&c[b||a.eventName]!==void 0)return c[b||a.eventName]},fx=function(a){var b=U(a,"user_data");if(Yc(b))return b},gx=function(a){if(U(a,"is_merchant_center")||!tk(a.D))return!1;if(!P(a.D,M.m.Uc)){var b=P(a.D,M.m.Nc);return b===!0||b==="true"}return!0},hx=function(a){return dx(a,M.m.Rc,P(a.D,M.m.Rc))||!!dx(a,"google_ng",!1)};var Tf;var ix=Number('')||5,jx=Number('')||50,kx=gb();
var mx=function(a,b){a&&(lx("sid",a.targetId,b),lx("cc",a.clientCount,b),lx("tl",a.totalLifeMs,b),lx("hc",a.heartbeatCount,b),lx("cl",a.clientLifeMs,b))},lx=function(a,b,c){b!=null&&c.push(a+"="+b)},nx=function(){var a=A.referrer;if(a){var b;return fk(lk(a),"host")===((b=z.location)==null?void 0:b.host)?1:2}return 0},px=function(){this.U=ox;this.N=0};px.prototype.H=function(a,b,c,d){var e=nx(),f,g=[];f=z===z.top&&e!==0&&b?(b==null?void 0:b.clientCount)>
1?e===2?1:2:e===2?0:3:4;a&&lx("si",a.Af,g);lx("m",0,g);lx("iss",f,g);lx("if",c,g);mx(b,g);d&&lx("fm",encodeURIComponent(d.substring(0,jx)),g);this.O(g);};px.prototype.C=function(a,b,c,d,e){var f=[];lx("m",1,f);lx("s",a,f);lx("po",nx(),f);b&&(lx("st",b.state,f),lx("si",b.Af,f),lx("sm",b.Kf,f));mx(c,f);lx("c",d,f);e&&lx("fm",encodeURIComponent(e.substring(0,jx)),f);this.O(f);};
px.prototype.O=function(a){a=a===void 0?[]:a;!Ck||this.N>=ix||(lx("pid",kx,a),lx("bc",++this.N,a),a.unshift("ctid="+Xf.ctid+"&t=s"),this.U("https://www.googletagmanager.com/a?"+a.join("&")))};var qx=Number('')||500,rx=Number('')||5E3,sx=Number('20')||10,tx=Number('')||5E3;function ux(a){return a.performance&&a.performance.now()||Date.now()}
var vx=function(a,b){var c;var d=function(e,f,g){g=g===void 0?{Kk:function(){},Lk:function(){},Jk:function(){},onFailure:function(){}}:g;this.km=e;this.C=f;this.N=g;this.ba=this.sa=this.heartbeatCount=this.jm=0;this.jk=!1;this.H={};this.id=String(Math.floor(Number.MAX_SAFE_INTEGER*Math.random()));this.state=0;this.Af=ux(this.C);this.Kf=ux(this.C);this.U=10};d.prototype.init=function(){this.O(1);this.jb()};d.prototype.getState=function(){return{state:this.state,
Af:Math.round(ux(this.C)-this.Af),Kf:Math.round(ux(this.C)-this.Kf)}};d.prototype.O=function(e){this.state!==e&&(this.state=e,this.Kf=ux(this.C))};d.prototype.tk=function(){return String(this.jm++)};d.prototype.jb=function(){var e=this;this.heartbeatCount++;this.Bc({type:0,clientId:this.id,requestId:this.tk(),maxDelay:this.qk()},function(f){if(f.type===0){var g;if(((g=f.failure)==null?void 0:g.failureType)!=null)if(f.stats&&(e.stats=f.stats),e.ba++,f.isDead||e.ba>sx){var k=f.isDead&&f.failure.failureType;
e.U=k||10;e.O(4);e.hm();var m,n;(n=(m=e.N).Jk)==null||n.call(m,{failureType:k||10,data:f.failure.data})}else e.O(3),e.uk();else{if(e.heartbeatCount>f.stats.heartbeatCount+sx){e.heartbeatCount=f.stats.heartbeatCount;var p,q;(q=(p=e.N).onFailure)==null||q.call(p,{failureType:13})}e.stats=f.stats;var r=e.state;e.O(2);if(r!==2)if(e.jk){var v,u;(u=(v=e.N).Lk)==null||u.call(v)}else{e.jk=!0;var t,w;(w=(t=e.N).Kk)==null||w.call(t)}e.ba=0;e.lm();e.uk()}}})};d.prototype.qk=function(){return this.state===2?
rx:qx};d.prototype.uk=function(){var e=this;this.C.setTimeout(function(){e.jb()},Math.max(0,this.qk()-(ux(this.C)-this.sa)))};d.prototype.qm=function(e,f,g){var k=this;this.Bc({type:1,clientId:this.id,requestId:this.tk(),command:e},function(m){if(m.type===1)if(m.result)f(m.result);else{var n,p,q,r={failureType:(q=(n=m.failure)==null?void 0:n.failureType)!=null?q:12,data:(p=m.failure)==null?void 0:p.data},v,u;(u=(v=k.N).onFailure)==null||u.call(v,r);g(r)}})};d.prototype.Bc=function(e,f){var g=this;
if(this.state===4)e.failure={failureType:this.U},f(e);else{var k=this.state!==2&&e.type!==0,m=e.requestId,n,p=this.C.setTimeout(function(){var r=g.H[m];r&&g.gk(r,7)},(n=e.maxDelay)!=null?n:tx),q={request:e,Wk:f,Rk:k,yn:p};this.H[m]=q;k||this.sendRequest(q)}};d.prototype.sendRequest=function(e){this.sa=ux(this.C);e.Rk=!1;this.km(e.request)};d.prototype.lm=function(){for(var e=l(Object.keys(this.H)),f=e.next();!f.done;f=e.next()){var g=this.H[f.value];g.Rk&&this.sendRequest(g)}};d.prototype.hm=function(){for(var e=
l(Object.keys(this.H)),f=e.next();!f.done;f=e.next())this.gk(this.H[f.value],this.U)};d.prototype.gk=function(e,f){this.rg(e);var g=e.request;g.failure={failureType:f};e.Wk(g)};d.prototype.rg=function(e){delete this.H[e.request.requestId];this.C.clearTimeout(e.yn)};d.prototype.bn=function(e){this.sa=ux(this.C);var f=this.H[e.requestId];if(f)this.rg(f),f.Wk(e);else{var g,k;(k=(g=this.N).onFailure)==null||k.call(g,{failureType:14})}};c=new d(a,z,b);return c};var wx;
var xx=function(){wx||(wx=new px);return wx},ox=function(a){I(106)?Tm(Vm(3),function(){wc(a)}):wc(a)},yx=function(a){var b=a.substring(0,a.indexOf("/_/service_worker"));return"&1p=1"+(b?"&path="+encodeURIComponent(b):"")},zx=function(a){var b=a,c=oj.ba;b?(b.charAt(b.length-1)!=="/"&&(b+="/"),a=b+c):a="https://www.googletagmanager.com/static/service_worker/"+c+"/";var d;try{d=new URL(a)}catch(e){return null}return d.protocol!=="https:"?null:d},Ax=function(a){var b=Bn(wn.nk);return b&&b[a]},Bx=function(a,
b,c,d,e){var f=this;this.H=d;this.U=this.O=!1;this.ba=null;this.initTime=c;this.C=15;this.N=this.Am(a);z.setTimeout(function(){f.initialize()},1E3);D(function(){f.on(a,b,e)})};h=Bx.prototype;h.delegate=function(a,b,c){this.getState()!==2?(this.H.C(this.C,{state:this.getState(),Af:this.initTime,Kf:Math.round(qb())-this.initTime},void 0,a.commandType),c({failureType:this.C})):this.N.qm(a,b,c)};h.getState=function(){return this.N.getState().state};h.on=function(a,b,c){var d=z.location.origin,e=this,
f=uc();try{var g=f.contentDocument.createElement("iframe"),k=a.pathname,m=k[k.length-1]==="/"?a.toString():a.toString()+"/",n=b?yx(k):"",p;I(128)&&(p={sandbox:"allow-same-origin allow-scripts"});uc(m+"sw_iframe.html?origin="+encodeURIComponent(d)+n+(c?"&e=1":""),void 0,p,void 0,g);var q=function(){f.contentDocument.body.appendChild(g);g.addEventListener("load",function(){e.ba=g.contentWindow;f.contentWindow.addEventListener("message",function(r){r.origin===a.origin&&e.N.bn(r.data)});e.initialize()})};
f.contentDocument.readyState==="complete"?q():f.contentWindow.addEventListener("load",function(){q()})}catch(r){f.parentElement.removeChild(f),this.C=11,this.H.H(void 0,void 0,this.C,r.toString())}};h.Am=function(a){var b=this,c=vx(function(d){var e;(e=b.ba)==null||e.postMessage(d,a.origin)},{Kk:function(){b.O=!0;b.H.H(c.getState(),c.stats)},Lk:function(){},Jk:function(d){b.O?(b.C=(d==null?void 0:d.failureType)||10,b.H.C(b.C,c.getState(),c.stats,void 0,d==null?void 0:d.data)):(b.C=(d==null?void 0:
d.failureType)||4,b.H.H(c.getState(),c.stats,b.C,d==null?void 0:d.data))},onFailure:function(d){b.C=d.failureType;b.H.C(b.C,c.getState(),c.stats,d.command,d.data)}});return c};h.initialize=function(){this.U||this.N.init();this.U=!0};function Cx(){var a=Wf(Tf.C,"",function(){return{}});try{return a("internal_sw_allowed"),!0}catch(b){return!1}}
function Dx(a,b,c){c=c===void 0?!1:c;var d=z.location.origin;if(!d||!Cx())return;Lj()&&(a=""+d+Kj()+"/_/service_worker");var e=zx(a);if(e===null||Ax(e.origin))return;if(!hc()){xx().H(void 0,void 0,6);return}var f=new Bx(e,!!a,b||Math.round(qb()),xx(),c),g;a:{var k=wn.nk,m={},n=zn(k);if(!n){n=zn(k,!0);if(!n){g=void 0;break a}n.set(m)}g=n.get()}g[e.origin]=f;}
var Ex=function(a,b,c,d){var e;if((e=Ax(a))==null||!e.delegate){var f=hc()?16:6;xx().C(f,void 0,void 0,b.commandType);d({failureType:f});return}Ax(a).delegate(b,c,d);};
function Fx(a,b,c,d,e){var f=zx();if(f===null){d(hc()?16:6);return}var g,k=(g=Ax(f.origin))==null?void 0:g.initTime,m=Math.round(qb()),n={commandType:0,params:{url:a,method:0,templates:b,body:"",processResponse:!1,sinceInit:k?m-k:void 0}};e&&(n.params.encryptionKeyString=e);Ex(f.origin,n,function(p){c(p)},function(p){d(p.failureType)});}
function Gx(a,b,c,d){var e=zx(a);if(e===null){d("_is_sw=f"+(hc()?16:6)+"te");return}var f=b?1:0,g=Math.round(qb()),k,m=(k=Ax(e.origin))==null?void 0:k.initTime,n=m?g-m:void 0;Ex(e.origin,{commandType:0,params:{url:a,method:f,templates:c,body:b||"",processResponse:!0,sinceInit:n,attributionReporting:!0}},function(){},function(p){var q="_is_sw=f"+p.failureType,r,v=(r=Ax(e.origin))==null?void 0:r.getState();v!==void 0&&(q+="s"+v);d(n?q+("t"+n):q+"te")});
};var Hx="platform platformVersion architecture model uaFullVersion bitness fullVersionList wow64".split(" ");function Ix(a){var b;return(b=a.google_tag_data)!=null?b:a.google_tag_data={}}function Jx(){var a=z.google_tag_data,b;if(a!=null&&a.uach){var c=a.uach,d=Object.assign({},c);c.fullVersionList&&(d.fullVersionList=c.fullVersionList.slice(0));b=d}else b=null;return b}function Kx(){var a,b;return(b=(a=z.google_tag_data)==null?void 0:a.uach_promise)!=null?b:null}
function Lx(a){var b,c;return typeof((b=a.navigator)==null?void 0:(c=b.userAgentData)==null?void 0:c.getHighEntropyValues)==="function"}function Mx(){var a=z;if(!Lx(a))return null;var b=Ix(a);if(b.uach_promise)return b.uach_promise;var c=a.navigator.userAgentData.getHighEntropyValues(Hx).then(function(d){b.uach!=null||(b.uach=d);return d});return b.uach_promise=c};
var Ox=function(a,b){if(a)for(var c=Nx(a),d=l(Object.keys(c)),e=d.next();!e.done;e=d.next()){var f=e.value;W(b,f,c[f])}},Nx=function(a){var b={};b[M.m.Oe]=a.architecture;b[M.m.Pe]=a.bitness;a.fullVersionList&&(b[M.m.Qe]=a.fullVersionList.map(function(c){return encodeURIComponent(c.brand||"")+";"+encodeURIComponent(c.version||"")}).join("|"));b[M.m.Re]=a.mobile?"1":"0";b[M.m.Se]=a.model;b[M.m.Te]=a.platform;b[M.m.Ue]=a.platformVersion;b[M.m.Ve]=a.wow64?"1":"0";return b},Qx=function(a){var b=Px.eo,
c=function(g,k){try{a(g,k)}catch(m){}},d=Jx();if(d)c(d);else{var e=Kx();if(e){b=Math.min(Math.max(isFinite(b)?b:0,0),1E3);var f=z.setTimeout(function(){c.Bf||(c.Bf=!0,O(106),c(null,Error("Timeout")))},b);e.then(function(g){c.Bf||(c.Bf=!0,O(104),z.clearTimeout(f),c(g))}).catch(function(g){c.Bf||(c.Bf=!0,O(105),z.clearTimeout(f),c(null,g))})}else c(null)}},Sx=function(){if(Lx(z)&&(Rx=qb(),!Kx())){var a=Mx();a&&(a.then(function(){O(95)}),a.catch(function(){O(96)}))}},Rx;function Tx(a){var b=a.location.href;if(a===a.top)return{url:b,tn:!0};var c=!1,d=a.document;d&&d.referrer&&(b=d.referrer,a.parent===a.top&&(c=!0));var e=a.location.ancestorOrigins;if(e){var f=e[e.length-1];f&&b.indexOf(f)===-1&&(c=!1,b=f)}return{url:b,tn:c}};function Iy(a){var b=!!Lj();switch(a){case 45:return b&&!I(77)?Kj()+"/g/ccm/collect":"https://www.google.com/ccm/collect";case 46:return b?Kj()+"/gs/ccm/collect":"https://pagead2.googlesyndication.com/ccm/collect";case 51:return b&&!I(80)?Kj()+"/travel/flights/click/conversion":"https://www.google.com/travel/flights/click/conversion";case 9:return!I(78)&&b?Kj()+"/pagead/viewthroughconversion":"https://googleads.g.doubleclick.net/pagead/viewthroughconversion";case 0:case 1:case 2:case 3:case 5:case 6:case 7:case 8:case 11:case 12:case 13:case 14:case 15:case 16:case 17:case 18:case 19:case 20:case 21:case 22:case 23:case 24:case 25:case 26:case 27:case 28:case 29:case 30:case 31:case 32:case 33:case 34:case 35:case 36:case 37:case 38:case 39:case 40:case 42:case 43:case 44:case 47:case 48:case 49:case 50:case 52:case 53:case 54:case 55:case 56:case 57:case 58:throw Error("Unsupported endpoint");
default:Zb(a,"Unknown endpoint")}};
var Ky=function(a,b){for(var c={},d=function(p,q){var r;r=q===!0?"1":q===!1?"0":encodeURIComponent(String(q));c[p]=r},e=l(Object.keys(a.C)),f=e.next();!f.done;f=e.next()){var g=f.value,k=vu(a,g),m=Jy[g];m&&k!==void 0&&k!==""&&(!U(a,"redact_click_ids")||g!==M.m.Kc&&g!==M.m.Mc&&g!==M.m.od&&g!==M.m.ke||(k="0"),d(m,k))}d("gtm",Tq({Da:U(a,"source_canonical_id")}));Gq()&&d("gcs",Hq());d("gcd",Lq(a.D));Oq()&&d("dma_cps",Mq());d("dma",Nq());kq(sq())&&d("tcfd",Pq());Jj()&&d("tag_exp",Jj());if(U(a,"add_tag_timing")){d("tft",
qb());var n=Kc();n!==void 0&&d("tfd",Math.round(n))}I(24)&&d("apve","1");(I(25)||I(26))&&d("apvf",Hc()?I(26)?"f":"sb":"nf");I(106)&&Nm[1]===1&&!Qm[1].isConsentGranted()&&(c.limited_ads="1");b(c)},Ly=function(a,b,c){var d=b.D;ho({targetId:b.target.destinationId,request:{url:a,parameterEncoding:2,endpoint:c},Ka:{eventId:d.eventId,priorityId:d.priorityId},wg:{eventId:U(b,"consent_event_id"),priorityId:U(b,"consent_priority_id")}})},My=function(a,b,c){var d={destinationId:b.target.destinationId,endpoint:c,
eventId:b.D.eventId,priorityId:b.D.priorityId};Ly(a,b,c);Fl(d,a,void 0,{yi:!0,method:"GET"},function(){},function(){El(d,a+"&img=1")})},Ny=function(a){var b=oc()||mc()?"www.google.com":"www.googleadservices.com",c=[];jb(a,function(d,e){d==="dl"?c.push("url="+e):d==="dr"?c.push("ref="+e):d==="uid"?c.push("userId="+e):c.push(d+"="+e)});return"https://"+b+"/pagead/set_partitioned_cookie?"+c.join("&")},Oy=function(a){Ky(a,function(b){if(U(a,"hit_type")==="page_view"){var c=[];I(28)&&a.target.destinationId&&
c.push("tid="+a.target.destinationId);jb(b,function(r,v){c.push(r+"="+v)});var d=R([M.m.R,M.m.T])?45:46,e=Iy(d)+"?"+c.join("&");Ly(e,a,d);var f=a.D,g={destinationId:a.target.destinationId,endpoint:d,eventId:f.eventId,priorityId:f.priorityId};if(I(26)&&Hc()){Fl(g,e,void 0,{yi:!0},function(){},function(){El(g,e+"&img=1")});var k=R([M.m.R,M.m.T]),m=vu(a,M.m.Sc)==="1",n=vu(a,M.m.Wg)==="1";if(k&&m&&!n){var p=Ny(b),q=oc()||mc()?58:57;My(p,a,q)}}else Dl(g,e)||El(g,e+"&img=1");if(ab(a.D.onSuccess))a.D.onSuccess()}})},
Py={},Jy=(Py[M.m.Sb]="gcu",Py[M.m.Cb]="gclgb",Py[M.m.Qa]="gclaw",Py[M.m.ie]="gad_source",Py[M.m.je]="gad_source_src",Py[M.m.Kc]="gclid",Py[M.m.tj]="gclsrc",Py[M.m.ke]="gbraid",Py[M.m.od]="wbraid",Py[M.m.Db]="auid",Py[M.m.vj]="rnd",Py[M.m.Wg]="ncl",Py[M.m.ah]="gcldc",Py[M.m.Mc]="dclid",Py[M.m.pb]="edid",Py[M.m.vc]="en",Py[M.m.wc]="gdpr",Py[M.m.qb]="gdid",Py[M.m.Qc]="_ng",Py[M.m.Ge]="_tu",Py[M.m.Jj]="gtm_up",Py[M.m.Xb]="frm",Py[M.m.Sc]="lps",Py[M.m.Ie]="did",Py[M.m.Kj]="navt",Py[M.m.oa]="dl",Py[M.m.Ga]=
"dr",Py[M.m.hb]="dt",Py[M.m.Pj]="scrsrc",Py[M.m.Ne]="ga_uid",Py[M.m.zc]="gdpr_consent",Py[M.m.Ah]="u_tz",Py[M.m.Ca]="uid",Py[M.m.Ld]="us_privacy",Py[M.m.bc]="npa",Py);var Qy={};Qy.M=Vq.M;var Ry={vo:"L",fm:"S",Co:"Y",jo:"B",ro:"E",uo:"I",Bo:"TC",so:"HTC"},Sy={fm:"S",qo:"V",mo:"E",Ao:"tag"},Ty={},Uy=(Ty[Qy.M.Rh]="6",Ty[Qy.M.Sh]="5",Ty[Qy.M.Qh]="7",Ty);function Vy(){function a(c,d){var e=Ya(d);e&&b.push([c,e])}var b=[];a("u","GTM");a("ut","TAGGING");a("h","HEALTH");return b};var Wy=!1;function lz(a){}
function mz(a){}function nz(){}
function oz(a){}function pz(a){}
function qz(a){}
function rz(){}
function sz(a,b){}
function tz(a,b,c){}
function uz(){};var vz=Object.freeze({cache:"no-store",credentials:"include",method:"GET",keepalive:!0,redirect:"follow"});
function wz(a,b,c,d,e,f,g){var k=Object.assign({},vz);c&&(k.body=c,k.method="POST");Object.assign(k,e);z.fetch(b,k).then(function(m){if(!m.ok)g==null||g();else if(m.body){var n=m.body.getReader(),p=new TextDecoder;return new Promise(function(q){function r(){n.read().then(function(v){var u;u=v.done;var t=p.decode(v.value,{stream:!u});xz(d,t);u?(f==null||f(),q()):r()}).catch(function(){q()})}r()})}}).catch(function(){g?g():I(123)&&(b+="&_z=retryFetch",c?Dl(a,b,c):Cl(a,b))})};var yz=function(a){this.O=a;this.C=""},zz=function(a,b){a.H=b;return a},Az=function(a,b){a.N=b;return a},xz=function(a,b){b=a.C+b;for(var c=b.indexOf("\n\n");c!==-1;){var d=a,e;a:{var f=l(b.substring(0,c).split("\n")),g=f.next().value,k=f.next().value;if(g.indexOf("event: message")===0&&k.indexOf("data: ")===0)try{e=JSON.parse(k.substring(k.indexOf(":")+1));break a}catch(m){}e=void 0}Bz(d,e);b=b.substring(c+2);c=b.indexOf("\n\n")}a.C=b},Cz=function(a,b){return function(){if(b.fallback_url&&b.fallback_url_method){var c=
{};Bz(a,(c[b.fallback_url_method]=[b.fallback_url],c.options={},c))}}},Bz=function(a,b){b&&(Dz(b.send_pixel,b.options,a.O),Dz(b.create_iframe,b.options,a.H),Dz(b.fetch,b.options,a.N))};function Ez(a){var b=a.search;return a.protocol+"//"+a.hostname+a.pathname+(b?b+"&richsstsse":"?richsstsse")}function Dz(a,b,c){if(a&&c){var d=a||[];if(Array.isArray(d))for(var e=Yc(b)?b:{},f=l(d),g=f.next();!g.done;g=f.next())c(g.value,e)}};function mA(a,b){if(data.entities){var c=data.entities[a];if(c)return c[b]}};function nA(a,b,c){c=c===void 0?!1:c;oA().addRestriction(0,a,b,c)}function pA(a,b,c){c=c===void 0?!1:c;oA().addRestriction(1,a,b,c)}function qA(){var a=dm();return oA().getRestrictions(1,a)}var rA=function(){this.container={};this.C={}},sA=function(a,b){var c=a.container[b];c||(c={_entity:{internal:[],external:[]},_event:{internal:[],external:[]}},a.container[b]=c);return c};
rA.prototype.addRestriction=function(a,b,c,d){d=d===void 0?!1:d;if(!d||!this.C[b]){var e=sA(this,b);a===0?d?e._entity.external.push(c):e._entity.internal.push(c):a===1&&(d?e._event.external.push(c):e._event.internal.push(c))}};
rA.prototype.getRestrictions=function(a,b){var c=sA(this,b);if(a===0){var d,e;return[].concat(ta((c==null?void 0:(d=c._entity)==null?void 0:d.internal)||[]),ta((c==null?void 0:(e=c._entity)==null?void 0:e.external)||[]))}if(a===1){var f,g;return[].concat(ta((c==null?void 0:(f=c._event)==null?void 0:f.internal)||[]),ta((c==null?void 0:(g=c._event)==null?void 0:g.external)||[]))}return[]};
rA.prototype.getExternalRestrictions=function(a,b){var c=sA(this,b),d,e;return a===0?(c==null?void 0:(d=c._entity)==null?void 0:d.external)||[]:(c==null?void 0:(e=c._event)==null?void 0:e.external)||[]};rA.prototype.removeExternalRestrictions=function(a){var b=sA(this,a);b._event&&(b._event.external=[]);b._entity&&(b._entity.external=[]);this.C[a]=!0};function oA(){return Fo("r",function(){return new rA})};var tA=new RegExp(/^(.*\.)?(google|youtube|blogger|withgoogle)(\.com?)?(\.[a-z]{2})?\.?$/),uA={cl:["ecl"],customPixels:["nonGooglePixels"],ecl:["cl"],ehl:["hl"],gaawc:["googtag"],hl:["ehl"],html:["customScripts","customPixels","nonGooglePixels","nonGoogleScripts","nonGoogleIframes"],customScripts:["html","customPixels","nonGooglePixels","nonGoogleScripts","nonGoogleIframes"],nonGooglePixels:[],nonGoogleScripts:["nonGooglePixels"],nonGoogleIframes:["nonGooglePixels"]},vA={cl:["ecl"],customPixels:["customScripts",
"html"],ecl:["cl"],ehl:["hl"],gaawc:["googtag"],hl:["ehl"],html:["customScripts"],customScripts:["html"],nonGooglePixels:["customPixels","customScripts","html","nonGoogleScripts","nonGoogleIframes"],nonGoogleScripts:["customScripts","html"],nonGoogleIframes:["customScripts","html","nonGoogleScripts"]},wA="google customPixels customScripts html nonGooglePixels nonGoogleScripts nonGoogleIframes".split(" ");
function xA(){var a=Rj("gtm.allowlist")||Rj("gtm.whitelist");a&&O(9);xj&&(a=["google","gtagfl","lcl","zone"],I(47)&&a.push("cmpPartners"));tA.test(z.location&&z.location.hostname)&&(xj?O(116):(O(117),yA&&(a=[],window.console&&window.console.log&&window.console.log("GTM blocked. See go/13687728."))));var b=a&&ub(nb(a),uA),c=Rj("gtm.blocklist")||Rj("gtm.blacklist");c||(c=Rj("tagTypeBlacklist"))&&O(3);c?O(8):c=[];tA.test(z.location&&z.location.hostname)&&(c=nb(c),c.push("nonGooglePixels","nonGoogleScripts",
"sandboxedScripts"));nb(c).indexOf("google")>=0&&O(2);var d=c&&ub(nb(c),vA),e={};return function(f){var g=f&&f[Te.xa];if(!g||typeof g!=="string")return!0;g=g.replace(/^_*/,"");if(e[g]!==void 0)return e[g];var k=Hj[g]||[],m=!0;if(a){var n;if(n=m)a:{if(b.indexOf(g)<0){if(I(47)&&xj&&k.indexOf("cmpPartners")>=0){n=!0;break a}if(k&&k.length>0)for(var p=0;p<k.length;p++){if(b.indexOf(k[p])<0){O(11);n=!1;break a}}else{n=!1;break a}}n=!0}m=n}var q=!1;if(c){var r=d.indexOf(g)>=0;if(r)q=r;else{var v=hb(d,k||
[]);v&&O(10);q=v}}var u=!m||q;!u&&(k.indexOf("sandboxedScripts")===-1?0:I(47)&&xj&&k.indexOf("cmpPartners")>=0?!zA():b&&b.indexOf("sandboxedScripts")!==-1?0:hb(d,wA))&&(u=!0);return e[g]=u}}function zA(){var a=Wf(Tf.C,bm(),function(){return{}});try{return a("inject_cmp_banner"),!0}catch(b){return!1}}var yA=!1;yA=!0;
function AA(){Tl&&nA(dm(),function(a){var b=Ef(a.entityId),c;if(Hf(b)){var d=b[Te.xa];if(!d)throw Error("Error: No function name given for function call.");var e=vf[d];c=!!e&&!!e.runInSiloedMode}else c=!!mA(b[Te.xa],4);return c})};function BA(a,b,c,d,e){if(!CA()){var f=d.siloed?Zl(a):a;if(!mm(f)){d.loadExperiments=pj();om(f,d,e);var g=DA(a),k=function(){Pl().container[f]&&(Pl().container[f].state=3);EA()},m={destinationId:f,endpoint:0};if(Lj())Gl(m,Kj()+"/"+g,void 0,k);else{var n=vb(a,"GTM-"),p=sk(),q=c?"/gtag/js":"/gtm.js",r=rk(b,q+g);if(!r){var v=rj.Qf+q;p&&jc&&n&&(v=jc.replace(/^(?:https?:\/\/)?/i,"").split(/[?#]/)[0]);r=Zu("https://","http://",v+g)}Gl(m,r,void 0,k)}}}}
function EA(){qm()||jb(rm(),function(a,b){FA(a,b.transportUrl,b.context);O(92)})}
function FA(a,b,c,d){if(!CA()){var e=c.siloed?Zl(a):a;if(!nm(e))if(c.loadExperiments||(c.loadExperiments=pj()),qm())Pl().destination[e]={state:0,transportUrl:b,context:c,parent:gm()},Ol({ctid:e,isDestination:!0},d),O(91);else{c.siloed&&pm({ctid:e,isDestination:!0});Pl().destination[e]={state:1,context:c,parent:gm()};Ol({ctid:e,isDestination:!0},d);var f={destinationId:e,endpoint:0};if(Lj())Gl(f,Kj()+("/gtd"+DA(a,!0)));else{var g="/gtag/destination"+DA(a,!0),k=rk(b,g);k||(k=Zu("https://","http://",
rj.Qf+g));Gl(f,k)}}}}function DA(a,b){b=b===void 0?!1:b;var c="?id="+encodeURIComponent(a);I(119)&&rj.zb==="dataLayer"||(c+="&l="+rj.zb);if(!vb(a,"GTM-")||b)c=I(125)?c+(Lj()?"&sc=1":"&cx=c"):c+"&cx=c";c+="&gtm="+Tq();sk()&&(c+="&sign="+rj.Lh);var d=oj.H;d===1?c+="&fps=fc":d===2&&(c+="&fps=fe");I(70)&&Jj()&&(c+="&tag_exp="+Jj());return c}function CA(){if(Rq()){return!0}return!1};var GA=function(){this.H=0;this.C={}};GA.prototype.addListener=function(a,b,c){var d=++this.H;this.C[a]=this.C[a]||{};this.C[a][String(d)]={listener:b,Rb:c};return d};GA.prototype.removeListener=function(a,b){var c=this.C[a],d=String(b);if(!c||!c[d])return!1;delete c[d];return!0};var IA=function(a,b){var c=[];jb(HA.C[a],function(d,e){c.indexOf(e.listener)<0&&(e.Rb===void 0||b.indexOf(e.Rb)>=0)&&c.push(e.listener)});return c};function JA(a,b,c){return{entityType:a,indexInOriginContainer:b,nameInOriginContainer:c,originContainerId:bm()}};var LA=function(a,b){this.C=!1;this.O=[];this.eventData={tags:[]};this.U=!1;this.H=this.N=0;KA(this,a,b)},MA=function(a,b,c,d){if(tj.hasOwnProperty(b)||b==="__zone")return-1;var e={};Yc(d)&&(e=Zc(d,e));e.id=c;e.status="timeout";return a.eventData.tags.push(e)-1},NA=function(a,b,c,d){var e=a.eventData.tags[b];e&&(e.status=c,e.executionTime=d)},OA=function(a){if(!a.C){for(var b=a.O,c=0;c<b.length;c++)b[c]();a.C=!0;a.O.length=0}},KA=function(a,b,c){b!==void 0&&a.df(b);c&&z.setTimeout(function(){OA(a)},
Number(c))};LA.prototype.df=function(a){var b=this,c=sb(function(){D(function(){a(bm(),b.eventData)})});this.C?c():this.O.push(c)};var PA=function(a){a.N++;return sb(function(){a.H++;a.U&&a.H>=a.N&&OA(a)})},QA=function(a){a.U=!0;a.H>=a.N&&OA(a)};var RA={};function SA(){return z[TA()]}
function TA(){return z.GoogleAnalyticsObject||"ga"}function WA(){var a=bm();}
function XA(a,b){return function(){var c=SA(),d=c&&c.getByName&&c.getByName(a);if(d){var e=d.get("sendHitTask");d.set("sendHitTask",function(f){var g=f.get("hitPayload"),k=f.get("hitCallback"),m=g.indexOf("&tid="+b)<0;m&&(f.set("hitPayload",g.replace(/&tid=UA-[0-9]+-[0-9]+/,"&tid="+b),!0),f.set("hitCallback",void 0,!0));e(f);m&&(f.set("hitPayload",g,!0),f.set("hitCallback",k,!0),f.set("_x_19",void 0,!0),e(f))})}}};var cB=["es","1"],dB={},eB={};function fB(a,b){if(Ck){var c;c=b.match(/^(gtm|gtag)\./)?encodeURIComponent(b):"*";dB[a]=[["e",c],["eid",a]];Lp(a)}}function gB(a){var b=a.eventId,c=a.kd;if(!dB[b])return[];var d=[];eB[b]||d.push(cB);d.push.apply(d,ta(dB[b]));c&&(eB[b]=!0);return d};var hB={},iB={},jB={};function kB(a,b,c,d){Ck&&I(114)&&((d===void 0?0:d)?(jB[b]=jB[b]||0,++jB[b]):c!==void 0?(iB[a]=iB[a]||{},iB[a][b]=Math.round(c)):(hB[a]=hB[a]||{},hB[a][b]=(hB[a][b]||0)+1))}function lB(a){var b=a.eventId,c=a.kd,d=hB[b]||{},e=[],f;for(f in d)d.hasOwnProperty(f)&&e.push(""+f+d[f]);c&&delete hB[b];return e.length?[["md",e.join(".")]]:[]}
function mB(a){var b=a.eventId,c=a.kd,d=iB[b]||{},e=[],f;for(f in d)d.hasOwnProperty(f)&&e.push(""+f+d[f]);c&&delete iB[b];return e.length?[["mtd",e.join(".")]]:[]}function nB(){for(var a=[],b=l(Object.keys(jB)),c=b.next();!c.done;c=b.next()){var d=c.value;a.push(""+d+jB[d])}return a.length?[["mec",a.join(".")]]:[]};var oB={},pB={};function qB(a,b,c){if(Ck&&b){var d=wk(b);oB[a]=oB[a]||[];oB[a].push(c+d);var e=(Hf(b)?"1":"2")+d;pB[a]=pB[a]||[];pB[a].push(e);Lp(a)}}function rB(a){var b=a.eventId,c=a.kd,d=[],e=oB[b]||[];e.length&&d.push(["tr",e.join(".")]);var f=pB[b]||[];f.length&&d.push(["ti",f.join(".")]);c&&(delete oB[b],delete pB[b]);return d};function sB(a,b,c,d){var e=tf[a],f=tB(a,b,c,d);if(!f)return null;var g=If(e[Te.pk],c,[]);if(g&&g.length){var k=g[0];f=sB(k.index,{onSuccess:f,onFailure:k.Dk===1?b.terminate:f,terminate:b.terminate},c,d)}return f}
function tB(a,b,c,d){function e(){function w(){vn(3);var N=qb()-H;qB(c.id,f,"7");NA(c.Cc,C,"exception",N);I(103)&&tz(c,f,Qy.M.Qh);E||(E=!0,k())}if(f[Te.Xl])k();else{var x=Gf(f,c,[]),y=x[Te.ql];if(y!=null)for(var B=0;B<y.length;B++)if(!R(y[B])){k();return}var C=MA(c.Cc,String(f[Te.xa]),Number(f[Te.sg]),x[Te.METADATA]),E=!1;x.vtp_gtmOnSuccess=function(){if(!E){E=!0;var N=qb()-H;qB(c.id,tf[a],"5");NA(c.Cc,C,"success",N);I(103)&&tz(c,f,Qy.M.Sh);g()}};x.vtp_gtmOnFailure=function(){if(!E){E=!0;var N=qb()-
H;qB(c.id,tf[a],"6");NA(c.Cc,C,"failure",N);I(103)&&tz(c,f,Qy.M.Rh);k()}};x.vtp_gtmTagId=f.tag_id;x.vtp_gtmEventId=c.id;c.priorityId&&(x.vtp_gtmPriorityId=c.priorityId);qB(c.id,f,"1");I(103)&&sz(c,f);var H=qb();try{Jf(x,{event:c,index:a,type:1})}catch(N){w(N)}I(103)&&tz(c,f,Qy.M.rk)}}var f=tf[a],g=b.onSuccess,k=b.onFailure,m=b.terminate;if(c.isBlocked(f))return null;var n=If(f[Te.sk],c,[]);if(n&&n.length){var p=n[0],q=sB(p.index,{onSuccess:g,onFailure:k,terminate:m},c,d);if(!q)return null;g=q;k=p.Dk===
2?m:q}if(f[Te.fk]||f[Te.Zl]){var r=f[Te.fk]?uf:c.Wn,v=g,u=k;if(!r[a]){var t=uB(a,r,sb(e));g=t.onSuccess;k=t.onFailure}return function(){r[a](v,u)}}return e}function uB(a,b,c){var d=[],e=[];b[a]=vB(d,e,c);return{onSuccess:function(){b[a]=wB;for(var f=0;f<d.length;f++)d[f]()},onFailure:function(){b[a]=xB;for(var f=0;f<e.length;f++)e[f]()}}}function vB(a,b,c){return function(d,e){a.push(d);b.push(e);c()}}function wB(a){a()}function xB(a,b){b()};var AB=function(a,b){for(var c=[],d=0;d<tf.length;d++)if(a[d]){var e=tf[d];var f=PA(b.Cc);try{var g=sB(d,{onSuccess:f,onFailure:f,terminate:f},b,d);if(g){var k=e[Te.xa];if(!k)throw Error("Error: No function name given for function call.");var m=vf[k];c.push({bl:d,priorityOverride:(m?m.priorityOverride||0:0)||mA(e[Te.xa],1)||0,execute:g})}else yB(d,b),f()}catch(p){f()}}c.sort(zB);for(var n=0;n<c.length;n++)c[n].execute();
return c.length>0};function BB(a,b){if(!HA)return!1;var c=a["gtm.triggers"]&&String(a["gtm.triggers"]),d=IA(a.event,c?String(c).split(","):[]);if(!d.length)return!1;for(var e=0;e<d.length;++e){var f=PA(b);try{d[e](a,f)}catch(g){f()}}return!0}function zB(a,b){var c,d=b.priorityOverride,e=a.priorityOverride;c=d>e?1:d<e?-1:0;var f;if(c!==0)f=c;else{var g=a.bl,k=b.bl;f=g>k?1:g<k?-1:0}return f}
function yB(a,b){if(Ck){var c=function(d){var e=b.isBlocked(tf[d])?"3":"4",f=If(tf[d][Te.pk],b,[]);f&&f.length&&c(f[0].index);qB(b.id,tf[d],e);var g=If(tf[d][Te.sk],b,[]);g&&g.length&&c(g[0].index)};c(a)}}var CB=!1,HA;function DB(){HA||(HA=new GA);return HA}
function EB(a){var b=a["gtm.uniqueEventId"],c=a["gtm.priorityId"],d=a.event;if(I(103)){}if(d==="gtm.js"){if(CB)return!1;CB=!0}var e=!1,f=qA(),g=Zc(a,null);if(!f.every(function(v){return v({originalEventData:g})})){if(d!=="gtm.js"&&d!=="gtm.init"&&d!=="gtm.init_consent")return!1;e=!0}fB(b,d);var k=a.eventCallback,m=
a.eventTimeout,n={id:b,priorityId:c,name:d,isBlocked:FB(g,e),Wn:[],logMacroError:function(){O(6);vn(0)},cachedModelValues:GB(),Cc:new LA(function(){if(I(103)){}k&&k.apply(k,Array.prototype.slice.call(arguments,0))},
m),originalEventData:g};I(114)&&Ck&&(n.reportMacroDiscrepancy=kB);I(103)&&pz(n.id);var p=Of(n);I(103)&&qz(n.id);e&&(p=HB(p));I(103)&&oz(b);var q=AB(p,n),r=BB(a,n.Cc);QA(n.Cc);d!=="gtm.js"&&d!=="gtm.sync"||WA();return IB(p,q)||r}function GB(){var a={};a.event=Wj("event",1);a.ecommerce=Wj("ecommerce",1);a.gtm=Wj("gtm");a.eventModel=Wj("eventModel");return a}
function FB(a,b){var c=xA();return function(d){if(c(d))return!0;var e=d&&d[Te.xa];if(!e||typeof e!=="string")return!0;e=e.replace(/^_*/,"");var f,g=dm();f=oA().getRestrictions(0,g);var k=a;b&&(k=Zc(a,null),k["gtm.uniqueEventId"]=Number.MAX_SAFE_INTEGER);for(var m=Hj[e]||[],n=l(f),p=n.next();!p.done;p=n.next()){var q=p.value;try{if(!q({entityId:e,securityGroups:m,originalEventData:k}))return!0}catch(r){return!0}}return!1}}
function HB(a){for(var b=[],c=0;c<a.length;c++)if(a[c]){var d=String(tf[c][Te.xa]);if(sj[d]||tf[c][Te.am]!==void 0||mA(d,2))b[c]=!0}return b}function IB(a,b){if(!b)return b;for(var c=0;c<a.length;c++)if(a[c]&&tf[c]&&!tj[String(tf[c][Te.xa])])return!0;return!1};function JB(){DB().addListener("gtm.init",function(a,b){oj.U=!0;fn();b()})};var KB=!1,LB=0,MB=[];function NB(a){if(!KB){var b=A.createEventObject,c=A.readyState==="complete",d=A.readyState==="interactive";if(!a||a.type!=="readystatechange"||c||!b&&d){KB=!0;for(var e=0;e<MB.length;e++)D(MB[e])}MB.push=function(){for(var f=xa.apply(0,arguments),g=0;g<f.length;g++)D(f[g]);return 0}}}function OB(){if(!KB&&LB<140){LB++;try{var a,b;(b=(a=A.documentElement).doScroll)==null||b.call(a,"left");NB()}catch(c){z.setTimeout(OB,50)}}}
function PB(){KB=!1;LB=0;if(A.readyState==="interactive"&&!A.createEventObject||A.readyState==="complete")NB();else{xc(A,"DOMContentLoaded",NB);xc(A,"readystatechange",NB);if(A.createEventObject&&A.documentElement.doScroll){var a=!0;try{a=!z.frameElement}catch(b){}a&&OB()}xc(z,"load",NB)}}function QB(a){KB?a():MB.push(a)};var RB=0;var SB={},TB={};function UB(a,b){for(var c=[],d=[],e={},f=0;f<a.length;e={Fi:void 0,li:void 0},f++){var g=a[f];if(g.indexOf("-")>=0){if(e.Fi=Oo(g,b),e.Fi){var k=Ul?Ul:am();fb(k,function(r){return function(v){return r.Fi.destinationId===v}}(e))?c.push(g):d.push(g)}}else{var m=SB[g]||[];e.li={};m.forEach(function(r){return function(v){r.li[v]=!0}}(e));for(var n=Xl(),p=0;p<n.length;p++)if(e.li[n[p]]){c=c.concat($l());break}var q=TB[g]||[];q.length&&(c=c.concat(q))}}return{xn:c,An:d}}
function VB(a){jb(SB,function(b,c){var d=c.indexOf(a);d>=0&&c.splice(d,1)})}function WB(a){jb(TB,function(b,c){var d=c.indexOf(a);d>=0&&c.splice(d,1)})};var XB=!1,YB=!1;function ZB(a,b){var c={},d=(c.event=a,c);b&&(d.eventModel=Zc(b,null),b[M.m.Ed]&&(d.eventCallback=b[M.m.Ed]),b[M.m.Be]&&(d.eventTimeout=b[M.m.Be]));return d}function $B(a,b){a.hasOwnProperty("gtm.uniqueEventId")||Object.defineProperty(a,"gtm.uniqueEventId",{value:Jo()});b.eventId=a["gtm.uniqueEventId"];b.priorityId=a["gtm.priorityId"];return{eventId:b.eventId,priorityId:b.priorityId}}
function aC(a,b){var c=a&&a[M.m.yc];c===void 0&&(c=Rj(M.m.yc,2),c===void 0&&(c="default"));if(bb(c)||Array.isArray(c)){var d;d=b.isGtmEvent?bb(c)?[c]:c:c.toString().replace(/\s+/g,"").split(",");var e=UB(d,b.isGtmEvent),f=e.xn,g=e.An;if(g.length)for(var k=bC(a),m=0;m<g.length;m++){var n=Oo(g[m],b.isGtmEvent);if(n){var p=n.destinationId,q;if(!(q=vb(p,"siloed_"))){var r=n.destinationId,v=Pl().destination[r];q=!!v&&v.state===0}q||FA(p,k,{source:3,fromContainerExecution:b.fromContainerExecution})}}return Po(f,
b.isGtmEvent)}}var cC=void 0,dC=void 0;function eC(a,b,c){var d=Zc(a,null);d.eventId=void 0;d.inheritParentConfig=void 0;Object.keys(b).some(function(f){return b[f]!==void 0})&&O(136);var e=Zc(b,null);Zc(c,e);Bv(xv(Xl()[0],e),a.eventId,d)}function bC(a){for(var b=l([M.m.Uc,M.m.Jb]),c=b.next();!c.done;c=b.next()){var d=c.value,e=a&&a[d]||Tp.C[d];if(e)return e}}
var fC={config:function(a,b){var c=$B(a,b);if(!(a.length<2)&&bb(a[1])){var d={};if(a.length>2){if(a[2]!==void 0&&!Yc(a[2])||a.length>3)return;d=a[2]}var e=Oo(a[1],b.isGtmEvent);if(e){var f,g,k;a:{if(!Sl.Xe){var m=fm(gm());if(sm(m)){var n=m.parent,p=n.isDestination;k={En:fm(n),wn:p};break a}}k=void 0}var q=k;q&&(f=q.En,g=q.wn);fB(c.eventId,"gtag.config");var r=e.destinationId,v=e.id!==r;if(v?$l().indexOf(r)===-1:Xl().indexOf(r)===-1){if(!b.inheritParentConfig&&!d[M.m.Yb]){var u=bC(d);if(v)FA(r,u,{source:2,
fromContainerExecution:b.fromContainerExecution});else if(f!==void 0&&f.containers.indexOf(r)!==-1){var t=d;cC?eC(b,t,cC):dC||(dC=Zc(t,null))}else BA(r,u,!0,{source:2,fromContainerExecution:b.fromContainerExecution})}}else{if(f&&(O(128),g&&O(130),b.inheritParentConfig)){var w;var x=d;dC?(eC(b,dC,x),w=!1):(!x[M.m.Ac]&&vj&&cC||(cC=Zc(x,null)),w=!0);w&&f.containers&&f.containers.join(",");return}Dk&&(RB===1&&(Zm.mcc=!1),RB=2);if(vj&&!v&&!d[M.m.Ac]){var y=YB;YB=!0;if(y)return}XB||O(43);if(!b.noTargetGroup)if(v){WB(e.id);
var B=e.id,C=d[M.m.Fe]||"default";C=String(C).split(",");for(var E=0;E<C.length;E++){var H=TB[C[E]]||[];TB[C[E]]=H;H.indexOf(B)<0&&H.push(B)}}else{VB(e.id);var N=e.id,L=d[M.m.Fe]||"default";L=L.toString().split(",");for(var T=0;T<L.length;T++){var F=SB[L[T]]||[];SB[L[T]]=F;F.indexOf(N)<0&&F.push(N)}}delete d[M.m.Fe];var S=b.eventMetadata||{};S.hasOwnProperty("is_external_event")||(S.is_external_event=!b.fromContainerExecution);b.eventMetadata=S;delete d[M.m.Ed];for(var ba=v?[e.id]:$l(),ha=0;ha<ba.length;ha++){var Y=
d,Q=ba[ha],ia=Zc(b,null),la=Oo(Q,ia.isGtmEvent);la&&Tp.push("config",[Y],la,ia)}}}}},consent:function(a,b){if(a.length===3){O(39);var c=$B(a,b),d=a[1],e;if(I(134)){var f={},g=$u(a[2]),k;for(k in g)if(g.hasOwnProperty(k)){var m=g[k];f[k]=k===M.m.de?Array.isArray(m)?NaN:Number(m):k===M.m.yb?(Array.isArray(m)?m:[m]).map(av):bv(m)}e=f}else e=a[2];var n=e;b.fromContainerExecution||(n[M.m.T]&&O(139),n[M.m.za]&&O(140));d==="default"?ro(n):d==="update"?to(n,c):d==="declare"&&b.fromContainerExecution&&qo(n)}},
event:function(a,b){var c=a[1];if(!(a.length<2)&&bb(c)){var d=void 0;if(a.length>2){if(!Yc(a[2])&&a[2]!==void 0||a.length>3)return;d=a[2]}var e=ZB(c,d),f=$B(a,b),g=f.eventId,k=f.priorityId;e["gtm.uniqueEventId"]=g;k&&(e["gtm.priorityId"]=k);if(c==="optimize.callback")return e.eventModel=e.eventModel||{},e;var m=aC(d,b);if(m){fB(g,c);var n=m.map(function(E){return E.id}),p=m.map(function(E){return E.destinationId}),q=n;if(!Tl&&I(102)){q=n.slice();for(var r=l(Ul?Ul:am()),v=r.next();!v.done;v=r.next()){var u=
v.value;!vb(u,"siloed_")&&p.indexOf(u)<0&&q.push(u)}}for(var t=l(q),w=t.next();!w.done;w=t.next()){var x=w.value,y=Zc(b,null),B=Zc(d,null);delete B[M.m.Ed];var C=y.eventMetadata||{};C.hasOwnProperty("is_external_event")||(C.is_external_event=!y.fromContainerExecution);C.send_to_targets=n.slice();C.send_to_destinations=p.slice();y.eventMetadata=C;Up(c,B,x,y);Dk&&C.source_canonical_id===void 0&&RB===0&&(bn("mcc","1"),RB=1)}e.eventModel=e.eventModel||{};n.length>0?e.eventModel[M.m.yc]=n.join(","):delete e.eventModel[M.m.yc];
XB||O(43);b.noGtmEvent===void 0&&b.eventMetadata&&b.eventMetadata.syn_or_mod&&(b.noGtmEvent=!0);e.eventModel[M.m.xc]&&(b.noGtmEvent=!0);return b.noGtmEvent?void 0:e}}},get:function(a,b){O(53);if(a.length===4&&bb(a[1])&&bb(a[2])&&ab(a[3])){var c=Oo(a[1],b.isGtmEvent),d=String(a[2]),e=a[3];if(c){XB||O(43);var f=bC();if(fb($l(),function(k){return c.destinationId===k})){$B(a,b);var g={};Zc((g[M.m.Gb]=d,g[M.m.Wb]=e,g),null);Vp(d,function(k){D(function(){e(k)})},c.id,b)}else FA(c.destinationId,f,{source:4,
fromContainerExecution:b.fromContainerExecution})}}},js:function(a,b){if(a.length===2&&a[1].getTime){XB=!0;var c=$B(a,b),d=c.eventId,e=c.priorityId,f={};return f.event="gtm.js",f["gtm.start"]=a[1].getTime(),f["gtm.uniqueEventId"]=d,f["gtm.priorityId"]=e,f}},policy:function(a){if(a.length===3&&bb(a[1])&&ab(a[2])){if(Uf(a[1],a[2]),O(74),a[1]==="all"){O(75);var b=!1;try{b=a[2](bm(),"unknown",{})}catch(c){}b||O(76)}}else O(73)},set:function(a,b){var c=void 0;a.length===2&&Yc(a[1])?c=Zc(a[1],null):a.length===
3&&bb(a[1])&&(c={},Yc(a[2])||Array.isArray(a[2])?c[a[1]]=Zc(a[2],null):c[a[1]]=a[2]);if(c){var d=$B(a,b),e=d.eventId,f=d.priorityId;Zc(c,null);var g=Zc(c,null);Tp.push("set",[g],void 0,b);c["gtm.uniqueEventId"]=e;f&&(c["gtm.priorityId"]=f);delete c.event;b.overwriteModelFields=!0;return c}}},gC={policy:!0};var iC=function(a){if(hC(a))return a;this.value=a};iC.prototype.getUntrustedMessageValue=function(){return this.value};var hC=function(a){return!a||Wc(a)!=="object"||Yc(a)?!1:"getUntrustedMessageValue"in a};iC.prototype.getUntrustedMessageValue=iC.prototype.getUntrustedMessageValue;var jC=!1,kC=[];function lC(){if(!jC){jC=!0;for(var a=0;a<kC.length;a++)D(kC[a])}}function mC(a){jC?D(a):kC.push(a)};var nC=0,oC={},pC=[],qC=[],rC=!1,sC=!1;function tC(a,b){return a.messageContext.eventId-b.messageContext.eventId||a.messageContext.priorityId-b.messageContext.priorityId}function uC(a,b,c){a.eventCallback=b;c&&(a.eventTimeout=c);return vC(a)}function wC(a,b){if(!db(b)||b<0)b=0;var c=Eo[rj.zb],d=0,e=!1,f=void 0;f=z.setTimeout(function(){e||(e=!0,a());f=void 0},b);return function(){var g=c?c.subscribers:1;++d===g&&(f&&(z.clearTimeout(f),f=void 0),e||(a(),e=!0))}}
function xC(a,b){var c=a._clear||b.overwriteModelFields;jb(a,function(e,f){e!=="_clear"&&(c&&Uj(e),Uj(e,f))});Ej||(Ej=a["gtm.start"]);var d=a["gtm.uniqueEventId"];if(!a.event)return!1;typeof d!=="number"&&(d=Jo(),a["gtm.uniqueEventId"]=d,Uj("gtm.uniqueEventId",d));return EB(a)}function yC(a){if(a==null||typeof a!=="object")return!1;if(a.event)return!0;if(kb(a)){var b=a[0];if(b==="config"||b==="event"||b==="js"||b==="get")return!0}return!1}
function zC(){var a;if(qC.length)a=qC.shift();else if(pC.length)a=pC.shift();else return;var b;var c=a;if(rC||!yC(c.message))b=c;else{rC=!0;var d=c.message["gtm.uniqueEventId"],e,f;typeof d==="number"?(e=d-2,f=d-1):(e=Jo(),f=Jo(),c.message["gtm.uniqueEventId"]=Jo());var g={},k={message:(g.event="gtm.init_consent",g["gtm.uniqueEventId"]=e,g),messageContext:{eventId:e}},m={},n={message:(m.event="gtm.init",m["gtm.uniqueEventId"]=f,m),messageContext:{eventId:f}};pC.unshift(n,c);b=k}return b}
function AC(){for(var a=!1,b;!sC&&(b=zC());){sC=!0;delete Oj.eventModel;Qj();var c=b,d=c.message,e=c.messageContext;if(d==null)sC=!1;else{e.fromContainerExecution&&Vj();try{if(ab(d))try{d.call(Sj)}catch(u){}else if(Array.isArray(d)){if(bb(d[0])){var f=d[0].split("."),g=f.pop(),k=d.slice(1),m=Rj(f.join("."),2);if(m!=null)try{m[g].apply(m,k)}catch(u){}}}else{var n=void 0;if(kb(d))a:{if(d.length&&bb(d[0])){var p=fC[d[0]];if(p&&(!e.fromContainerExecution||!gC[d[0]])){n=p(d,e);break a}}n=void 0}else n=
d;n&&(a=xC(n,e)||a)}}finally{e.fromContainerExecution&&Qj(!0);var q=d["gtm.uniqueEventId"];if(typeof q==="number"){for(var r=oC[String(q)]||[],v=0;v<r.length;v++)qC.push(BC(r[v]));r.length&&qC.sort(tC);delete oC[String(q)];q>nC&&(nC=q)}sC=!1}}}return!a}
function CC(){if(I(103)){var a=!oj.N;}var c=AC();if(I(103)){}try{var e=bm(),f=z[rj.zb].hide;if(f&&f[e]!==void 0&&f.end){f[e]=
!1;var g=!0,k;for(k in f)if(f.hasOwnProperty(k)&&f[k]===!0){g=!1;break}g&&(f.end(),f.end=null)}}catch(m){}return c}function Ev(a){if(nC<a.notBeforeEventId){var b=String(a.notBeforeEventId);oC[b]=oC[b]||[];oC[b].push(a)}else qC.push(BC(a)),qC.sort(tC),D(function(){sC||AC()})}function BC(a){return{message:a.message,messageContext:a.messageContext}}
function DC(){function a(f){var g={};if(hC(f)){var k=f;f=hC(k)?k.getUntrustedMessageValue():void 0;g.fromContainerExecution=!0}return{message:f,messageContext:g}}var b=kc(rj.zb,[]),c=Io();c.pruned===!0&&O(83);oC=Cv().get();Dv();QB(function(){if(!c.gtmDom){c.gtmDom=!0;var f={};b.push((f.event="gtm.dom",f))}});mC(function(){if(!c.gtmLoad){c.gtmLoad=!0;var f={};b.push((f.event="gtm.load",f))}});c.subscribers=(c.subscribers||0)+1;var d=b.push;b.push=function(){var f;if(Eo.SANDBOXED_JS_SEMAPHORE>0){f=
[];for(var g=0;g<arguments.length;g++)f[g]=new iC(arguments[g])}else f=[].slice.call(arguments,0);var k=f.map(function(q){return a(q)});pC.push.apply(pC,k);var m=d.apply(b,f),n=Math.max(100,Number("1000")||300);if(this.length>n)for(O(4),c.pruned=!0;this.length>n;)this.shift();var p=typeof m!=="boolean"||m;return AC()&&p};var e=b.slice(0).map(function(f){return a(f)});pC.push.apply(pC,e);if(!oj.N){if(I(103)){}D(CC)}}var vC=function(a){return z[rj.zb].push(a)};function EC(a){vC(a)};function FC(){var a,b=lk(z.location.href);(a=b.hostname+b.pathname)&&bn("dl",encodeURIComponent(a));var c;var d=Xf.ctid;if(d){var e=Sl.Xe?1:0,f,g=fm(gm());f=g&&g.context;c=d+";"+Xf.canonicalContainerId+";"+(f&&f.fromContainerExecution?1:0)+";"+(f&&f.source||0)+";"+e}else c=void 0;var k=c;k&&bn("tdp",k);var m=il(!0);m!==void 0&&bn("frm",String(m))};function GC(){I(54)&&Dk&&z.addEventListener("securitypolicyviolation",function(a){if(a.disposition==="enforce"){var b=Bl(a.effectiveDirective);if(b){var c;var d=zl(b,a.blockedURI);c=d?xl[b][d]:void 0;var e;if(e=c)a:{try{var f=new URL(a.blockedURI),g=f.pathname.indexOf(";");e=g>=0?f.origin+f.pathname.substring(0,g):f.origin+f.pathname;break a}catch(q){}e=void 0}if(e){for(var k=l(c),m=k.next();!m.done;m=k.next()){var n=m.value;if(!n.Uk){n.Uk=!0;var p=String(n.endpoint);gn.hasOwnProperty(p)||(gn[p]=
!0,bn("csp",Object.keys(gn).join("~")))}}Al(b,a.blockedURI)}}}})};function HC(){var a;var b=em();if(b)if(b.canonicalContainerId)a=b.canonicalContainerId;else{var c,d=b.scriptContainerId||((c=b.destinations)==null?void 0:c[0]);a=d?"_"+d:void 0}else a=void 0;var e=a;e&&bn("pcid",e)};var IC=/^(https?:)?\/\//;
function JC(){var a;var b=fm(gm());if(b){for(;b.parent;){var c=fm(b.parent);if(!c)break;b=c}a=b}else a=void 0;var d=a;if(d){var e;a:{var f,g=(f=d.scriptElement)==null?void 0:f.src;if(g){var k;try{var m;k=(m=Mc())==null?void 0:m.getEntriesByType("resource")}catch(u){}if(k){for(var n=-1,p=l(k),q=p.next();!q.done;q=p.next()){var r=q.value;if(r.initiatorType==="script"&&(n+=1,r.name.replace(IC,"")===g.replace(IC,""))){e=n;break a}}O(146)}else O(145)}e=void 0}var v=e;v!==void 0&&(d.canonicalContainerId&&
bn("rtg",String(d.canonicalContainerId)),bn("slo",String(v)),bn("hlo",d.htmlLoadOrder||"-1"),bn("lst",String(d.loadScriptType||"0")))}else O(144)};

function dD(){};var eD=function(){};eD.prototype.toString=function(){return"undefined"};var fD=new eD;function mD(a,b){function c(g){var k=lk(g),m=fk(k,"protocol"),n=fk(k,"host",!0),p=fk(k,"port"),q=fk(k,"path").toLowerCase().replace(/\/$/,"");if(m===void 0||m==="http"&&p==="80"||m==="https"&&p==="443")m="web",p="default";return[m,n,p,q]}for(var d=c(String(a)),e=c(String(b)),f=0;f<d.length;f++)if(d[f]!==e[f])return!1;return!0}
function nD(a){return oD(a)?1:0}
function oD(a){var b=a.arg0,c=a.arg1;if(a.any_of&&Array.isArray(c)){for(var d=0;d<c.length;d++){var e=Zc(a,{});Zc({arg1:c[d],any_of:void 0},e);if(nD(e))return!0}return!1}switch(a["function"]){case "_cn":return Fg(b,c);case "_css":var f;a:{if(b)try{for(var g=0;g<Ag.length;g++){var k=Ag[g];if(b[k]!=null){f=b[k](c);break a}}}catch(m){}f=!1}return f;case "_ew":return Bg(b,c);case "_eq":return Gg(b,c);case "_ge":return Hg(b,c);case "_gt":return Jg(b,c);case "_lc":return Cg(b,c);case "_le":return Ig(b,
c);case "_lt":return Kg(b,c);case "_re":return Eg(b,c,a.ignore_case);case "_sw":return Lg(b,c);case "_um":return mD(b,c)}return!1};function pD(){var a;a=a===void 0?"":a;var b,c;return((b=data)==null?0:(c=b.blob)==null?0:c.hasOwnProperty(1))?String(data.blob[1]):a};function qD(){var a=[["cv",I(137)?pD():"1"],["rv",rj.Kh],["tc",tf.filter(function(b){return b}).length]];rj.Jh&&a.push(["x",rj.Jh]);Jj()&&a.push(["tag_exp",Jj()]);return a};var rD={},sD={};function tD(a){var b=a.eventId,c=a.kd,d=[],e=rD[b]||[];e.length&&d.push(["hf",e.join(".")]);var f=sD[b]||[];f.length&&d.push(["ht",f.join(".")]);c&&(delete rD[b],delete sD[b]);return d};function uD(){return!1}function vD(){var a={};return function(b,c,d){}};function wD(){var a=xD;return function(b,c,d){var e=d&&d.event;yD(c);var f=ph(b)?void 0:1,g=new Na;jb(c,function(r,v){var u=od(v,void 0,f);u===void 0&&v!==void 0&&O(44);g.set(r,u)});a.C.C.H=Mf();var k={xk:ag(b),eventId:e==null?void 0:e.id,priorityId:e!==void 0?e.priorityId:void 0,df:e!==void 0?function(r){e.Cc.df(r)}:void 0,vb:function(){return b},log:function(){},Km:{index:d==null?void 0:d.index,type:d==null?void 0:d.type,name:d==null?void 0:d.name},On:!!mA(b,3),originalEventData:e==null?void 0:
e.originalEventData};e&&e.cachedModelValues&&(k.cachedModelValues={gtm:e.cachedModelValues.gtm,ecommerce:e.cachedModelValues.ecommerce});if(uD()){var m=vD(),n,p;k.cb={Oi:[],ef:{},Nb:function(r,v,u){v===1&&(n=r);v===7&&(p=u);m(r,v,u)},Hg:Hh()};k.log=function(r){var v=xa.apply(1,arguments);n&&m(n,4,{level:r,source:p,message:v})}}var q=
Ke(a,k,[b,g]);a.C.C.H=void 0;q instanceof za&&(q.type==="return"?q=q.data:q=void 0);return nd(q,void 0,f)}}function yD(a){var b=a.gtmOnSuccess,c=a.gtmOnFailure;ab(b)&&(a.gtmOnSuccess=function(){D(b)});ab(c)&&(a.gtmOnFailure=function(){D(c)})};function zD(a){}zD.K="internal.addAdsClickIds";function AD(a,b){var c=this;}AD.publicName="addConsentListener";var BD=!1;function CD(a){for(var b=0;b<a.length;++b)if(BD)try{a[b]()}catch(c){O(77)}else a[b]()}function DD(a,b,c){var d=this,e;if(!bh(a)||!Yg(b)||!ch(c))throw J(this.getName(),["string","function","string|undefined"],arguments);CD([function(){K(d,"listen_data_layer",a)}]);e=DB().addListener(a,nd(b),c===null?void 0:c);return e}DD.K="internal.addDataLayerEventListener";function ED(a,b,c){}ED.publicName="addDocumentEventListener";function FD(a,b,c,d){}FD.publicName="addElementEventListener";function GD(a){return a.J.C};function HD(a){}HD.publicName="addEventCallback";
var ID=function(a){return typeof a==="string"?a:String(Jo())},LD=function(a,b){JD(a,"init",!1)||(KD(a,"init",!0),b())},JD=function(a,b,c){var d=MD(a);return rb(d,b,c)},ND=function(a,b,c,d){var e=MD(a),f=rb(e,b,d);e[b]=c(f)},KD=function(a,b,c){MD(a)[b]=c},MD=function(a){var b=Fo("autoEventsSettings",function(){return{}});b.hasOwnProperty(a)||(b[a]={});return b[a]},OD=function(a,b,c){var d={event:b,"gtm.element":a,"gtm.elementClasses":Jc(a,"className"),"gtm.elementId":a.for||zc(a,"id")||"","gtm.elementTarget":a.formTarget||
Jc(a,"target")||""};c&&(d["gtm.triggers"]=c.join(","));d["gtm.elementUrl"]=(a.attributes&&a.attributes.formaction?a.formAction:"")||a.action||Jc(a,"href")||a.src||a.code||a.codebase||"";return d};
var QD=function(a,b,c){if(!a.elements)return 0;for(var d=b.dataset[c],e=0,f=1;e<a.elements.length;e++){var g=a.elements[e];if(PD(g)){if(g.dataset[c]===d)return f;f++}}return 0},RD=function(a){if(a.form){var b;return((b=a.form)==null?0:b.tagName)?a.form:A.getElementById(a.form)}return Cc(a,["form"],100)},PD=function(a){var b=a.tagName.toLowerCase();return SD.indexOf(b)<0||b==="input"&&TD.indexOf(a.type.toLowerCase())>=0?!1:!0},SD=["input","select","textarea"],TD=["button","hidden","image","reset",
"submit"];
function XD(a){}XD.K="internal.addFormAbandonmentListener";function YD(a,b,c,d){}
YD.K="internal.addFormData";var ZD={},$D=[],aE={},bE=0,cE=0;
var eE=function(){xc(A,"change",function(a){for(var b=0;b<$D.length;b++)$D[b](a)});xc(z,"pagehide",function(){dE()})},dE=function(){jb(aE,function(a,b){var c=ZD[a];c&&jb(b,function(d,e){fE(e,c)})})},iE=function(a,b){var c=""+a;if(ZD[c])ZD[c].push(b);else{var d=[b];ZD[c]=d;var e=aE[c];e||(e={},aE[c]=e);$D.push(function(f){var g=f.target;if(g){var k=RD(g);if(k){var m=gE(k,"gtmFormInteractId",function(){return bE++}),n=gE(g,"gtmFormInteractFieldId",function(){return cE++}),p=e[m];p?(p.mc&&(z.clearTimeout(p.mc),
p.Ob.dataset.gtmFormInteractFieldId!==n&&fE(p,d)),p.Ob=g,hE(p,d,a)):(e[m]={form:k,Ob:g,sequenceNumber:0,mc:null},hE(e[m],d,a))}}})}},fE=function(a,b){var c=a.form,d=a.Ob,e=OD(c,"gtm.formInteract"),f=c.action;f&&f.tagName&&(f=c.cloneNode(!1).action);e["gtm.elementUrl"]=f;e["gtm.interactedFormName"]=c.getAttribute("name");e["gtm.interactedFormLength"]=c.length;e["gtm.interactedFormField"]=d;e["gtm.interactedFormFieldPosition"]=QD(c,d,"gtmFormInteractFieldId");e["gtm.interactSequenceNumber"]=a.sequenceNumber;
e["gtm.interactedFormFieldId"]=d.id;e["gtm.interactedFormFieldName"]=d.getAttribute("name");e["gtm.interactedFormFieldType"]=d.getAttribute("type");for(var g=0;g<b.length;g++)b[g](e);a.sequenceNumber++;a.mc=null},hE=function(a,b,c){c?a.mc=z.setTimeout(function(){fE(a,b)},c):fE(a,b)},gE=function(a,b,c){var d=a.dataset[b];if(d)return d;d=String(c());return a.dataset[b]=d};
function jE(a,b){if(!Yg(a)||!Wg(b))throw J(this.getName(),["function","Object|undefined"],arguments);var c=nd(b)||{},d=Number(c.interval);if(!d||d<0)d=0;var e=nd(a),f;JD("pix.fil","init")?f=JD("pix.fil","reg"):(eE(),f=iE,KD("pix.fil","reg",iE),KD("pix.fil","init",!0));f(d,e);}jE.K="internal.addFormInteractionListener";
var lE=function(a,b,c){var d=OD(a,"gtm.formSubmit");d["gtm.interactedFormName"]=a.getAttribute("name");d["gtm.interactedFormLength"]=a.length;d["gtm.willOpenInCurrentWindow"]=!b&&kE(a);c&&c.value&&(d["gtm.formSubmitButtonText"]=c.value);var e=a.action;e&&e.tagName&&(e=a.cloneNode(!1).action);d["gtm.elementUrl"]=e;d["gtm.formCanceled"]=b;return d},mE=function(a,b){var c=JD("pix.fsl",a?"nv.mwt":"mwt",0);z.setTimeout(b,c)},nE=function(a,b,c,d,e){var f=JD("pix.fsl",c?"nv.mwt":"mwt",0),g=JD("pix.fsl",
c?"runIfCanceled":"runIfUncanceled",[]);if(!g.length)return!0;var k=lE(a,c,e);O(121);if(k["gtm.elementUrl"]==="https://www.facebook.com/tr/")return O(122),!0;if(d&&f){for(var m=Ab(b,g.length),n=0;n<g.length;++n)g[n](k,m);return m.done}for(var p=0;p<g.length;++p)g[p](k,function(){});return!0},oE=function(){var a=[],b=function(c){return fb(a,function(d){return d.form===c})};return{store:function(c,d){var e=b(c);e?e.button=d:a.push({form:c,button:d})},get:function(c){var d=b(c);return d?d.button:null}}},
kE=function(a){var b=Jc(a,"target");return b&&b!=="_self"&&b!=="_parent"&&b!=="_top"?!1:!0},pE=function(){var a=oE(),b=HTMLFormElement.prototype.submit;xc(A,"click",function(c){var d=c.target;if(d){var e=Cc(d,["button","input"],100);if(e&&(e.type==="submit"||e.type==="image")&&e.name&&zc(e,"value")){var f=RD(e);f&&a.store(f,e)}}},!1);xc(A,"submit",function(c){var d=c.target;if(!d)return c.returnValue;var e=c.defaultPrevented||c.returnValue===!1,f=kE(d)&&!e,g=a.get(d),k=!0,m=function(){if(k){var n,
p={};g&&(n=A.createElement("input"),n.type="hidden",n.name=g.name,n.value=g.value,d.appendChild(n),g.getAttribute("formaction")&&(p.action=d.getAttribute("action"),Yb(d,g.getAttribute("formaction"))),g.hasAttribute("formenctype")&&(p.enctype=d.getAttribute("enctype"),d.setAttribute("enctype",g.getAttribute("formenctype"))),g.hasAttribute("formmethod")&&(p.method=d.getAttribute("method"),d.setAttribute("method",g.getAttribute("formmethod"))),g.hasAttribute("formvalidate")&&(p.validate=d.getAttribute("validate"),
d.setAttribute("validate",g.getAttribute("formvalidate"))),g.hasAttribute("formtarget")&&(p.target=d.getAttribute("target"),d.setAttribute("target",g.getAttribute("formtarget"))));b.call(d);n&&(d.removeChild(n),p.hasOwnProperty("action")&&Yb(d,p.action),p.hasOwnProperty("enctype")&&d.setAttribute("enctype",p.enctype),p.hasOwnProperty("method")&&d.setAttribute("method",p.method),p.hasOwnProperty("validate")&&d.setAttribute("validate",p.validate),p.hasOwnProperty("target")&&d.setAttribute("target",
p.target))}};if(nE(d,m,e,f,g))return k=!1,c.returnValue;mE(e,m);e||(c.preventDefault&&c.preventDefault(),c.returnValue=!1);return!1},!1);HTMLFormElement.prototype.submit=function(){var c=this,d=!0,e=function(){d&&b.call(c)};nE(c,e,!1,kE(c))?(b.call(c),d=!1):mE(!1,e)}};
function qE(a,b){if(!Yg(a)||!Wg(b))throw J(this.getName(),["function","Object|undefined"],arguments);var c=nd(b,this.J,1)||{},d=c.waitForCallbacks,e=c.waitForCallbacksTimeout,f=c.checkValidation;e=e&&e>0?e:2E3;var g=nd(a,this.J,1);if(d){var k=function(n){return Math.max(e,n)};ND("pix.fsl","mwt",k,0);f||ND("pix.fsl","nv.mwt",k,0)}var m=function(n){n.push(g);return n};ND("pix.fsl","runIfUncanceled",m,[]);f||ND("pix.fsl","runIfCanceled",
m,[]);JD("pix.fsl","init")||(pE(),KD("pix.fsl","init",!0));}qE.K="internal.addFormSubmitListener";
function vE(a){}vE.K="internal.addGaSendListener";function wE(a){if(!a)return{};var b=a.Km;return JA(b.type,b.index,b.name)}function xE(a){return a?{originatingEntity:wE(a)}:{}};function FE(a){var b=Eo.zones;return b?b.getIsAllowedFn(Xl(),a):function(){return!0}}function GE(){var a=Eo.zones;a&&a.unregisterChild(Xl())}
function HE(){pA(dm(),function(a){var b=a.originalEventData["gtm.uniqueEventId"],c=Eo.zones;return c?c.isActive(Xl(),b):!0});nA(dm(),function(a){var b,c;b=a.entityId;c=a.securityGroups;return FE(Number(a.originalEventData["gtm.uniqueEventId"]))(b,c)})};var IE=function(a,b){this.tagId=a;this.hf=b};
function JE(a,b){var c=this,d=void 0;
return d}JE.K="internal.loadGoogleTag";function KE(a){return new fd("",function(b){var c=this.evaluate(b);if(c instanceof fd)return new fd("",function(){var d=xa.apply(0,arguments),e=this,f=Zc(GD(this),null);f.eventId=a.eventId;f.priorityId=a.priorityId;f.originalEventData=a.originalEventData;var g=d.map(function(m){return e.evaluate(m)}),k=Ga(this.J);k.C=f;return c.kb.apply(c,[k].concat(ta(g)))})})};function LE(a,b,c){var d=this;}LE.K="internal.addGoogleTagRestriction";var ME={},NE=[];
function UE(a,b){}
UE.K="internal.addHistoryChangeListener";function VE(a,b,c){}VE.publicName="addWindowEventListener";function WE(a,b){return!0}WE.publicName="aliasInWindow";function XE(a,b,c){}XE.K="internal.appendRemoteConfigParameter";function YE(a){var b;return b}
YE.publicName="callInWindow";function ZE(a){}ZE.publicName="callLater";function $E(a){}$E.K="callOnDomReady";function aF(a){}aF.K="callOnWindowLoad";function bF(a,b){var c;return c}bF.K="internal.computeGtmParameter";function cF(a,b){var c=this;}cF.K="internal.consentScheduleFirstTry";function dF(a,b){var c=this;}dF.K="internal.consentScheduleRetry";function eF(a){var b;return b}eF.K="internal.copyFromCrossContainerData";function fF(a,b){var c;var d=od(c,this.J,ph(GD(this).vb())?2:1);d===void 0&&c!==void 0&&O(45);return d}fF.publicName="copyFromDataLayer";
function gF(a){var b=void 0;return b}gF.K="internal.copyFromDataLayerCache";function hF(a){var b;return b}hF.publicName="copyFromWindow";function iF(a){var b=void 0;return od(b,this.J,1)}iF.K="internal.copyKeyFromWindow";var jF=function(a){this.C=a},kF=function(a,b,c,d){var e;return(e=a.C[b])!=null&&e[c]?a.C[b][c].some(function(f){return f(d)}):!1},lF=function(a){return a===1&&Nm[a]===1&&!R(M.m.R)};var mF=function(){return"0"},nF=function(a){if(typeof a!=="string")return"";var b=["gclid","dclid","wbraid","_gl"];I(99)&&b.push("gbraid");return mk(a,b,"0")};var oF={},pF={},qF={},rF={},sF={},tF={},uF={},vF={},wF={},xF={},yF={},zF={},AF={},BF={},CF={},DF={},EF={},FF={},GF={},HF={},IF={},JF={},KF={},LF={},MF={},NF={},OF=(NF[M.m.Ca]=(oF[2]=[lF],oF),NF[M.m.Ne]=(pF[2]=[lF],pF),NF[M.m.De]=(qF[2]=[lF],qF),NF[M.m.jg]=(rF[2]=[lF],rF),NF[M.m.kg]=(sF[2]=[lF],sF),NF[M.m.lg]=(tF[2]=[lF],tF),NF[M.m.mg]=(uF[2]=[lF],uF),NF[M.m.ng]=(vF[2]=[lF],vF),NF[M.m.sb]=(wF[2]=[lF],wF),NF[M.m.Oe]=(xF[2]=[lF],xF),NF[M.m.Pe]=(yF[2]=[lF],yF),NF[M.m.Qe]=(zF[2]=[lF],zF),NF[M.m.Re]=(AF[2]=
[lF],AF),NF[M.m.Se]=(BF[2]=[lF],BF),NF[M.m.Te]=(CF[2]=[lF],CF),NF[M.m.Ue]=(DF[2]=[lF],DF),NF[M.m.Ve]=(EF[2]=[lF],EF),NF[M.m.Qa]=(FF[1]=[lF],FF),NF[M.m.Kc]=(GF[1]=[lF],GF),NF[M.m.Mc]=(HF[1]=[lF],HF),NF[M.m.od]=(IF[1]=[lF],IF),NF[M.m.ke]=(JF[1]=[function(a){return I(99)&&lF(a)}],JF),NF[M.m.uc]=(KF[1]=[lF],KF),NF[M.m.oa]=(LF[1]=[lF],LF),NF[M.m.Ga]=(MF[1]=[lF],MF),NF),PF={},QF=(PF[M.m.Qa]=mF,PF[M.m.Kc]=mF,PF[M.m.Mc]=mF,PF[M.m.od]=mF,PF[M.m.ke]=mF,PF[M.m.uc]=function(a){if(!Yc(a))return{};var b=Zc(a,null);
delete b.match_id;return b},PF[M.m.oa]=nF,PF[M.m.Ga]=nF,PF),RF={},SF={},TF=(SF.user_data=(RF[2]=[lF],RF),SF),UF={};var VF=function(a,b){this.conditions=a;this.C=b},WF=function(a,b,c,d){return kF(a.conditions,b,2,d)?{status:2}:kF(a.conditions,b,1,d)?a.C[b]===void 0?{status:2}:{status:1,value:a.C[b](c,d)}:{status:0,value:c}},XF=new VF(new jF(OF),QF),YF=new VF(new jF(TF),UF);function ZF(a,b,c){return WF(XF,a,b,c)}function $F(a,b,c){return WF(YF,a,b,c)}var aG=function(a,b,c,d){this.C=a;this.N=b;this.O=c;this.U=d};
aG.prototype.getValue=function(a){a=a===void 0?0:a;if(!this.N.some(function(b){return b(a)}))return this.O.some(function(b){return b(a)})?this.U(this.C):this.C};aG.prototype.H=function(){return Wc(this.C)==="array"||Yc(this.C)?Zc(this.C,null):this.C};var bG=function(){},cG=function(a,b){this.conditions=a;this.C=b},dG=function(a,b,c){var d,e=((d=a.conditions[b])==null?void 0:d[2])||[],f,g=((f=a.conditions[b])==null?void 0:f[1])||[];return new aG(c,e,g,a.C[b]||bG)},eG,fG;function gG(a,b,c,d,e){if(I(106)&&b!==void 0){var f=d(a,b,e);f.status===2?delete c[a]:c[a]=f.value}else c[a]=b}
var hG=function(a,b,c){this.eventName=b;this.D=c;this.C={};this.isAborted=!1;this.target=a;if(I(56)){this.metadata={};for(var d=c.eventMetadata||{},e=l(Object.keys(d)),f=e.next();!f.done;f=e.next()){var g=f.value;V(this,g,d[g])}}else this.metadata=Zc(c.eventMetadata||{},{})},vu=function(a,b){if(I(56)){var c,d;return(c=a.C[b])==null?void 0:(d=c.getValue)==null?void 0:d.call(c,U(a,"transmission_type"))}return a.C[b]},W=function(a,b,c){if(I(56)){var d=a.C,e;c===void 0?e=void 0:(eG!=null||(eG=new cG(OF,
QF)),e=dG(eG,b,c));d[b]=e}else gG(b,c,a.C,ZF,U(a,"transmission_type"))},iG=function(a,b){b=b===void 0?{}:b;if(I(56)){for(var c=l(Object.keys(a.C)),d=c.next();!d.done;d=c.next()){var e=d.value,f=void 0,g=void 0,k=void 0;b[e]=(f=a.C[e])==null?void 0:(k=(g=f).H)==null?void 0:k.call(g)}return b}return Zc(a.C,b)};hG.prototype.copyToHitData=function(a,b,c){var d=P(this.D,a);d===void 0&&(d=b);if(d!==void 0&&c!==void 0&&bb(d)&&I(87))try{d=c(d)}catch(e){}d!==void 0&&W(this,a,d)};
var U=function(a,b){if(I(56)){var c=a.metadata[b];if(b==="transmission_type"){var d;return c==null?void 0:(d=c.H)==null?void 0:d.call(c)}var e;return c==null?void 0:(e=c.getValue)==null?void 0:e.call(c,U(a,"transmission_type"))}return a.metadata[b]},V=function(a,b,c){if(I(56)){var d=a.metadata,e;c===void 0?e=void 0:(fG!=null||(fG=new cG(TF,UF)),e=dG(fG,b,c));d[b]=e}else if(gG(b,c,a.metadata,$F,U(a,"transmission_type")),I(106)&&b==="transmission_type"){for(var f=l(Object.keys(a.metadata)),g=f.next();!g.done;g=
f.next()){var k=g.value;k!=="transmission_type"&&V(a,k,U(a,k))}for(var m=l(Object.keys(a.C)),n=m.next();!n.done;n=m.next()){var p=n.value;W(a,p,vu(a,p))}}},jG=function(a,b){b=b===void 0?{}:b;if(I(56)){for(var c=l(Object.keys(a.metadata)),d=c.next();!d.done;d=c.next()){var e=d.value,f=void 0,g=void 0,k=void 0;b[e]=(f=a.metadata[e])==null?void 0:(k=(g=f).H)==null?void 0:k.call(g)}return b}return Zc(a.metadata,b)},dx=function(a,b,c){var d=a.target.destinationId;Tl||(d=hm(d));var e=Iv(d);return e&&e[b]!==
void 0?e[b]:c};function kG(a,b){var c;if(!Vg(a)||!Wg(b))throw J(this.getName(),["Object","Object|undefined"],arguments);var d=nd(b)||{},e=nd(a,this.J,1).hc(),f=e.D;d.omitEventContext&&(f=wp(new lp(e.D.eventId,e.D.priorityId)));var g=new hG(e.target,e.eventName,f);if(!d.omitHitData)for(var k=iG(e),m=l(Object.keys(k)),n=m.next();!n.done;n=m.next()){var p=n.value;W(g,p,k[p])}if(d.omitMetadata)g.metadata={};else for(var q=jG(e),r=l(Object.keys(q)),v=r.next();!v.done;v=
r.next()){var u=v.value;V(g,u,q[u])}g.isAborted=e.isAborted;c=od(ov(g),this.J,1);return c}kG.K="internal.copyPreHit";function lG(a,b){var c=null;return od(c,this.J,2)}lG.publicName="createArgumentsQueue";function mG(a){return od(function(c){var d=SA();if(typeof c==="function")d(function(){c(function(f,g,k){var m=
SA(),n=m&&m.getByName&&m.getByName(f);return(new z.gaplugins.Linker(n)).decorate(g,k)})});else if(Array.isArray(c)){var e=String(c[0]).split(".");b[e.length===1?e[0]:e[1]]&&d.apply(null,c)}else if(c==="isLoaded")return!!d.loaded},this.J,1)}mG.K="internal.createGaCommandQueue";function nG(a){return od(function(){if(!ab(e.push))throw Error("Object at "+a+" in window is not an array.");e.push.apply(e,Array.prototype.slice.call(arguments,0))},this.J,
ph(GD(this).vb())?2:1)}nG.publicName="createQueue";function oG(a,b){var c=null;return c}oG.K="internal.createRegex";function pG(){var a={};return a};function qG(a){}qG.K="internal.declareConsentState";function rG(a){var b="";return b}rG.K="internal.decodeUrlHtmlEntities";function sG(a,b,c){var d;return d}sG.K="internal.decorateUrlWithGaCookies";function tG(){}tG.K="internal.deferCustomEvents";function uG(a){var b;K(this,"detect_user_provided_data","auto");var c=nd(a)||{},d=hw({Ud:!!c.includeSelector,Vd:!!c.includeVisibility,nf:c.excludeElementSelectors,Lb:c.fieldFilters,Jg:!!c.selectMultipleElements});b=new Na;var e=new bd;b.set("elements",e);for(var f=d.elements,g=0;g<f.length;g++)e.push(vG(f[g]));d.Gi!==void 0&&b.set("preferredEmailElement",vG(d.Gi));b.set("status",d.status);if(I(124)&&c.performDataLayerSearch&&!/Mobile|iPhone|iPad|iPod|Android|IEMobile/.test(gc&&
gc.userAgent||"")){}return b}
var wG=function(a){switch(a){case fw.Tb:return"email";case fw.Xc:return"phone_number";case fw.Vc:return"first_name";case fw.Wc:return"last_name";case fw.Ph:return"street";case fw.Lg:return"city";case fw.Ih:return"region";case fw.Ze:return"postal_code";case fw.ee:return"country"}},vG=function(a){var b=new Na;b.set("userData",a.aa);b.set("tagName",a.tagName);a.querySelector!==void 0&&b.set("querySelector",a.querySelector);a.isVisible!==void 0&&b.set("isVisible",a.isVisible);if(I(33)){}else switch(a.type){case fw.Tb:b.set("type","email")}return b};uG.K="internal.detectUserProvidedData";
function zG(a,b){return f}zG.K="internal.enableAutoEventOnClick";var CG=function(a){if(!AG){var b=function(){var c=A.body;if(c)if(BG)(new MutationObserver(function(){for(var e=0;e<AG.length;e++)D(AG[e])})).observe(c,{childList:!0,subtree:!0});else{var d=!1;xc(c,"DOMNodeInserted",function(){d||(d=!0,D(function(){d=!1;for(var e=0;e<AG.length;e++)D(AG[e])}))})}};AG=[];A.body?b():D(b)}AG.push(a)},BG=!!z.MutationObserver,AG;
function HG(a,b){return p}HG.K="internal.enableAutoEventOnElementVisibility";function IG(){}IG.K="internal.enableAutoEventOnError";var JG={},KG=[],LG={},MG=0,NG=0;
var PG=function(){jb(LG,function(a,b){var c=JG[a];c&&jb(b,function(d,e){OG(e,c)})})},SG=function(a,b){var c=""+b;if(JG[c])JG[c].push(a);else{var d=[a];JG[c]=d;var e=LG[c];e||(e={},LG[c]=e);KG.push(function(f){var g=f.target;if(g){var k=RD(g);if(k){var m=QG(k,"gtmFormInteractId",function(){return MG++}),n=QG(g,"gtmFormInteractFieldId",function(){return NG++});if(m!==null&&n!==null){var p=e[m];p?(p.mc&&(z.clearTimeout(p.mc),p.Ob.getAttribute("data-gtm-form-interact-field-id")!==n&&OG(p,d)),p.Ob=g,RG(p,
d,b)):(e[m]={form:k,Ob:g,sequenceNumber:0,mc:null},RG(e[m],d,b))}}}})}},OG=function(a,b){var c=a.form,d=a.Ob,e=OD(c,"gtm.formInteract",b),f=c.action;f&&f.tagName&&(f=c.cloneNode(!1).action);e["gtm.elementUrl"]=f;e["gtm.interactedFormName"]=c.getAttribute("name")!=null?c.getAttribute("name"):void 0;e["gtm.interactedFormLength"]=c.length;e["gtm.interactedFormField"]=d;e["gtm.interactedFormFieldId"]=d.id;e["gtm.interactedFormFieldName"]=d.getAttribute("name")!=null?d.getAttribute("name"):void 0;e["gtm.interactedFormFieldPosition"]=
QD(c,d,"gtmFormInteractFieldId");e["gtm.interactedFormFieldType"]=d.getAttribute("type")!=null?d.getAttribute("type"):void 0;e["gtm.interactSequenceNumber"]=a.sequenceNumber;vC(e);a.sequenceNumber++;a.mc=null},RG=function(a,b,c){c?a.mc=z.setTimeout(function(){OG(a,b)},c):OG(a,b)},QG=function(a,b,c){var d;try{if(d=a.dataset[b])return d;d=String(c());a.dataset[b]=d}catch(e){d=null}return d};
function TG(a,b){var c=this;if(!Wg(a))throw J(this.getName(),["Object|undefined","any"],arguments);CD([function(){K(c,"detect_form_interaction_events")}]);var d=ID(b),e=a&&Number(a.get("interval"));e>0&&isFinite(e)||(e=0);if(JD("fil","init",!1)){var f=JD("fil","reg");if(f)f(d,e);else throw Error("Failed to register trigger: "+d);}else xc(A,"change",function(g){for(var k=0;k<KG.length;k++)KG[k](g)}),xc(z,"pagehide",function(){PG()}),
SG(d,e),KD("fil","reg",SG),KD("fil","init",!0);return d}TG.K="internal.enableAutoEventOnFormInteraction";
var UG=function(a,b,c,d,e){var f=JD("fsl",c?"nv.mwt":"mwt",0),g;g=c?JD("fsl","nv.ids",[]):JD("fsl","ids",[]);if(!g.length)return!0;var k=OD(a,"gtm.formSubmit",g),m=a.action;m&&m.tagName&&(m=a.cloneNode(!1).action);O(121);if(m==="https://www.facebook.com/tr/")return O(122),!0;k["gtm.elementUrl"]=m;k["gtm.formCanceled"]=c;a.getAttribute("name")!=null&&(k["gtm.interactedFormName"]=a.getAttribute("name"));e&&(k["gtm.formSubmitElement"]=e,k["gtm.formSubmitElementText"]=e.value);if(d&&f){if(!uC(k,wC(b,
f),f))return!1}else uC(k,function(){},f||2E3);return!0},VG=function(){var a=[],b=function(c){return fb(a,function(d){return d.form===c})};return{store:function(c,d){var e=b(c);e?e.button=d:a.push({form:c,button:d})},get:function(c){var d=b(c);if(d)return d.button}}},WG=function(a){var b=a.target;return b&&b!=="_self"&&b!=="_parent"&&b!=="_top"?!1:!0},XG=function(){var a=VG(),b=HTMLFormElement.prototype.submit;xc(A,"click",function(c){var d=c.target;if(d){var e=Cc(d,["button","input"],100);if(e&&(e.type===
"submit"||e.type==="image")&&e.name&&zc(e,"value")){var f=RD(e);f&&a.store(f,e)}}},!1);xc(A,"submit",function(c){var d=c.target;if(!d)return c.returnValue;var e=c.defaultPrevented||c.returnValue===!1,f=WG(d)&&!e,g=a.get(d),k=!0;if(UG(d,function(){if(k){var m=null,n={};g&&(m=A.createElement("input"),m.type="hidden",m.name=g.name,m.value=g.value,d.appendChild(m),g.hasAttribute("formaction")&&(n.action=d.getAttribute("action"),Yb(d,g.getAttribute("formaction"))),g.hasAttribute("formenctype")&&(n.enctype=
d.getAttribute("enctype"),d.setAttribute("enctype",g.getAttribute("formenctype"))),g.hasAttribute("formmethod")&&(n.method=d.getAttribute("method"),d.setAttribute("method",g.getAttribute("formmethod"))),g.hasAttribute("formvalidate")&&(n.validate=d.getAttribute("validate"),d.setAttribute("validate",g.getAttribute("formvalidate"))),g.hasAttribute("formtarget")&&(n.target=d.getAttribute("target"),d.setAttribute("target",g.getAttribute("formtarget"))));b.call(d);m&&(d.removeChild(m),n.hasOwnProperty("action")&&
Yb(d,n.action),n.hasOwnProperty("enctype")&&d.setAttribute("enctype",n.enctype),n.hasOwnProperty("method")&&d.setAttribute("method",n.method),n.hasOwnProperty("validate")&&d.setAttribute("validate",n.validate),n.hasOwnProperty("target")&&d.setAttribute("target",n.target))}},e,f,g))k=!1;else return e||(c.preventDefault&&c.preventDefault(),c.returnValue=!1),!1;return c.returnValue},!1);HTMLFormElement.prototype.submit=function(){var c=this,d=!0;UG(c,function(){d&&b.call(c)},!1,WG(c))&&(b.call(c),d=
!1)}};
function YG(a,b){var c=this;if(!Wg(a))throw J(this.getName(),["Object|undefined","any"],arguments);var d=a&&a.get("waitForTags");CD([function(){K(c,"detect_form_submit_events",{waitForTags:!!d})}]);var e=a&&a.get("checkValidation"),f=ID(b);if(d){var g=Number(a.get("waitForTagsTimeout"));g>0&&isFinite(g)||(g=2E3);var k=function(n){return Math.max(g,n)};ND("fsl","mwt",k,0);e||ND("fsl","nv.mwt",k,0)}var m=function(n){n.push(f);
return n};ND("fsl","ids",m,[]);e||ND("fsl","nv.ids",m,[]);JD("fsl","init",!1)||(XG(),KD("fsl","init",!0));return f}YG.K="internal.enableAutoEventOnFormSubmit";
function cH(){var a=this;}cH.K="internal.enableAutoEventOnGaSend";var dH={},eH=[];
var gH=function(a,b){var c=""+b;if(dH[c])dH[c].push(a);else{var d=[a];dH[c]=d;var e=fH("gtm.historyChange-v2"),f=-1;eH.push(function(g){f>=0&&z.clearTimeout(f);b?f=z.setTimeout(function(){e(g,d);f=-1},b):e(g,d)})}},fH=function(a){var b=z.location.href,c={source:null,state:z.history.state||null,url:ik(lk(b)),Oa:fk(lk(b),"fragment")};return function(d,e){var f=c,g={};g[f.source]=!0;g[d.source]=!0;if(!g.popstate||!g.hashchange||f.Oa!==d.Oa){var k={event:a,"gtm.historyChangeSource":d.source,"gtm.oldUrlFragment":c.Oa,
"gtm.newUrlFragment":d.Oa,"gtm.oldHistoryState":c.state,"gtm.newHistoryState":d.state,"gtm.oldUrl":c.url,"gtm.newUrl":d.url};e&&(k["gtm.triggers"]=e.join(","));c=d;vC(k)}}},hH=function(a,b){var c=z.history,d=c[a];if(ab(d))try{c[a]=function(e,f,g){d.apply(c,[].slice.call(arguments,0));var k=z.location.href;b({source:a,state:e,url:ik(lk(k)),Oa:fk(lk(k),"fragment")})}}catch(e){}},jH=function(a){z.addEventListener("popstate",function(b){var c=iH(b);a({source:"popstate",state:b.state,url:ik(lk(c)),Oa:fk(lk(c),
"fragment")})})},kH=function(a){z.addEventListener("hashchange",function(b){var c=iH(b);a({source:"hashchange",state:null,url:ik(lk(c)),Oa:fk(lk(c),"fragment")})})},iH=function(a){var b,c;return((b=a.target)==null?void 0:(c=b.location)==null?void 0:c.href)||z.location.href};
function lH(a,b){var c=this;if(!Wg(a))throw J(this.getName(),["Object|undefined","any"],arguments);CD([function(){K(c,"detect_history_change_events")}]);var d=a&&a.get("useV2EventName")?"ehl":"hl",e=Number(a&&a.get("interval"));e>0&&isFinite(e)||(e=0);var f;if(!JD(d,"init",!1)){var g;d==="ehl"?(g=function(m){for(var n=0;n<eH.length;n++)eH[n](m)},f=ID(b),gH(f,e),KD(d,"reg",gH)):g=fH("gtm.historyChange");kH(g);jH(g);hH("pushState",
g);hH("replaceState",g);KD(d,"init",!0)}else if(d==="ehl"){var k=JD(d,"reg");k&&(f=ID(b),k(f,e))}d==="hl"&&(f=void 0);return f}lH.K="internal.enableAutoEventOnHistoryChange";var mH=["http://","https://","javascript:","file://"];
var nH=function(a,b){if(a.which===2||a.ctrlKey||a.shiftKey||a.altKey||a.metaKey)return!1;var c=Jc(b,"href");if(c.indexOf(":")!==-1&&!mH.some(function(k){return vb(c,k)}))return!1;var d=c.indexOf("#"),e=Jc(b,"target");if(e&&e!=="_self"&&e!=="_parent"&&e!=="_top"||d===0)return!1;if(d>0){var f=ik(lk(c)),g=ik(lk(z.location.href));return f!==g}return!0},oH=function(a,b){for(var c=fk(lk((b.attributes&&b.attributes.formaction?b.formAction:"")||b.action||Jc(b,"href")||b.src||b.code||b.codebase||""),"host"),
d=0;d<a.length;d++)try{if((new RegExp(a[d])).test(c))return!1}catch(e){}return!0},pH=function(){function a(c){var d=c.target;if(d&&c.which!==3&&!(c.C||c.timeStamp&&c.timeStamp===b)){b=c.timeStamp;d=Cc(d,["a","area"],100);if(!d)return c.returnValue;var e=c.defaultPrevented||c.returnValue===!1,f=JD("lcl",e?"nv.mwt":"mwt",0),g;g=e?JD("lcl","nv.ids",[]):JD("lcl","ids",[]);for(var k=[],m=0;m<g.length;m++){var n=g[m],p=JD("lcl","aff.map",{})[n];p&&!oH(p,d)||k.push(n)}if(k.length){var q=nH(c,d),r=OD(d,"gtm.linkClick",
k);r["gtm.elementText"]=Ac(d);r["gtm.willOpenInNewWindow"]=!q;if(q&&!e&&f&&d.href){var v=!!fb(String(Jc(d,"rel")||"").split(" "),function(x){return x.toLowerCase()==="noreferrer"}),u=z[(Jc(d,"target")||"_self").substring(1)],t=!0,w=wC(function(){var x;if(x=t&&u){var y;a:if(v){var B;try{B=new MouseEvent(c.type,{bubbles:!0})}catch(C){if(!A.createEvent){y=!1;break a}B=A.createEvent("MouseEvents");B.initEvent(c.type,!0,!0)}B.C=!0;c.target.dispatchEvent(B);y=!0}else y=!1;x=!y}x&&(u.location.href=Jc(d,
"href"))},f);if(uC(r,w,f))t=!1;else return c.preventDefault&&c.preventDefault(),c.returnValue=!1}else uC(r,function(){},f||2E3);return!0}}}var b=0;xc(A,"click",a,!1);xc(A,"auxclick",a,!1)};
function qH(a,b){var c=this;if(!Wg(a))throw J(this.getName(),["Object|undefined","any"],arguments);var d=nd(a);CD([function(){K(c,"detect_link_click_events",d)}]);var e=d&&!!d.waitForTags,f=d&&!!d.checkValidation,g=d?d.affiliateDomains:void 0,k=ID(b);if(e){var m=Number(d.waitForTagsTimeout);m>0&&isFinite(m)||(m=2E3);var n=function(q){return Math.max(m,q)};ND("lcl","mwt",n,0);f||ND("lcl","nv.mwt",n,0)}var p=function(q){q.push(k);
return q};ND("lcl","ids",p,[]);f||ND("lcl","nv.ids",p,[]);g&&ND("lcl","aff.map",function(q){q[k]=g;return q},{});JD("lcl","init",!1)||(pH(),KD("lcl","init",!0));return k}qH.K="internal.enableAutoEventOnLinkClick";var rH,sH;
var tH=function(a){return JD("sdl",a,{})},uH=function(a,b,c){if(b){var d=Array.isArray(a)?a:[a];ND("sdl",c,function(e){for(var f=0;f<d.length;f++){var g=String(d[f]);e.hasOwnProperty(g)||(e[g]=[]);e[g].push(b)}return e},{})}},xH=function(){function a(){vH();wH(a,!0)}return a},yH=function(){function a(){f?e=z.setTimeout(a,c):(e=0,vH(),wH(b));f=!1}function b(){d&&rH();e?f=!0:(e=z.setTimeout(a,c),KD("sdl","pending",!0))}var c=250,d=!1;A.scrollingElement&&A.documentElement&&(c=50,d=!0);var e=0,f=!1;return b},
wH=function(a,b){JD("sdl","init",!1)&&!zH()&&(b?yc(z,"scrollend",a):yc(z,"scroll",a),yc(z,"resize",a),KD("sdl","init",!1))},vH=function(){var a=rH(),b=a.depthX,c=a.depthY,d=b/sH.scrollWidth*100,e=c/sH.scrollHeight*100;AH(b,"horiz.pix","PIXELS","horizontal");AH(d,"horiz.pct","PERCENT","horizontal");AH(c,"vert.pix","PIXELS","vertical");AH(e,"vert.pct","PERCENT","vertical");KD("sdl","pending",!1)},AH=function(a,b,c,d){var e=tH(b),f={},g;for(g in e)if(f={ae:f.ae},f.ae=g,e.hasOwnProperty(f.ae)){var k=
Number(f.ae);if(!(a<k)){var m={};EC((m.event="gtm.scrollDepth",m["gtm.scrollThreshold"]=k,m["gtm.scrollUnits"]=c.toLowerCase(),m["gtm.scrollDirection"]=d,m["gtm.triggers"]=e[f.ae].join(","),m));ND("sdl",b,function(n){return function(p){delete p[n.ae];return p}}(f),{})}}},CH=function(){ND("sdl","scr",function(a){a||(a=A.scrollingElement||A.body&&A.body.parentNode);return sH=a},!1);ND("sdl","depth",function(a){a||(a=BH());return rH=a},!1)},BH=function(){var a=0,b=0;return function(){var c=Lv(),d=c.height;
a=Math.max(sH.scrollLeft+c.width,a);b=Math.max(sH.scrollTop+d,b);return{depthX:a,depthY:b}}},zH=function(){return!!(Object.keys(tH("horiz.pix")).length||Object.keys(tH("horiz.pct")).length||Object.keys(tH("vert.pix")).length||Object.keys(tH("vert.pct")).length)};
function DH(a,b){var c=this;if(!Vg(a))throw J(this.getName(),["Object","any"],arguments);CD([function(){K(c,"detect_scroll_events")}]);CH();if(!sH)return;var d=ID(b),e=nd(a);switch(e.horizontalThresholdUnits){case "PIXELS":uH(e.horizontalThresholds,d,"horiz.pix");break;case "PERCENT":uH(e.horizontalThresholds,d,"horiz.pct")}switch(e.verticalThresholdUnits){case "PIXELS":uH(e.verticalThresholds,d,"vert.pix");break;case "PERCENT":uH(e.verticalThresholds,
d,"vert.pct")}JD("sdl","init",!1)?JD("sdl","pending",!1)||D(function(){vH()}):(KD("sdl","init",!0),KD("sdl","pending",!0),D(function(){vH();if(zH()){var f=yH();"onscrollend"in z?(f=xH(),xc(z,"scrollend",f)):xc(z,"scroll",f);xc(z,"resize",f)}else KD("sdl","init",!1)}));return d}DH.K="internal.enableAutoEventOnScroll";function EH(a){return function(){if(a.limit&&a.Ai>=a.limit)a.Eg&&z.clearInterval(a.Eg);else{a.Ai++;var b=qb();vC({event:a.eventName,"gtm.timerId":a.Eg,"gtm.timerEventNumber":a.Ai,"gtm.timerInterval":a.interval,"gtm.timerLimit":a.limit,"gtm.timerStartTime":a.al,"gtm.timerCurrentTime":b,"gtm.timerElapsedTime":b-a.al,"gtm.triggers":a.co})}}}
function FH(a,b){
return f}FH.K="internal.enableAutoEventOnTimer";
var GH=function(a,b,c){function d(){var g=a();f+=e?(qb()-e)*g.playbackRate/1E3:0;e=qb()}var e=0,f=0;return{createEvent:function(g,k,m){var n=a(),p=n.ei,q=m?Math.round(m):k?Math.round(n.ei*k):Math.round(n.Ak),r=k!==void 0?Math.round(k*100):p<=0?0:Math.round(q/p*100),v=A.hidden?!1:Mv(c)>=.5;d();var u=void 0;b!==void 0&&(u=[b]);var t=OD(c,"gtm.video",u);t["gtm.videoProvider"]="youtube";t["gtm.videoStatus"]=g;t["gtm.videoUrl"]=n.url;t["gtm.videoTitle"]=n.title;t["gtm.videoDuration"]=Math.round(p);t["gtm.videoCurrentTime"]=
Math.round(q);t["gtm.videoElapsedTime"]=Math.round(f);t["gtm.videoPercent"]=r;t["gtm.videoVisible"]=v;return t},Vk:function(){e=qb()},Od:function(){d()}}};var ac=va(["data-gtm-yt-inspected-"]),HH=["www.youtube.com","www.youtube-nocookie.com"],IH,JH=!1;
var KH=function(a,b,c){var d=a.map(function(g){return{Na:g,Jf:g,Hf:void 0}});if(!b.length)return d;var e=b.map(function(g){return{Na:g*c,Jf:void 0,Hf:g}});if(!d.length)return e;var f=d.concat(e);f.sort(function(g,k){return g.Na-k.Na});return f},LH=function(a){a=a===void 0?[]:a;for(var b=[],c=0;c<a.length;c++)a[c]<0||b.push(a[c]);b.sort(function(d,e){return d-e});return b},MH=function(a){a=a===void 0?[]:a;for(var b=[],c=0;c<a.length;c++)a[c]>100||a[c]<0||(b[c]=a[c]/100);b.sort(function(d,e){return d-
e});return b},NH=function(a,b){var c,d;function e(){v=GH(function(){return{url:w,title:x,ei:t,Ak:a.getCurrentTime(),playbackRate:y}},b.Rb,a.getIframe());t=0;x=w="";y=1;return f}function f(H){switch(H){case 1:t=Math.round(a.getDuration());w=a.getVideoUrl();if(a.getVideoData){var N=a.getVideoData();x=N?N.title:""}y=a.getPlaybackRate();if(b.Xh){var L=v.createEvent("start");vC(L)}else v.Od();u=KH(b.Ii,b.Hi,a.getDuration());return g(H);default:return f}}function g(){B=a.getCurrentTime();C=pb().getTime();
v.Vk();r();return k}function k(H){var N;switch(H){case 0:return n(H);case 2:N="pause";case 3:var L=a.getCurrentTime()-B;N=Math.abs((pb().getTime()-C)/1E3*y-L)>1?"seek":N||"buffering";if(a.getCurrentTime())if(b.Wh){var T=v.createEvent(N);vC(T)}else v.Od();q();return m;case -1:return e(H);default:return k}}function m(H){switch(H){case 0:return n(H);case 1:return g(H);case -1:return e(H);default:return m}}function n(){for(;d;){var H=c;z.clearTimeout(d);H()}if(b.Vh){var N=v.createEvent("complete",1);
vC(N)}return e(-1)}function p(){}function q(){d&&(z.clearTimeout(d),d=0,c=p)}function r(){if(u.length&&y!==0){var H=-1,N;do{N=u[0];if(N.Na>a.getDuration())return;H=(N.Na-a.getCurrentTime())/y;if(H<0&&(u.shift(),u.length===0))return}while(H<0);c=function(){d=0;c=p;if(u.length>0&&u[0].Na===N.Na){u.shift();var L=v.createEvent("progress",N.Hf,N.Jf);vC(L)}r()};d=z.setTimeout(c,H*1E3)}}var v,u=[],t,w,x,y,B,C,E=e(-1);d=0;c=p;return{onStateChange:function(H){E=E(H)},onPlaybackRateChange:function(H){B=a.getCurrentTime();
C=pb().getTime();v.Od();y=H;q();r()}}},PH=function(a){D(function(){function b(){for(var d=c.getElementsByTagName("iframe"),e=d.length,f=0;f<e;f++)OH(d[f],a)}var c=A;b();CG(b)})},OH=function(a,b){if(!a.getAttribute("data-gtm-yt-inspected-"+b.Rb)&&(cc(a,"data-gtm-yt-inspected-"+b.Rb),QH(a,b.rf))){a.id||(a.id=RH());var c=z.YT,d=c.get(a.id);d||(d=new c.Player(a.id));var e=NH(d,b),f={},g;for(g in e)f={Cf:f.Cf},f.Cf=g,e.hasOwnProperty(f.Cf)&&d.addEventListener(f.Cf,function(k){return function(m){return e[k.Cf](m.data)}}(f))}},
QH=function(a,b){var c=a.getAttribute("src");if(SH(c,"embed/")){if(c.indexOf("enablejsapi=1")>0)return!0;if(b){var d;var e=c.indexOf("?")!==-1?"&":"?";c.indexOf("origin=")>-1?d=c+e+"enablejsapi=1":(IH||(IH=A.location.protocol+"//"+A.location.hostname,A.location.port&&(IH+=":"+A.location.port)),d=c+e+"enablejsapi=1&origin="+encodeURIComponent(IH));var f;f=Jb(d);a.src=Kb(f).toString();return!0}}return!1},SH=function(a,b){if(!a)return!1;for(var c=0;c<HH.length;c++)if(a.indexOf("//"+HH[c]+"/"+b)>=0)return!0;
return!1},RH=function(){var a=""+Math.round(Math.random()*1E9);return A.getElementById(a)?RH():a};
function TH(a,b){var c=this;var d=function(){PH(q)};if(!Vg(a))throw J(this.getName(),["Object","any"],arguments);CD([function(){K(c,"detect_youtube_activity_events",{fixMissingApi:!!a.get("fixMissingApi")})}]);var e=ID(b),f=!!a.get("captureStart"),g=!!a.get("captureComplete"),k=!!a.get("capturePause"),m=MH(nd(a.get("progressThresholdsPercent"))),n=LH(nd(a.get("progressThresholdsTimeInSeconds"))),p=!!a.get("fixMissingApi");
if(!(f||g||k||m.length||n.length))return;var q={Xh:f,Vh:g,Wh:k,Hi:m,Ii:n,rf:p,Rb:e},r=z.YT;if(r)return r.ready&&r.ready(d),e;var v=z.onYouTubeIframeAPIReady;z.onYouTubeIframeAPIReady=function(){v&&v();d()};D(function(){for(var u=A.getElementsByTagName("script"),t=u.length,w=0;w<t;w++){var x=u[w].getAttribute("src");if(SH(x,"iframe_api")||SH(x,"player_api"))return e}for(var y=A.getElementsByTagName("iframe"),B=y.length,C=0;C<B;C++)if(!JH&&QH(y[C],q.rf))return sc("https://www.youtube.com/iframe_api"),
JH=!0,e});return e}TH.K="internal.enableAutoEventOnYouTubeActivity";JH=!1;function UH(a,b){if(!bh(a)||!Wg(b))throw J(this.getName(),["string","Object|undefined"],arguments);var c=b?nd(b):{},d=a,e=!1;var f=JSON.parse(d);if(!f)throw Error("Invalid boolean expression string was given.");e=wh(f,c);return e}UH.K="internal.evaluateBooleanExpression";var VH;function WH(a){var b=!1;return b}WH.K="internal.evaluateMatchingRules";function FI(){return Aq(7)&&Aq(9)&&Aq(10)};function LJ(a,b,c,d){}LJ.K="internal.executeEventProcessor";function MJ(a){var b;return od(b,this.J,1)}MJ.K="internal.executeJavascriptString";function NJ(a){var b;return b};function OJ(a){var b={};return od(b)}OJ.K="internal.getAdsCookieWritingOptions";function PJ(a){var b=!1;return b}PJ.K="internal.getAllowAdPersonalization";function QJ(a,b){b=b===void 0?!0:b;var c;return c}QJ.K="internal.getAuid";var RJ=null;
function SJ(){var a=new Na;K(this,"read_container_data"),I(48)&&RJ?a=RJ:(a.set("containerId",'G-QDZY9PPZZ7'),a.set("version",'1'),a.set("environmentName",''),a.set("debugMode",bg),a.set("previewMode",cg.fl),a.set("environmentMode",cg.Gm),a.set("firstPartyServing",Lj()||zj),a.set("containerUrl",jc),a.Ma(),I(48)&&(RJ=a));return a}
SJ.publicName="getContainerVersion";function TJ(a,b){b=b===void 0?!0:b;var c;return c}TJ.publicName="getCookieValues";function UJ(){return Kn()}UJ.K="internal.getCountryCode";function VJ(){var a=[];a=$l();return od(a)}VJ.K="internal.getDestinationIds";function WJ(a){var b=new Na;return b}WJ.K="internal.getDeveloperIds";function XJ(a,b){var c=null;return c}XJ.K="internal.getElementAttribute";function YJ(a){var b=null;return b}YJ.K="internal.getElementById";function ZJ(a){var b="";return b}ZJ.K="internal.getElementInnerText";function $J(a,b){var c=null;return od(c)}$J.K="internal.getElementProperty";function aK(a){var b;return b}aK.K="internal.getElementValue";function bK(a){var b=0;return b}bK.K="internal.getElementVisibilityRatio";function cK(a){var b=null;return b}cK.K="internal.getElementsByCssSelector";
function dK(a){var b;if(!bh(a))throw J(this.getName(),["string"],arguments);K(this,"read_event_data",a);var c;a:{var d=a,e=GD(this).originalEventData;if(e){for(var f=e,g={},k={},m={},n=[],p=d.split("\\\\"),q=0;q<p.length;q++){for(var r=p[q].split("\\."),v=0;v<r.length;v++){for(var u=r[v].split("."),t=0;t<u.length;t++)n.push(u[t]),t!==u.length-1&&n.push(m);v!==r.length-1&&n.push(k)}q!==p.length-1&&n.push(g)}for(var w=[],x="",y=l(n),B=y.next();!B.done;B=
y.next()){var C=B.value;C===m?(w.push(x),x=""):x=C===g?x+"\\":C===k?x+".":x+C}x&&w.push(x);for(var E=l(w),H=E.next();!H.done;H=E.next()){if(f==null){c=void 0;break a}f=f[H.value]}c=f}else c=void 0}b=od(c,this.J,1);return b}dK.K="internal.getEventData";var eK={};eK.enableAWFledge=I(34);eK.enableAdsConversionValidation=I(18);eK.enableAdsSupernovaParams=I(30);eK.enableAutoPhoneAndAddressDetection=I(32);eK.enableAutoPiiOnPhoneAndAddress=I(33);eK.enableCachedEcommerceData=I(39);eK.enableCatchPredetectPermissionFailure=I(133);eK.enableCcdSendTo=I(40);eK.enableCloudRecommentationsErrorLogging=I(41);eK.enableCloudRecommentationsSchemaIngestion=I(42);eK.enableCloudRetailInjectPurchaseMetadata=I(44);eK.enableCloudRetailLogging=I(43);
eK.enableCloudRetailPageCategories=I(45);eK.enableDCFledge=I(55);eK.enableDataLayerSearchExperiment=I(124);eK.enableDecodeUri=I(87);eK.enableDeferAllEnhancedMeasurement=I(57);eK.enableFormSkipValidation=I(76);eK.enableGa4OutboundClicksFix=I(91);eK.enableGaAdsConversions=I(116);eK.enableGaAdsConversionsClientId=I(115);eK.enableLimitedDataModes=I(106);eK.enableMerchantRenameForBasketData=I(109);eK.enableUrlDecodeEventUsage=I(136);eK.enableZoneConfigInChildContainers=I(139);
eK.useEnableAutoEventOnFormApis=I(152);function fK(){return od(eK)}fK.K="internal.getFlags";function gK(){return new kd(fD)}gK.K="internal.getHtmlId";function hK(a){var b;return b}hK.K="internal.getIframingState";function iK(a,b){var c={};return od(c)}iK.K="internal.getLinkerValueFromLocation";function jK(){var a=new Na;return a}jK.K="internal.getPrivacyStrings";function kK(a,b){var c;if(!bh(a)||!bh(b))throw J(this.getName(),["string","string"],arguments);var d=Iv(a)||{};c=od(d[b],this.J);return c}kK.K="internal.getProductSettingsParameter";function lK(a,b){var c;if(!bh(a)||!eh(b))throw J(this.getName(),["string","boolean|undefined"],arguments);K(this,"get_url","query",a);var d=fk(lk(z.location.href),"query"),e=dk(d,a,b);c=od(e,this.J);return c}lK.publicName="getQueryParameters";function mK(a,b){var c;return c}mK.publicName="getReferrerQueryParameters";function nK(a){var b="";return b}nK.publicName="getReferrerUrl";function oK(){return Ln()}oK.K="internal.getRegionCode";function pK(a,b){var c;if(!bh(a)||!bh(b))throw J(this.getName(),["string","string"],arguments);var d=Wp(a);c=od(d[b],this.J);return c}pK.K="internal.getRemoteConfigParameter";function qK(){var a=new Na;a.set("width",0);a.set("height",0);return a}qK.K="internal.getScreenDimensions";function rK(){var a="";return a}rK.K="internal.getTopSameDomainUrl";function sK(){var a="";return a}sK.K="internal.getTopWindowUrl";function tK(a){var b="";if(!ch(a))throw J(this.getName(),["string|undefined"],arguments);K(this,"get_url",a);b=fk(lk(z.location.href),a);return b}tK.publicName="getUrl";function uK(){K(this,"get_user_agent");return gc.userAgent}uK.K="internal.getUserAgent";function vK(){var a;return a?od(Nx(a)):a}vK.K="internal.getUserAgentClientHints";var xK=function(a){var b=a.eventName===M.m.oc&&Hm()&&gx(a),c=U(a,"is_sgtm_service_worker"),d=U(a,"batch_on_navigation"),e=U(a,"is_conversion"),f=U(a,"is_session_start"),g=U(a,"create_dc_join"),k=U(a,"create_google_join"),m=!!fx(a)||!!U(a,"enhanced_match_result");return!(!Hc()&&gc.sendBeacon===void 0||e||m||f||g||k||b||c||!d&&wK)},wK=!1;
var yK=function(a){var b=0,c=0;return{start:function(){b=qb()},stop:function(){c=this.get()},get:function(){var d=0;a.si()&&(d=qb()-b);return d+c}}},zK=function(){this.C=void 0;this.H=0;this.isActive=this.isVisible=this.N=!1;this.U=this.O=void 0};h=zK.prototype;h.Ul=function(a){var b=this;if(!this.C){this.N=A.hasFocus();this.isVisible=!A.hidden;this.isActive=!0;var c=function(d,e,f){xc(d,e,function(g){b.C.stop();f(g);b.si()&&b.C.start()})};c(z,"focus",function(){b.N=!0});c(z,"blur",function(){b.N=
!1});c(z,"pageshow",function(d){b.isActive=!0;d.persisted&&O(56);b.U&&b.U()});c(z,"pagehide",function(){b.isActive=!1;b.O&&b.O()});c(A,"visibilitychange",function(){b.isVisible=!A.hidden});gx(a)&&!mc()&&c(z,"beforeunload",function(){wK=!0});this.Li(!0);this.H=0}};h.Li=function(a){if((a===void 0?0:a)||this.C)this.H+=this.Cg(),this.C=yK(this),this.si()&&this.C.start()};h.bo=function(a){var b=this.Cg();b>0&&W(a,M.m.we,b)};h.dn=function(a){W(a,M.m.we);this.Li();this.H=0};h.si=function(){return this.N&&
this.isVisible&&this.isActive};h.Tm=function(){return this.H+this.Cg()};h.Cg=function(){return this.C&&this.C.get()||0};h.Mn=function(a){this.O=a};h.Tk=function(a){this.U=a};var BK=function(a){var b=U(a,"event_usage");if(Array.isArray(b))for(var c=0;c<b.length;c++)AK(b[c]);var d=Ya("GA4_EVENT");d&&W(a,"_eu",d)},CK=function(){delete Wa.GA4_EVENT},AK=function(a){Xa("GA4_EVENT",a)};function DK(){return z.gaGlobal=z.gaGlobal||{}}function EK(){var a=DK();a.hid=a.hid||gb();return a.hid}function FK(a,b){var c=DK();if(c.vid===void 0||b&&!c.from_cookie)c.vid=a,c.from_cookie=b};
var GK=function(a,b,c){var d=U(a,"client_id_source");if(d===void 0||c<=d)W(a,M.m.mb,b),V(a,"client_id_source",c)},IK=function(a,b){var c=vu(a,M.m.mb);if(P(a.D,M.m.Yb)&&P(a.D,M.m.xc)||b&&c===b)return c;if(c){c=""+c;if(!HK(c,a))return O(31),a.isAborted=!0,"";FK(c,R(M.m.X));return c}O(32);a.isAborted=!0;return""},JK=["GA1"],KK=function(a){var b=U(a,"cookie_options"),c=b.prefix+"_ga",d=tr(c,b.domain,b.path,JK,M.m.X);if(!d){var e=String(P(a.D,M.m.sc,""));e&&e!==c&&(d=tr(e,b.domain,b.path,JK,M.m.X))}return d},
HK=function(a,b){var c;var d=U(b,"cookie_options"),e=d.prefix+"_ga",f=vr(d,void 0,void 0,M.m.X);if(P(b.D,M.m.Vb)===!1&&KK(b)===a)c=!0;else{var g=ur(a,JK[0],d.domain,d.path);c=lr(e,g,f)!==1}return c};
var NK=function(a,b,c){if(!b)return a;if(!a)return b;var d=LK(a);if(!d)return b;var e,f=lb((e=P(c.D,M.m.Jd))!=null?e:30),g=U(c,"event_start_timestamp_ms");if(!(Math.floor(g/1E3)>d.t+f*60))return a;var k=LK(b);if(!k)return a;k.o=d.o+1;var m;return(m=MK(k))!=null?m:b},QK=function(a,b){var c=U(b,"cookie_options"),d=OK(b,c);if(I(94)){var e=PK(a);if(!e)return!1;var f=vr(c||{},void 0,void 0,Gs.get(2));lr(d,void 0,f);return Js(d,e,2,c)!==1}var g=ur(a,"GS1",c.domain,c.path),k={Qb:M.m.X,domain:c.domain,path:c.path,
expires:c.Pb?new Date(qb()+Number(c.Pb)*1E3):void 0,flags:c.flags};lr(d,void 0,k);return lr(d,g,k)!==1},RK=function(a){if(I(94))return Fs(a,2);var b=[a.s,a.o,a.g,a.t,a.j];a.l!==void 0&&b.push(a.l);a.h!==void 0&&b.push(a.h);return b.join(".")},TK=function(a){return I(93)?Hs(a,2,SK):ar(a,void 0,void 0,M.m.X).map(function(b){return PK(b.split(".").slice(2).join("."))}).filter(function(b){return b!==void 0})},VK=function(a){var b=U(a,"cookie_options"),c=OK(a,b),d;if(I(93))b:{var e=SK,f=Cs[2];if(f){var g,
k=or(b.domain),m=pr(b.path),n=Object.keys(f.Kg),p=Gs.get(2),q;if(g=(q=dr(c,k,m,n,p))==null?void 0:q.xm){var r=Ds(g,2,e);d=r?Is(r):void 0;break b}}d=void 0}else{var v=tr(c,b.domain,b.path,UK,M.m.X);d=v?PK(v):void 0}if(d){var u=TK(c);if(u&&u.length>1){AK(28);var t;if(u&&u.length!==0){for(var w,x=-Infinity,y=l(u),B=y.next();!B.done;B=y.next()){var C=B.value;if(C.t!==void 0){var E=Number(C.t);!isNaN(E)&&E>x&&(x=E,w=C)}}t=w}else t=void 0;var H=t;H&&H.t!==d.t&&(AK(32),d=H)}return RK(d)}},WK=function(a){var b=
U(a,"event_start_timestamp_ms"),c={},d=(c.s=vu(a,M.m.Ib),c.o=vu(a,M.m.Me),c.g=vu(a,M.m.Le),c.t=Math.floor(b/1E3),c.d=U(a,"join_id"),c.j=U(a,"join_timer_sec")||0,c.l=!!U(a,M.m.Wf),c.h=vu(a,M.m.xe),c);return MK(d)},MK=function(a){if(a.s&&a.o){var b={},c=(b.s=a.s,b.o=String(a.o),b.g=lb(a.g)?"1":"0",b.t=String(a.t),b.j=String(a.j),b.l=a.l?"1":"0",b.h=a.h||"0",b.d=a.d,b);return RK(c)}},SK=function(a){a&&(a==="GS1"?AK(33):a==="GS2"&&AK(34))},PK=function(a){if(a){var b;if(I(93))a:{var c=(vb(a,"s")&&a.indexOf("$")!==
-1?"GS2":"GS1")+".1."+a;try{b=Ds(c,2);break a}catch(f){}b=void 0}else{var d=a.split(".");if(d.length<5||d.length>7)return;var e={};b=(e.s=d[0],e.o=d[1],e.g=d[2],e.t=d[3],e.j=d[4],e.l=d[5],e.h=d[6],e)}return b}},OK=function(a,b){return b.prefix+"_ga_"+a.target.ids[Ro[6]]},UK=["GS1","GS2"],LK=function(a){var b=PK(a);if(b){var c=Number(b.o),d=Number(b.t),e=Number(b.j||0);c||AK(29);d||AK(30);isNaN(e)&&AK(31);if(c&&d&&!isNaN(e)){var f=b.h,g=f&&f!=="0"?String(f):void 0,k=b.d?String(b.d):void 0,m={};return m.s=
String(b.s),m.o=c,m.g=!!Number(b.g),m.t=d,m.d=k,m.j=e,m.l=b.l==="1",m.h=g,m}}};
var XK=function(a){var b=P(a.D,M.m.Ba),c=a.D.H[M.m.Ba];if(c===b)return c;var d=Zc(b,null);c&&c[M.m.Z]&&(d[M.m.Z]=(d[M.m.Z]||[]).concat(c[M.m.Z]));return d},YK=function(a,b){var c=Yr(!0);return c._up!=="1"?{}:{clientId:c[a],ab:c[b]}},ZK=function(a,b,c){var d=Yr(!0),e=d[b];e&&(GK(a,e,2),HK(e,a));var f=d[c];f&&QK(f,a);return{clientId:e,ab:f}},$K=function(){var a=hk(z.location,"host"),b=hk(lk(A.referrer),"host");return a&&b?a===b||a.indexOf("."+b)>=0||b.indexOf("."+a)>=0?!0:!1:!1},aL=function(a){if(!P(a.D,
M.m.ib))return{};var b=U(a,"cookie_options"),c=b.prefix+"_ga",d=OK(a,b);fs(function(){var e;if(R("analytics_storage"))e={};else{var f={_up:"1"},g;g=vu(a,M.m.mb);e=(f[c]=g,f[d]=WK(a),f)}return e},1);return!R("analytics_storage")&&$K()?YK(c,d):{}},cL=function(a){var b=XK(a)||{},c=U(a,"cookie_options"),d=c.prefix+"_ga",e=OK(a,c),f={};hs(b[M.m.Tc],!!b[M.m.Z])&&(f=ZK(a,d,e),f.clientId&&f.ab&&(bL=!0));b[M.m.Z]&&es(function(){var g={},k=KK(a);k&&(g[d]=k);var m=VK(a);m&&(g[e]=m);var n=ar("FPLC",void 0,void 0,
M.m.X);n.length&&(g._fplc=n[0]);return g},b[M.m.Z],b[M.m.Zb],!!b[M.m.Hb]);return f},bL=!1;var dL=function(a){if(!U(a,"is_merchant_center")&&tk(a.D)){var b=XK(a)||{},c=(hs(b[M.m.Tc],!!b[M.m.Z])?Yr(!0)._fplc:void 0)||(ar("FPLC",void 0,void 0,M.m.X).length>0?void 0:"0");W(a,"_fplc",c)}};function eL(a){(gx(a)||Lj())&&W(a,M.m.Tj,Ln()||Kn());!gx(a)&&Lj()&&W(a,M.m.dk,"::")}function fL(a){if(I(79)&&Lj()){Ju(a);Ku(a,"cpf",cv(P(a.D,M.m.Ja)));var b=P(a.D,M.m.Vb);Ku(a,"cu",b===!0?1:b===!1?0:void 0);Ku(a,"cf",cv(P(a.D,M.m.Xa)));Ku(a,"cd",qr(bv(P(a.D,M.m.Ra)),bv(P(a.D,M.m.ob))))}};var hL=function(a,b){Fo("grl",function(){return gL()})(b)||(O(35),a.isAborted=!0)},gL=function(){var a=qb(),b=a+864E5,c=20,d=5E3;return function(e){var f=qb();f>=b&&(b=f+864E5,d=5E3);c=Math.min(c+(f-a)/1E3*5,20);a=f;var g=!1;d<1||c<1||(g=!0,d--,c--);e&&(e.Cm=d,e.tm=c);return g}};
var iL=function(a){var b=vu(a,M.m.Ga);return fk(lk(b),"host",!0)},jL=function(a){if(P(a.D,M.m.Fd)!==void 0)a.copyToHitData(M.m.Fd);else{var b=P(a.D,M.m.eg),c,d;a:{if(bL){var e=XK(a)||{};if(e&&e[M.m.Z])for(var f=iL(a),g=e[M.m.Z],k=0;k<g.length;k++)if(g[k]instanceof RegExp){if(g[k].test(f)){d=!0;break a}}else if(f.indexOf(g[k])>=0){d=!0;break a}}d=!1}if(!(c=d)){var m;if(m=b)a:{for(var n=b.include_conditions||[],p=iL(a),q=0;q<n.length;q++)if(n[q].test(p)){m=!0;break a}m=!1}c=m}c&&(W(a,M.m.Fd,"1"),AK(4))}};

var kL=function(a,b){Gq()&&(a.gcs=Hq(),U(b,"is_consent_update")&&(a.gcu="1"));a.gcd=Lq(b.D);I(92)?a.npa=U(b,"allow_ad_personalization")?"0":"1":Fq(b.D)?a.npa="0":a.npa="1";Qq()&&(a._ng="1")},lL=function(a){return I(100)&&R([M.m.X,M.m.R])?Lj()&&U(a,"is_google_signals_allowed"):!1},pL=function(a){if(U(a,"is_merchant_center"))return{url:uk("https://www.merchant-center-analytics.goog")+"/mc/collect",endpoint:20};var b=qk(tk(a.D),"/g/collect");if(b)return{url:b,endpoint:16};var c=hx(a),d=P(a.D,M.m.fb),
e=c&&!Mn()&&d!==!1&&FI()&&R(M.m.R)&&R(M.m.X);return Lj()?I(100)&&e?{url:(mL?On():"").toLowerCase()==="region1"?""+Kj()+"/r1ag/g/c":""+Kj()+"/ag/g/c",endpoint:17}:{url:""+Kj()+(I(15)?"/ga/g/c":"/g/collect"),endpoint:16}:e?{url:nL(),endpoint:17}:{url:oL(),endpoint:16}},nL=function(){var a;mL&&On()!==""&&(a=On());return"https://"+(a?a+".":"")+"analytics.google.com/g/collect"},oL=function(){var a="www";mL&&On()&&(a=On());return"https://"+a+".google-analytics.com/g/collect"},mL=!1;
mL=!0;var qL={};qL[M.m.mb]="cid";qL[M.m.Vg]="gcut";qL[M.m.rc]="are";qL[M.m.Sf]="pscdl";qL[M.m.Xf]="_fid";qL[M.m.ph]="_geo";qL[M.m.qb]="gdid";qL[M.m.Qc]="_ng";qL[M.m.Xb]="frm";qL[M.m.Fd]="ir";qL[M.m.Ya]="ul";qL[M.m.Je]="ni";qL[M.m.zh]="pae";qL[M.m.Ke]="_rdi";qL[M.m.ac]="sr";qL[M.m.Rj]="tid";qL[M.m.ig]="tt";qL[M.m.sb]="ec_mode";qL[M.m.ek]="gtm_up";
qL[M.m.Oe]="uaa";qL[M.m.Pe]="uab";qL[M.m.Qe]="uafvl";qL[M.m.Re]="uamb";qL[M.m.Se]="uam";qL[M.m.Te]="uap";qL[M.m.Ue]="uapv";qL[M.m.Ve]="uaw";qL[M.m.Tj]="ur";qL[M.m.dk]="_uip";qL[M.m.Sc]="lps";qL[M.m.ld]=
"gclgs",qL[M.m.nd]="gclst",qL[M.m.md]="gcllp";var rL={};rL[M.m.pd]="cc";rL[M.m.rd]="ci";rL[M.m.sd]="cm";rL[M.m.ud]="cn";rL[M.m.wd]="cs";rL[M.m.xd]="ck";rL[M.m.Fa]="cu";rL[M.m.Ge]="_tu";rL[M.m.oa]="dl";rL[M.m.Ga]="dr";rL[M.m.hb]="dt";rL[M.m.Le]="seg";rL[M.m.Ib]="sid";rL[M.m.Me]="sct";rL[M.m.Ca]="uid";I(141)&&(rL[M.m.Hd]="dp");var sL={};sL[M.m.we]="_et";sL[M.m.pb]="edid";I(89)&&(sL._eu="_eu");var tL={};tL[M.m.pd]="cc";tL[M.m.rd]="ci";tL[M.m.sd]="cm";tL[M.m.ud]="cn";tL[M.m.wd]="cs";tL[M.m.xd]="ck";var uL={},vL=(uL[M.m.Ia]=1,uL),wL=function(a,b,c){function d(F,S){if(S!==void 0&&!Sh.hasOwnProperty(F)){S===null&&(S="");var ba;var ha=S;F!==M.m.xe?ba=!1:U(a,"euid_mode_enabled")||gx(a)?(e.ecid=ha,ba=!0):ba=void 0;if(!ba&&F!==M.m.Wf){var Y=S;S===!0&&(Y="1");S===!1&&(Y="0");Y=String(Y);var Q;if(qL[F])Q=qL[F],e[Q]=Y;else if(rL[F])Q=
rL[F],g[Q]=Y;else if(sL[F])Q=sL[F],f[Q]=Y;else if(F.charAt(0)==="_")e[F]=Y;else{var ia;tL[F]?ia=!0:F!==M.m.vd?ia=!1:(typeof S!=="object"&&B(F,S),ia=!0);ia||B(F,S)}}}}var e={},f={},g={};e.v="2";e.tid=a.target.destinationId;e.gtm=Tq({Da:U(a,"source_canonical_id")});e._p=I(155)?Ej:EK();if(c&&(c.La||c.mi)&&(I(120)||(e.em=c.Za),c.ya)){var k=c.ya.Qd;k&&!I(9)&&(k=k.replace(/./g,"*"));k&&(e.eme=k);e._es=c.ya.status;c.ya.time!==void 0&&(e._est=c.ya.time)}U(a,"create_google_join")&&(e._gaz=1);kL(e,a);Oq()&&
(e.dma_cps=Mq());e.dma=Nq();kq(sq())&&(e.tcfd=Pq());Jj()&&(e.tag_exp=Jj());var m=vu(a,M.m.qb);m&&(e.gdid=m);f.en=String(a.eventName);if(U(a,"is_first_visit")){var n=U(a,"is_first_visit_conversion");f._fv=n?2:1}U(a,"is_new_to_site")&&(f._nsi=1);if(U(a,"is_session_start")){var p=U(a,"is_session_start_conversion");f._ss=p?2:1}U(a,"is_conversion")&&(f._c=1);U(a,"is_external_event")&&(f._ee=1);if(U(a,"is_ecommerce")){var q=vu(a,M.m.ja)||P(a.D,M.m.ja);if(Array.isArray(q))for(var r=0;r<q.length&&r<200;r++)f["pr"+
(r+1)]=gg(q[r])}var v=vu(a,M.m.pb);v&&(f.edid=v);var u=vu(a,M.m.Pc);if(u&&typeof u==="object")for(var t=l(Object.keys(u)),w=t.next();!w.done;w=t.next()){var x=w.value,y=u[x];y!==void 0&&(y===null&&(y=""),f["gap."+x]=String(y))}for(var B=function(F,S){if(typeof S!=="object"||!vL[F]){var ba="ep."+F,ha="epn."+F;F=db(S)?ha:ba;var Y=db(S)?ba:ha;f.hasOwnProperty(Y)&&delete f[Y];f[F]=String(S)}},C=l(Object.keys(a.C)),E=C.next();!E.done;E=C.next()){var H=E.value;d(H,vu(a,H))}(function(F){gx(a)&&typeof F===
"object"&&jb(F||{},function(S,ba){typeof ba!=="object"&&(e["sst."+S]=String(ba))})})(vu(a,M.m.bf));Lo(e,vu(a,M.m.Md));var N=vu(a,M.m.tb)||{};I(104)&&P(a.D,M.m.fb,void 0,4)===!1&&(e.ngs="1");jb(N,function(F,S){S!==void 0&&((S===null&&(S=""),F!==M.m.Ca||g.uid)?b[F]!==S&&(f[(db(S)?"upn.":"up.")+String(F)]=String(S),b[F]=S):g.uid=String(S))});if(lL(a)){var L=U(a,"join_id");L?e._gsid=L:e.njid="1"}var T=pL(a);wg.call(this,{fa:e,hd:g,ii:f},T.url,T.endpoint,gx(a),void 0,a.target.destinationId,a.D.eventId,
a.D.priorityId)};ra(wL,wg);
var xL=function(a,b){return a.replace(/\$\{([^\}]+)\}/g,function(c,d){return b[d]||c})},yL=function(a){var b={},c="",d=a.pathname.indexOf("/g/collect");d>=0&&(c=a.pathname.substring(0,d));b.transport_url=a.protocol+"//"+a.hostname+c;return b},zL=function(a,b,c,d,e){var f=0,g=new z.XMLHttpRequest;g.withCredentials=!0;g.onprogress=function(k){if(g.status===200){var m=g.responseText.substring(f);f=k.loaded;xz(c,m)}};g.onerror=function(){e==null||e()};g.onload=function(){g.status<=399||e==null||e()};
g.open(b?"POST":"GET",a);(d==null?0:d.attributionReporting)&&g.setAttributionReporting&&g.setAttributionReporting(d.attributionReporting);g.send(b)},BL=function(a,b,c){var d;return d=Az(zz(new yz(function(e,f){var g=xL(e,b);c&&(g=g.replace("_is_sw=0",c));var k={};f.attribution_reporting&&(k.attributionsrc="");El(a,g,void 0,Cz(d,f),k)}),function(e,f){var g=xL(e,b),k=f.dedupe_key;k&&Jl(a,g,k)}),function(e,f){var g=xL(e,b);c&&(g=g.replace("_is_sw=0",c));var k={};f.attribution_reporting&&(k.attributionReporting=
{eventSourceEligible:!1,triggerEligible:!0});f.process_response?AL(a,g,void 0,d,k,Cz(d,f)):Fl(a,g,void 0,k,void 0,Cz(d,f))})},CL=function(a,b,c,d,e){yl(a,2,b);var f=BL(a,d,e);AL(a,b,c,f)},AL=function(a,b,c,d,e,f){Hc()?wz(a,b,c,d,e,void 0,f):zL(b,c,d,(e==null?0:e.attributionReporting)?{attributionReporting:e.attributionReporting}:{},f)},DL=function(a,b,c){var d=lk(b),e=yL(d),f=Ez(d),g=I(127);I(12)&&(g=g&&!(lc("; wv")||lc("FBAN")||lc("FBAV")||nc()));g?Gx(f,c,e,function(k){CL(a,f,c,e,k)}):CL(a,f,c,e)};
var EL={AW:wn.ol,G:wn.Rl,DC:wn.Ql};function FL(a){var b=Gi(a);return""+Uq(b.map(function(c){return c.value}).join("!"))}function GL(a){var b=Oo(a);return b&&EL[b.prefix]}function HL(a,b){var c=a[b];c&&(c.clearTimerId&&z.clearTimeout(c.clearTimerId),c.clearTimerId=z.setTimeout(function(){delete a[b]},36E5))};
var IL=function(a,b,c,d){var e=a+"?"+b;d?Dl(c,e,d):Cl(c,e)},KL=function(a,b,c,d,e){var f=b,g=Kc();g!==void 0&&(f+="&tfd="+Math.round(g));b=f;var k=a+"?"+b;JL&&(d=!vb(k,oL())&&!vb(k,nL()));if(d&&!wK)DL(e,k,c);else{var m=b;Hc()?Fl(e,a+"?"+m,c,{yi:!0})||IL(a,m,e,c):IL(a,m,e,c)}},LL=function(a,b){function c(x){q.push(x+"="+encodeURIComponent(""+a.fa[x]))}var d=b.Rn,e=b.Tn,f=b.Sn,g=b.Vm,k=b.qn,m=b.pn,n=b.Ln,p=b.Mm;if(d||e||f){var q=[];a.fa._ng&&c("_ng");c("tid");c("cid");c("gtm");q.push("aip=1");a.hd.uid&&
!m&&q.push("uid="+encodeURIComponent(""+a.hd.uid));var r=function(){c("dma");a.fa.dma_cps!=null&&c("dma_cps");a.fa.gcs!=null&&c("gcs");c("gcd");a.fa.npa!=null&&c("npa")};r();a.fa.frm!=null&&c("frm");d&&(Jj()&&q.push("tag_exp="+Jj()),IL("https://stats.g.doubleclick.net/g/collect","v=2&"+q.join("&"),{destinationId:a.destinationId||"",endpoint:19,eventId:a.eventId,priorityId:a.priorityId}),ho({targetId:String(a.fa.tid),request:{url:"https://stats.g.doubleclick.net/g/collect?v=2&"+q.join("&"),parameterEncoding:2,
endpoint:19},Ka:b.Ka}));if(e){var v=function(){var x=pl()+"/td/ga/rul?";q=[];c("tid");q.push("gacid="+encodeURIComponent(String(a.fa.cid)));c("gtm");r();c("pscdl");a.fa._ng!=null&&c("_ng");q.push("aip=1");q.push("fledge=1");a.fa.frm!=null&&c("frm");Jj()&&q.push("tag_exp="+Jj());q.push("z="+gb());var y=x+q.join("&");Jl({destinationId:a.destinationId||"",endpoint:42,eventId:a.eventId,priorityId:a.priorityId},y,a.fa.tid);ho({targetId:String(a.fa.tid),request:{url:y,parameterEncoding:2,endpoint:42},Ka:b.Ka})};
Jj()&&q.push("tag_exp="+Jj());q.push("z="+gb());if(!k){var u=g&&vb(g,"google.")&&g!=="google.com"?"https://www.%/ads/ga-audiences?v=1&t=sr&slf_rd=1&_r=4&".replace("%",g):void 0;if(u){var t=u+q.join("&");El({destinationId:a.destinationId||"",endpoint:47,eventId:a.eventId,priorityId:a.priorityId},t);ho({targetId:String(a.fa.tid),request:{url:t,parameterEncoding:2,endpoint:47},Ka:b.Ka})}}I(104)&&n&&!wK&&v()}if(f&&I(100)){var w="https://{ga4CollectionSubdomain.}analytics.google.com/g/s/collect".replace("{ga4CollectionSubdomain.}",
p===""?p:p+".");q=[];c("_gsid");c("gtm");IL(w,q.join("&"),{destinationId:a.destinationId||"",endpoint:18,eventId:a.eventId,priorityId:a.priorityId});ho({targetId:String(a.fa.tid),request:{url:w+"?"+q.join("&"),parameterEncoding:2,endpoint:18},Ka:b.Ka})}}},JL=!1;var ML=function(){this.N=1;this.O={};this.H=-1;this.C=new pg};h=ML.prototype;h.xb=function(a,b){var c=
this,d=new wL(a,this.O,b),e={eventId:a.D.eventId,priorityId:a.D.priorityId},f=xK(a),g,k;f&&this.C.U(d)||this.flush();var m=f&&this.C.add(d);if(m){if(this.H<0){var n=z.setTimeout,p;gx(a)?NL?(NL=!1,p=OL):p=PL:p=5E3;this.H=n.call(z,function(){c.flush()},p)}}else{var q=sg(d,this.N++),r=q.params,v=q.body;g=r;k=v;KL(d.baseUrl,r,v,d.N,{destinationId:a.target.destinationId,endpoint:d.endpoint,eventId:d.eventId,priorityId:d.priorityId});var u=U(a,"create_dc_join"),t=U(a,"create_google_join"),w=U(a,"create_fpm_join"),
x=P(a.D,M.m.Ea)!==!1,y=Fq(a.D),B=vu(a,M.m.zh),C={Rn:u,Tn:t,Sn:w,Vm:Qn(),Eo:x,Do:y,qn:Mn(),pn:U(a,"euid_mode_enabled"),Ka:e,Ln:B,D:a.D,Mm:On()};LL(d,C)}lz(a.D.eventId);io(function(){if(m){var E=sg(d),H=E.body;g=E.params;k=H}return{targetId:a.target.destinationId,request:{url:d.baseUrl+"?"+g,parameterEncoding:2,postBody:k,endpoint:d.endpoint},Ka:e,isBatched:!1}})};h.add=function(a){if(I(95)){var b=U(a,"enhanced_match_result");if(b){W(a,M.m.sb,U(a,"user_data_mode"));W(a,M.m.Je,"1");this.xb(a,b);return}}var c=
fx(a);if(I(95)&&c){var d;var e=a.target.destinationId,f;var g=c,k=GL(e);if(k){var m=FL(g);f=(Bn(k)||{})[m]}else f=void 0;var n=f;d=n?n.sentTo[e]:void 0;if(d&&d+6E4>qb())c=void 0,W(a,M.m.sb);else{var p=c,q=a.target.destinationId,r=GL(q);if(r){var v=FL(p),u=Bn(r)||{},t=u[v];if(t)t.timestamp=qb(),t.sentTo=t.sentTo||{},t.sentTo[q]=qb(),t.pending=!0;else{var w={};u[v]={pending:!0,timestamp:qb(),sentTo:(w[q]=qb(),w)}}HL(u,v);An(r,u)}}}!c||wK||I(120)&&!I(88)?this.xb(a):this.Un(a)};h.flush=function(){if(this.C.events.length){var a=
ug(this.C,this.N++);KL(this.C.baseUrl,a.params,a.body,this.C.H,{destinationId:this.C.destinationId||"",endpoint:this.C.endpoint,eventId:this.C.ba,priorityId:this.C.sa});this.C=new pg;this.H>=0&&(z.clearTimeout(this.H),this.H=-1)}};h.Ck=function(a,b){var c=vu(a,M.m.sb);W(a,M.m.sb);b.then(function(d){var e={},f=(e.enhanced_match_result=d,e.user_data_mode=c,e),g=yv(a.target.destinationId,M.m.Jc,a.D.C);Bv(g,a.D.eventId,{eventMetadata:f})})};h.Un=function(a){var b=this,c=fx(a);if(aj(c)){var d=Si(c,I(88));
d?I(95)?(this.Ck(a,d),this.xb(a)):d.then(function(g){b.xb(a,g)},function(){b.xb(a)}):this.xb(a)}else{var e=$i(c);if(I(88)){var f=Oi(e);f?I(95)?(this.Ck(a,f),this.xb(a)):f.then(function(g){b.xb(a,g)},function(){b.xb(a,e)}):this.xb(a,e)}else this.xb(a,e)}};var OL=hg('',500),PL=hg('',5E3),NL=!0;
var QL=function(a,b,c){c===void 0&&(c={});if(b==null)return c;if(typeof b==="object")for(var d=l(Object.keys(b)),e=d.next();!e.done;e=d.next()){var f=e.value;QL(a+"."+f,b[f],c)}else c[a]=b;return c},RL=function(a){for(var b={},c=l(a),d=c.next();!d.done;d=c.next()){var e=d.value;b[e]=!!R(e)}return b},TL=function(a,b){var c=SL.filter(function(e){return!R(e)});if(c.length){var d=RL(c);vo(c,function(){for(var e=RL(c),f=[],g=l(c),k=g.next();!k.done;k=g.next()){var m=k.value;!d[m]&&e[m]&&f.push(m);e[m]&&
(d[m]=!0)}if(f.length){V(b,"is_consent_update",!0);var n=f.map(function(p){return bi[p]}).join(".");n&&cx(b,"gcut",n);a(b)}})}},UL=function(a){I(147)&&gx(a)&&cx(a,"navt",Lc())},VL=function(a){I(146)&&gx(a)&&cx(a,"lpc",Ns())},WL=function(a){if(I(148)&&gx(a)){var b=P(a.D,M.m.rb),c;b===!0&&(c="1");b===!1&&(c="0");c&&cx(a,"rdp",c)}},XL=function(a){I(143)&&gx(a)&&P(a.D,M.m.ne,!0)===!1&&W(a,M.m.ne,0)},YL=function(a,b){if(gx(b)){var c=U(b,"is_conversion");(b.eventName==="page_view"||c)&&TL(a,b)}},ZL=function(a){if(gx(a)&&
a.eventName===M.m.Jc&&U(a,"is_consent_update")){var b=vu(a,M.m.Vg);b&&(cx(a,"gcut",b),cx(a,"syn",1))}},$L=function(a){I(145)&&gx(a)&&P(a.D,M.m.Ea)!==!1&&ll("join-ad-interest-group")&&ab(gc.joinAdInterestGroup)&&cx(a,"flg",1)},aM=function(a){gx(a)&&V(a,"speculative",!1)},bM=function(a){gx(a)&&(U(a,"speculative")&&cx(a,"sp",1),U(a,"is_syn")&&cx(a,"syn",1),U(a,"em_event")&&(cx(a,"em_event",1),cx(a,"sp",1)))},cM=function(a){if(gx(a)){var b=Ej;b&&cx(a,"tft",Number(b))}},dM=function(a){function b(e){var f=
QL(M.m.Ia,e);jb(f,function(g,k){W(a,g,k)})}if(gx(a)){var c=dx(a,"ccd_add_1p_data",!1)?1:0;cx(a,"ude",c);var d=P(a.D,M.m.Ia);d!==void 0?(b(d),W(a,M.m.sb,"c")):b(U(a,"user_data"));V(a,"user_data")}},eM=function(a){if(gx(a)){var b=su();b&&cx(a,"us_privacy",b);var c=zq();c&&cx(a,"gdpr",c);var d=yq();d&&cx(a,"gdpr_consent",d)}},fM=function(a){gx(a)&&Hm()&&P(a.D,M.m.ka)&&cx(a,"adr",1)},gM=function(a){if(gx(a)){var b=mL?On():"";b&&cx(a,"gcsub",b)}},hM=function(a){if(gx(a)){P(a.D,M.m.fb,void 0,4)===!1&&cx(a,
"ngs",1);Mn()&&cx(a,"ga_rd",1);FI()||cx(a,"ngst",1);var b=Qn();b&&cx(a,"etld",b)}},iM=function(a){},jM=function(a){gx(a)&&Hm()&&cx(a,"rnd",$t())},SL=[M.m.R,M.m.T];
var kM=function(a,b){var c;a:{var d=WK(a);if(d){if(QK(d,a)){c=d;break a}O(25);a.isAborted=!0}c=void 0}var e=c;return{clientId:IK(a,b),ab:e}},lM=function(a,b,c,d,e){var f=bv(P(a.D,M.m.mb));if(P(a.D,M.m.Yb)&&P(a.D,M.m.xc))f?GK(a,f,1):(O(127),a.isAborted=!0);else{var g=f?1:8;V(a,"is_new_to_site",!1);f||(f=KK(a),g=3);f||(f=b,g=5);if(!f){var k=R(M.m.X),m=DK();f=!m.from_cookie||k?m.vid:void 0;g=6}f?f=""+f:(f=sr(),g=7,V(a,"is_first_visit",!0),V(a,"is_new_to_site",!0));GK(a,f,g)}var n;n=U(a,"event_start_timestamp_ms");
var p=Math.floor(n/1E3),q=void 0;U(a,"is_new_to_site")||(q=VK(a)||c);var r=lb(P(a.D,M.m.Jd,30));r=Math.min(475,r);r=Math.max(5,r);var v=lb(P(a.D,M.m.gg,1E4)),u=LK(q);V(a,"is_first_visit",!1);V(a,"is_session_start",!1);V(a,"join_timer_sec",0);u&&u.j&&V(a,"join_timer_sec",Math.max(0,u.j-Math.max(0,p-u.t)));var t=!1;if(!u){V(a,"is_first_visit",!0);t=!0;var w={};u=(w.s=String(p),w.o=1,w.g=!1,w.t=p,w.l=!1,w.h=void 0,w)}p>u.t+r*60&&(t=!0,u.s=String(p),u.o++,u.g=!1,u.h=void 0);if(t)V(a,"is_session_start",
!0),d.dn(a);else if(d.Tm()>v||a.eventName===M.m.oc)u.g=!0;U(a,"euid_mode_enabled")?P(a.D,M.m.Ca)?u.l=!0:(u.l&&!I(10)&&(u.h=void 0),u.l=!1):u.l=!1;var x=u.h;if(U(a,"euid_mode_enabled")||gx(a)){var y=P(a.D,M.m.xe),B=y?1:8;y||(y=x,B=4);y||(y=rr(),B=7);var C=y.toString(),E=B,H=U(a,"enhanced_client_id_source");if(H===void 0||E<=H)W(a,M.m.xe,C),V(a,"enhanced_client_id_source",E)}e?(a.copyToHitData(M.m.Ib,u.s),a.copyToHitData(M.m.Me,u.o),a.copyToHitData(M.m.Le,u.g?1:0)):(W(a,M.m.Ib,u.s),W(a,M.m.Me,u.o),
W(a,M.m.Le,u.g?1:0));V(a,M.m.Wf,u.l?1:0);var N=u;if(U(a,"is_google_signals_allowed")){var L=N.d;if(I(100)){var T=z.crypto||z.msCrypto,F;if(!(F=L))a:{if(T&&T.getRandomValues)try{var S=new Uint8Array(25);T.getRandomValues(S);F=btoa(String.fromCharCode.apply(String,ta(S))).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"");break a}catch(ba){}F=void 0}L=F}V(a,"join_id",L)}};var mM=window,nM=document,oM=function(a){var b=mM._gaUserPrefs;if(b&&b.ioo&&b.ioo()||nM.documentElement.hasAttribute("data-google-analytics-opt-out")||a&&mM["ga-disable-"+a]===!0)return!0;try{var c=mM.external;if(c&&c._gaUserPrefs&&c._gaUserPrefs=="oo")return!0}catch(p){}for(var d=[],e=String(nM.cookie).split(";"),f=0;f<e.length;f++){var g=e[f].split("="),k=g[0].replace(/^\s*|\s*$/g,"");if(k&&k=="AMP_TOKEN"){var m;(m=g.slice(1).join("=").replace(/^\s*|\s*$/g,""))&&(m=decodeURIComponent(m));d.push(m)}}for(var n=
0;n<d.length;n++)if(d[n]=="$OPT_OUT")return!0;return nM.getElementById("__gaOptOutExtension")?!0:!1};
var qM=function(a){return!a||pM.test(a)||Uh.hasOwnProperty(a)},rM=function(a){var b=M.m.ac,c;c||(c=function(){});vu(a,b)!==void 0&&W(a,b,c(vu(a,b)))},sM=function(a){var b=a.indexOf("?"),c=b===-1?a:a.substring(0,b);try{c=decodeURIComponent(c)}catch(d){}return b===-1?c:""+c+a.substring(b)},tM=function(a){P(a.D,M.m.ib)&&(R(M.m.X)||P(a.D,M.m.mb)||W(a,M.m.ek,!0));var b;var c;c=c===void 0?3:c;var d=z.location.href;if(d){var e=lk(d).search.replace("?",""),f=dk(e,"_gl",!1,!0)||"";b=f?Zr(f,c)!==void 0:!1}else b=
!1;b&&gx(a)&&cx(a,"glv",1);if(a.eventName!==M.m.ia)return{};P(a.D,M.m.ib)&&Ct(["aw","dc"]);Et(["aw","dc"]);var g=cL(a),k=aL(a);return Object.keys(g).length?g:k},uM=function(a){var b=zb(jp(a.D,M.m.la,1),".");b&&W(a,M.m.qb,b);var c=zb(jp(a.D,M.m.la,2),".");c&&W(a,M.m.pb,c)},Px={Im:"",eo:Number("")},vM={},wM=(vM[M.m.pd]=1,vM[M.m.rd]=1,vM[M.m.sd]=1,vM[M.m.ud]=1,vM[M.m.wd]=1,vM[M.m.xd]=1,vM),pM=/^(_|ga_|google_|gtag\.|firebase_).*$/,xM=[Lu,xu,
uM,qv],yM=function(a){this.N=a;this.C=this.ab=this.clientId=void 0;this.sa=this.U=!1;this.jb=0;this.O=!1;this.ba=new ML;this.H=new zK};h=yM.prototype;h.Jn=function(a,b,c){var d=this,e=Oo(this.N);if(e)if(c.eventMetadata.is_external_event&&a.charAt(0)==="_")c.onFailure();else{a!==M.m.ia&&a!==M.m.eb&&qM(a)&&O(58);zM(c.C);var f=new hG(e,a,c);V(f,"event_start_timestamp_ms",b);var g=[M.m.X],k=gx(f);V(f,"is_server_side_destination",k);if(dx(f,M.m.Rc,P(f.D,M.m.Rc))||k)g.push(M.m.R),g.push(M.m.T);Qx(function(){xo(function(){d.Kn(f)},
g)});I(83)&&a===M.m.ia&&dx(f,"ga4_ads_linked",!1)&&(I(106)?Tm(Vm(1),function(){d.Qk(a,c,f)}):this.Qk(a,c,f))}else c.onFailure()};h.Qk=function(a,b,c){function d(){for(var k=l(xM),m=k.next();!m.done;m=k.next()){var n=m.value;n(f);if(f.isAborted)break}U(f,"speculative")||f.isAborted||Oy(f)}var e=Oo(this.N),f=new hG(e,a,b);V(f,"hit_type","page_view");V(f,"speculative",!0);V(f,"is_server_side_destination",U(c,"is_server_side_destination"));var g=[M.m.R,M.m.T];xo(function(){d();R(g)||wo(function(k){var m,
n;m=k.consentEventId;n=k.consentPriorityId;V(f,"consent_updated",!0);V(f,"consent_event_id",m);V(f,"consent_priority_id",n);d()},g)},g)};h.Kn=function(a){var b=this;this.C=a;try{AM(a);BM(a);CM(a);DM(a);I(135)&&(a.isAborted=!0);Fu(a);var c={};hL(a,c);if(a.isAborted){a.D.onFailure();CK();return}var d=c.tm;c.Cm===0&&AK(25);d===0&&AK(26);I(106)&&V(a,"transmission_type",2);EM(a);FM(a);this.Vl(a);this.H.bo(a);GM(a);HM(a);IM(a);JM(a);this.Sk(tM(a));var e=a.eventName===M.m.ia;e&&(this.O=!0);KM(a);e&&!a.isAborted&&
this.jb++>0&&AK(17);LM(a);MM(a);lM(a,this.clientId,this.ab,this.H,!this.sa);NM(a);OM(a);PM(a);QM(a);RM(a);SM(a);TM(a);dL(a);jL(a);jM(a);iM(a);hM(a);gM(a);fM(a);eM(a);cM(a);bM(a);$L(a);ZL(a);XL(a);WL(a);VL(a);UL(a);eL(a);fL(a);UM(a);VM(a);WM(a);XM(a);Hu(a);Gu(a);Nu(a);YM(a);ZM(a);qv(a);$M(a);dM(a);aM(a);aN(a);!this.O&&U(a,"em_event")&&AK(18);BK(a);if(U(a,"speculative")||a.isAborted){a.D.onFailure();CK();return}this.Sk(kM(a,this.clientId));this.sa=!0;this.Xn(a);bN(a);YL(function(f){b.vk(f)},a);this.H.Li();
cN(a);Mu(a);if(a.isAborted){a.D.onFailure();CK();return}this.vk(a);a.D.onSuccess()}catch(f){a.D.onFailure()}CK()};h.vk=function(a){this.ba.add(a)};h.Sk=function(a){var b=a.clientId,c=a.ab;b&&c&&(this.clientId=b,this.ab=c)};h.flush=function(){this.ba.flush()};h.Xn=function(a){var b=this;if(!this.U){var c=R(M.m.T),d=R(M.m.X);vo([M.m.T,M.m.X],function(){var e=R(M.m.T),f=R(M.m.X),g=!1,k={},m={};if(d!==f&&b.C&&b.ab&&b.clientId){var n=b.clientId,p;var q=LK(b.ab);p=q?q.h:void 0;if(f){var r=KK(b.C);if(r){b.clientId=
r;var v=VK(b.C);v&&(b.ab=NK(v,b.ab,b.C))}else HK(b.clientId,b.C),FK(b.clientId,!0);QK(b.ab,b.C);g=!0;k[M.m.oh]=n;I(69)&&p&&(k[M.m.Ll]=p)}else b.ab=void 0,b.clientId=void 0,z.gaGlobal={}}e&&!c&&(g=!0,m.is_consent_update=!0,k[M.m.Vg]=bi[M.m.T]);if(g){var u=yv(b.N,M.m.Jc,k);Bv(u,a.D.eventId,{eventMetadata:m})}d=f;c=e});this.U=!0}};h.Vl=function(a){a.eventName!==M.m.eb&&this.H.Ul(a)};var CM=function(a){var b=A.location.protocol;b!=="http:"&&b!=="https:"&&(O(29),a.isAborted=!0)},DM=function(a){gc&&gc.loadPurpose===
"preview"&&(O(30),a.isAborted=!0)},EM=function(a){var b={prefix:String(P(a.D,M.m.Ja,"")),path:String(P(a.D,M.m.ob,"/")),flags:String(P(a.D,M.m.Xa,"")),domain:String(P(a.D,M.m.Ra,"auto")),Pb:Number(P(a.D,M.m.Sa,63072E3))};V(a,"cookie_options",b)},GM=function(a){U(a,"is_merchant_center")?V(a,"euid_mode_enabled",!1):I(118)?dx(a,"ccd_add_ec_stitching",!1)&&V(a,"euid_mode_enabled",!0):(dx(a,"ccd_add_1p_data",!1)||dx(a,"ccd_add_ec_stitching",!1))&&V(a,"euid_mode_enabled",!0)},HM=function(a){if(U(a,"euid_mode_enabled")&&
dx(a,"ccd_add_1p_data",!1)){var b=a.D.H[M.m.We];if(ak(b)){var c=P(a.D,M.m.Ia);c===null?V(a,"user_data_from_code",null):(b.enable_code&&Yc(c)&&V(a,"user_data_from_code",c),Yc(b.selectors)&&!U(a,"user_data_from_manual")&&V(a,"user_data_from_manual",Yj(b.selectors)))}}},IM=function(a){if(I(86)&&!I(83)&&dx(a,"ga4_ads_linked",!1)&&a.eventName===M.m.ia){var b=P(a.D,M.m.Aa)!==!1;if(b){var c=tu(a);c.Pb&&(c.Pb=Math.min(c.Pb,7776E3));var d=$u(P(a.D,M.m.Ba)),e;e=!!P(a.D,M.m.ib);uu({Pd:b,Xd:d,be:e,Dc:c})}}},
JM=function(a){if(I(92)){var b=Fq(a.D);P(a.D,M.m.rb)===!0&&(b=!1);V(a,"allow_ad_personalization",b)}},UM=function(a){if(!Lx(z))O(87);else if(Rx!==void 0){O(85);var b=Jx();b?P(a.D,M.m.Ke)&&!gx(a)||Ox(b,a):O(86)}},KM=function(a){a.eventName===M.m.ia&&(P(a.D,M.m.Ta,!0)?(a.D.C[M.m.la]&&(a.D.N[M.m.la]=a.D.C[M.m.la],a.D.C[M.m.la]=void 0,W(a,M.m.la)),a.eventName=M.m.oc):a.isAborted=!0)},FM=function(a){function b(c,d){Sh[c]||d===void 0||W(a,c,d)}jb(a.D.N,b);jb(a.D.C,b)},NM=function(a){var b=kp(a.D),c=function(d,
e){wM[d]&&W(a,d,e)};Yc(b[M.m.vd])?jb(b[M.m.vd],function(d,e){c((M.m.vd+"_"+d).toLowerCase(),e)}):jb(b,c)},LM=uM,bN=function(a){if(I(127)&&gx(a)&&!(I(12)&&gx(a)&&(lc("; wv")||lc("FBAN")||lc("FBAV")||nc()))&&R(M.m.X)){V(a,"is_sgtm_service_worker",!0);gx(a)&&cx(a,"sw_exp",1);a:{if(!I(127)||!gx(a))break a;var b=qk(tk(a.D),"/_/service_worker");Dx(b,Math.round(qb()));}}},YM=function(a){if(a.eventName===M.m.eb){var b=
P(a.D,M.m.Gb),c=P(a.D,M.m.Wb),d;d=vu(a,b);c(d||P(a.D,b));a.isAborted=!0}},OM=function(a){if(!P(a.D,M.m.xc)||!P(a.D,M.m.Yb)){var b=a.copyToHitData,c=M.m.oa,d="",e=A.location;if(e){var f=e.pathname||"";f.charAt(0)!=="/"&&(f="/"+f);var g=e.search||"";if(g&&g[0]==="?")for(var k=g.substring(1).split("&"),m=0;m<k.length;++m){var n=k[m].split("=");n&&n.length===2&&n[0]==="wbraid"&&(g=g.replace(/([?&])wbraid=[^&]+/,"$1wbraid="+Bb(n[1])))}d=e.protocol+"//"+e.hostname+f+g}b.call(a,c,d,sM);var p=a.copyToHitData,
q=M.m.Ga,r;a:{var v=ar("_opt_expid",void 0,void 0,M.m.X)[0];if(v){var u=decodeURIComponent(v).split("$");if(u.length===3){r=u[2];break a}}var t=Eo.ga4_referrer_override;if(t!==void 0)r=t;else{var w=Rj("gtm.gtagReferrer."+a.target.destinationId),x=A.referrer;r=w?""+w:x}}p.call(a,q,r||void 0,sM);a.copyToHitData(M.m.hb,A.title);a.copyToHitData(M.m.Ya,(gc.language||"").toLowerCase());var y=Jv();a.copyToHitData(M.m.ac,y.width+"x"+y.height);I(141)&&a.copyToHitData(M.m.Hd,void 0,sM);I(98)&&cu()&&a.copyToHitData(M.m.Sc,
"1")}},QM=function(a){V(a,"create_dc_join",!1);V(a,"create_google_join",!1);V(a,"create_fpm_join",!1);if(!(Lj()&&!I(100)||gx(a)||U(a,"is_merchant_center")||P(a.D,M.m.fb)===!1)&&FI()&&R(M.m.R)){var b=hx(a);(U(a,"is_session_start")||P(a.D,M.m.oh))&&V(a,"create_dc_join",!!b);var c=U(a,"join_timer_sec");b&&(c||0)===0&&(V(a,"join_timer_sec",60),V(a,"create_google_join",!0),I(100)&&Lj()&&U(a,"join_id")&&V(a,"create_fpm_join",!0))}},TM=function(a){a.copyToHitData(M.m.ig);for(var b=P(a.D,M.m.Yf)||[],c=0;c<
b.length;c++){var d=b[c];if(d.rule_result){a.copyToHitData(M.m.ig,d.traffic_type);AK(3);break}}},cN=function(a){a.copyToHitData(M.m.ph);P(a.D,M.m.Ke)&&(W(a,M.m.Ke,!0),gx(a)||rM(a))},ZM=function(a){a.copyToHitData(M.m.Ca);a.copyToHitData(M.m.tb)},PM=function(a){dx(a,"google_ng")&&!Mn()?a.copyToHitData(M.m.Qc,1):Iu(a)},WM=function(a){if(P(a.D,M.m.Ea)!==!1){if(I(92)){if(U(a,"allow_ad_personalization")===!1)return}else if(!Fq(a.D))return;var b=hx(a),c=P(a.D,M.m.fb);b&&c!==!1&&FI()&&R(M.m.R)&&Dm(M.m.T)&&
Fm(["ads"]).ads&&ml()&&W(a,M.m.zh,!0)}},aN=function(a){var b=P(a.D,M.m.Yb);b&&AK(12);U(a,"em_event")&&AK(14);var c=fm(gm());(b||sm(c)||c&&c.parent&&c.context&&c.context.source===5)&&AK(19)},AM=function(a){if(oM(a.target.destinationId))O(28),a.isAborted=!0;else if(I(140)){var b=em();if(b&&Array.isArray(b.destinations))for(var c=0;c<b.destinations.length;c++)if(oM(b.destinations[c])){O(125);a.isAborted=!0;break}}},VM=function(a){ll("attribution-reporting")&&W(a,M.m.rc,"1")},BM=function(a){if(Px.Im.replace(/\s+/g,
"").split(",").indexOf(a.eventName)>=0)a.isAborted=!0;else{var b=ex(a);b&&b.blacklisted&&(a.isAborted=!0)}},RM=function(a){var b=function(c){return!!c&&c.conversion};V(a,"is_conversion",b(ex(a)));U(a,"is_first_visit")&&V(a,"is_first_visit_conversion",b(ex(a,"first_visit")));U(a,"is_session_start")&&V(a,"is_session_start_conversion",b(ex(a,"session_start")))},SM=function(a){Wh.hasOwnProperty(a.eventName)&&(V(a,"is_ecommerce",!0),a.copyToHitData(M.m.ja),a.copyToHitData(M.m.Fa))},$M=function(a){if(I(97)&&
(!I(14)||!gx(a))&&U(a,"is_conversion")&&R(M.m.R)&&dx(a,"ga4_ads_linked",!1)){var b=tu(a),c=Zs(b.prefix),d=ou(c);W(a,M.m.ld,d.zg);W(a,M.m.nd,d.Bg);W(a,M.m.md,d.Ag)}},XM=function(a){if(I(116)){var b=On();b&&V(a,"ga4_collection_subdomain",b)}},MM=function(a){V(a,"is_google_signals_allowed",hx(a)&&P(a.D,M.m.fb)!==!1&&FI()&&!Mn())};function zM(a){jb(a,function(c){c.charAt(0)==="_"&&delete a[c]});var b=a[M.m.tb]||{};jb(b,function(c){c.charAt(0)==="_"&&delete b[c]})};var eN=function(a){if(!dN(a)){var b=!1,c=function(){!b&&dN(a)&&(b=!0,yc(A,"visibilitychange",c),I(6)&&yc(A,"prerenderingchange",c),O(55))};xc(A,"visibilitychange",c);I(6)&&xc(A,"prerenderingchange",c);O(54)}},dN=function(a){if(I(6)&&"prerendering"in A?A.prerendering:A.visibilityState==="prerender")return!1;a();return!0};function fN(a,b){eN(function(){var c=Oo(a);if(c){var d=gN(c,b);Sp(a,d,2)}});}function gN(a,b){var c=function(){};var d=new yM(a.id),e=a.prefix==="MC";c=function(f,g,k,m){e&&(m.eventMetadata.is_merchant_center=!0);d.Jn(g,k,m)};Tl||hN(a,d,b);return c}
function hN(a,b,c){var d=b.H,e={},f={eventId:c,eventMetadata:(e.batch_on_navigation=!0,e)};I(57)&&(f.deferrable=!0);d.Mn(function(){wK=!0;Tp.flush();d.Cg()>=1E3&&gc.sendBeacon!==void 0&&Up(M.m.Jc,{},a.id,f);b.flush();d.Tk(function(){wK=!1;d.Tk()})});};var iN=gN;function kN(a,b,c){var d=this;}kN.K="internal.gtagConfig";function lN(){var a={};return a};
function nN(a,b){}
nN.publicName="gtagSet";function oN(){var a={};return a};function pN(a,b){}pN.publicName="injectHiddenIframe";var qN=function(){var a=0;return function(b){switch(b){case 1:a|=1;break;case 2:a|=2;break;case 3:a|=4}return a}}();
function rN(a,b,c,d,e){}rN.K="internal.injectHtml";var vN={};
function xN(a,b,c,d){}var yN={dl:1,id:1},zN={};
function AN(a,b,c,d){}I(156)?AN.publicName="injectScript":xN.publicName="injectScript";AN.K="internal.injectScript";function BN(){return Pn()}BN.K="internal.isAutoPiiEligible";function CN(a){var b=!0;return b}CN.publicName="isConsentGranted";function DN(a){var b=!1;return b}DN.K="internal.isDebugMode";function EN(){return Nn()}EN.K="internal.isDmaRegion";function FN(a){var b=!1;return b}FN.K="internal.isEntityInfrastructure";function GN(){var a=!1;return a}GN.K="internal.isLandingPage";function HN(){var a=Ch(function(b){GD(this).log("error",b)});a.publicName="JSON";return a};function IN(a){var b=void 0;return od(b)}IN.K="internal.legacyParseUrl";function JN(){return!1}
var KN={getItem:function(a){var b=null;return b},setItem:function(a,b){return!1},removeItem:function(a){}};function LN(){}LN.publicName="logToConsole";function MN(a,b){}MN.K="internal.mergeRemoteConfig";function NN(a,b,c){c=c===void 0?!0:c;var d=[];return od(d)}NN.K="internal.parseCookieValuesFromString";function ON(a){var b=void 0;if(typeof a!=="string")return;a&&vb(a,"//")&&(a=A.location.protocol+a);if(typeof URL==="function"){var c;a:{var d;try{d=new URL(a)}catch(w){c=void 0;break a}for(var e={},f=Array.from(d.searchParams),g=0;g<f.length;g++){var k=f[g][0],m=f[g][1];e.hasOwnProperty(k)?typeof e[k]==="string"?e[k]=[e[k],m]:e[k].push(m):e[k]=m}c=od({href:d.href,origin:d.origin,protocol:d.protocol,username:d.username,password:d.password,host:d.host,
hostname:d.hostname,port:d.port,pathname:d.pathname,search:d.search,searchParams:e,hash:d.hash})}return c}var n;try{n=lk(a)}catch(w){return}if(!n.protocol||!n.host)return;var p={};if(n.search)for(var q=n.search.replace("?","").split("&"),r=0;r<q.length;r++){var v=q[r].split("="),u=v[0],t=decodeURIComponent(v.splice(1).join("=")).replace(/\+/g," ");p.hasOwnProperty(u)?typeof p[u]==="string"?p[u]=[p[u],t]:p[u].push(t):p[u]=t}n.searchParams=p;n.origin=n.protocol+"//"+n.host;n.username="";n.password=
"";b=od(n);return b}ON.publicName="parseUrl";function PN(a){}PN.K="internal.processAsNewEvent";function QN(a,b,c){var d;return d}QN.K="internal.pushToDataLayer";function RN(a){var b=xa.apply(1,arguments),c=!1;if(!bh(a))throw J(this.getName(),["string"],arguments);for(var d=[this,a],e=l(b),f=e.next();!f.done;f=e.next())d.push(nd(f.value,this.J,1));try{K.apply(null,d),c=!0}catch(g){return!1}return c}RN.publicName="queryPermission";function SN(a){var b=this;}SN.K="internal.queueAdsTransmission";function TN(){var a="";return a}TN.publicName="readCharacterSet";function UN(){return rj.zb}UN.K="internal.readDataLayerName";function VN(){var a="";return a}VN.publicName="readTitle";function WN(a,b){var c=this;if(!bh(a)||!Yg(b))throw J(this.getName(),["string","function"],arguments);rv(a,function(d){b.invoke(c.J,od(d,c.J,1))});}WN.K="internal.registerCcdCallback";function XN(a){
return!0}XN.K="internal.registerDestination";var YN=["config","event","get","set"];function ZN(a,b,c){}ZN.K="internal.registerGtagCommandListener";function $N(a,b){var c=!1;return c}$N.K="internal.removeDataLayerEventListener";function aO(a,b){}
aO.K="internal.removeFormData";function bO(){}bO.publicName="resetDataLayer";function cO(a,b,c){var d=void 0;return d}cO.K="internal.scrubUrlParams";function dO(a){}dO.K="internal.sendAdsHit";function eO(a,b,c,d){if(arguments.length<2||!Wg(d)||!Wg(c))throw J(this.getName(),["any","any","Object|undefined","Object|undefined"],arguments);var e=c?nd(c):{},f=nd(a),g=Array.isArray(f)?f:[f];b=String(b);var k=d?nd(d):{},m=GD(this);k.originatingEntity=wE(m);for(var n=0;n<g.length;n++){var p=g[n];if(typeof p==="string"){var q=
{};Zc(e,q);var r={};Zc(k,r);var v=yv(p,b,q);Bv(v,k.eventId||m.eventId,r)}}}eO.K="internal.sendGtagEvent";function fO(a,b,c){}fO.publicName="sendPixel";function gO(a,b){}gO.K="internal.setAnchorHref";function hO(a){}hO.K="internal.setContainerConsentDefaults";function iO(a,b,c,d){var e=this;d=d===void 0?!0:d;var f=!1;
return f}iO.publicName="setCookie";function jO(a){}jO.K="internal.setCorePlatformServices";function kO(a,b){}kO.K="internal.setDataLayerValue";function lO(a){}lO.publicName="setDefaultConsentState";function mO(a,b){}mO.K="internal.setDelegatedConsentType";function nO(a,b){}nO.K="internal.setFormAction";function oO(a,b,c){c=c===void 0?!1:c;}oO.K="internal.setInCrossContainerData";function pO(a,b,c){return!1}pO.publicName="setInWindow";function qO(a,b,c){}qO.K="internal.setProductSettingsParameter";function rO(a,b,c){if(!bh(a)||!bh(b)||arguments.length!==3)throw J(this.getName(),["string","string","any"],arguments);for(var d=b.split("."),e=Wp(a),f=0;f<d.length-1;f++){if(e[d[f]]===void 0)e[d[f]]={};else if(!Yc(e[d[f]]))throw Error("setRemoteConfigParameter failed, path contains a non-object type: "+d[f]);e=e[d[f]]}e[d[f]]=nd(c,this.J,1);}rO.K="internal.setRemoteConfigParameter";function sO(a,b){}sO.K="internal.setTransmissionMode";function tO(a,b,c,d){var e=this;}tO.publicName="sha256";function uO(a,b,c){}
uO.K="internal.sortRemoteConfigParameters";function vO(a,b){var c=void 0;return c}vO.K="internal.subscribeToCrossContainerData";var wO={},xO={};wO.getItem=function(a){var b=null;K(this,"access_template_storage");var c=GD(this).vb();xO[c]&&(b=xO[c].hasOwnProperty("gtm."+a)?xO[c]["gtm."+a]:null);return b};wO.setItem=function(a,b){K(this,"access_template_storage");var c=GD(this).vb();xO[c]=xO[c]||{};xO[c]["gtm."+a]=b;};
wO.removeItem=function(a){K(this,"access_template_storage");var b=GD(this).vb();if(!xO[b]||!xO[b].hasOwnProperty("gtm."+a))return;delete xO[b]["gtm."+a];};wO.clear=function(){K(this,"access_template_storage"),delete xO[GD(this).vb()];};wO.publicName="templateStorage";function yO(a,b){var c=!1;return c}yO.K="internal.testRegex";function zO(a){var b;return b};function AO(a){var b;return b}AO.K="internal.unsiloId";function BO(a,b){var c;return c}BO.K="internal.unsubscribeFromCrossContainerData";function CO(a){}CO.publicName="updateConsentState";var DO;function EO(a,b,c){DO=DO||new Nh;DO.add(a,b,c)}function FO(a,b){var c=DO=DO||new Nh;if(c.C.hasOwnProperty(a))throw Error("Attempting to add a private function which already exists: "+a+".");if(c.contains(a))throw Error("Attempting to add a private function with an existing API name: "+a+".");c.C[a]=ab(b)?ih(a,b):jh(a,b)}
function GO(){return function(a){var b;var c=DO;if(c.contains(a))b=c.get(a,this);else{var d;if(d=c.C.hasOwnProperty(a)){var e=this.J.C;if(e){var f=!1,g=e.vb();if(g){ph(g)||(f=!0);}d=f}else d=!0}if(d){var k=c.C.hasOwnProperty(a)?c.C[a]:void 0;
b=k}else throw Error(a+" is not a valid API name.");}return b}};function HO(){var a=function(c){return void FO(c.K,c)},b=function(c){return void EO(c.publicName,c)};b(AD);b(HD);b(WE);b(YE);b(ZE);b(fF);b(hF);b(lG);b(HN());b(nG);b(SJ);b(TJ);b(lK);b(mK);b(nK);b(tK);b(nN);b(pN);b(CN);b(LN);b(ON);b(RN);b(TN);b(VN);b(fO);b(iO);b(lO);b(pO);b(tO);b(wO);b(CO);EO("Math",nh());EO("Object",Lh);EO("TestHelper",Qh());EO("assertApi",kh);EO("assertThat",lh);EO("decodeUri",qh);EO("decodeUriComponent",rh);EO("encodeUri",sh);EO("encodeUriComponent",th);EO("fail",yh);EO("generateRandom",
zh);EO("getTimestamp",Ah);EO("getTimestampMillis",Ah);EO("getType",Bh);EO("makeInteger",Dh);EO("makeNumber",Eh);EO("makeString",Fh);EO("makeTableMap",Gh);EO("mock",Jh);EO("mockObject",Kh);EO("fromBase64",NJ,!("atob"in z));EO("localStorage",KN,!JN());EO("toBase64",zO,!("btoa"in z));a(zD);a(DD);a(YD);a(jE);a(qE);a(vE);a(LE);a(UE);a(XE);a($E);a(aF);a(bF);a(cF);a(dF);a(eF);a(gF);a(iF);a(kG);a(mG);a(oG);a(qG);a(rG);a(sG);a(tG);a(uG);a(zG);a(HG);a(IG);a(TG);a(YG);a(cH);a(lH);a(qH);a(DH);a(FH);a(TH);a(UH);
a(WH);a(LJ);a(MJ);a(OJ);a(PJ);a(QJ);a(UJ);a(VJ);a(WJ);a(XJ);a(YJ);a(ZJ);a($J);a(aK);a(bK);a(cK);a(dK);a(fK);a(gK);a(hK);a(iK);a(jK);a(kK);a(oK);a(pK);a(qK);a(rK);a(sK);a(vK);a(kN);a(rN);a(AN);a(BN);a(DN);a(EN);a(FN);a(GN);a(IN);a(JE);a(MN);a(NN);a(PN);a(QN);a(SN);a(UN);a(WN);a(XN);a(ZN);a($N);a(aO);a(Ph);a(cO);a(dO);a(eO);a(gO);a(hO);a(jO);a(kO);a(mO);a(nO);a(oO);a(qO);a(rO);a(sO);a(uO);a(vO);a(yO);a(AO);a(BO);FO("internal.CrossContainerSchema",pG());FO("internal.GtagSchema",lN());FO("internal.IframingStateSchema",
oN());
I(156)?b(AN):b(xN);return GO()};var xD;
function IO(){var a=data.sandboxed_scripts,b=data.security_groups;a:{var c=data.runtime||[],d=data.runtime_lines;xD=new Ie;JO();pf=wD();var e=xD,f=HO(),g=new gd("require",f);g.Ma();e.C.C.set("require",g);for(var k=[],m=0;m<c.length;m++){var n=c[m];if(!Array.isArray(n)||n.length<3){if(n.length===0)continue;break a}d&&d[m]&&d[m].length&&Lf(n,d[m]);try{xD.execute(n),I(114)&&Ck&&n[0]===50&&k.push(n[1])}catch(r){}}I(114)&&(Cf=k)}if(a&&a.length)for(var p=0;p<a.length;p++){var q=a[p].replace(/^_*/,"");Hj[q]=
["sandboxedScripts"]}KO(b)}function JO(){xD.C.C.N=function(a,b,c){Eo.SANDBOXED_JS_SEMAPHORE=Eo.SANDBOXED_JS_SEMAPHORE||0;Eo.SANDBOXED_JS_SEMAPHORE++;try{return a.apply(b,c)}finally{Eo.SANDBOXED_JS_SEMAPHORE--}}}function KO(a){a&&jb(a,function(b,c){for(var d=0;d<c.length;d++){var e=c[d].replace(/^_*/,"");Hj[e]=Hj[e]||[];Hj[e].push(b)}})};function LO(a){Bv(vv("developer_id."+a,!0),0,{})};var MO=Array.isArray;function NO(a,b){return Zc(a,b||null)}function X(a){return window.encodeURIComponent(a)}function OO(a,b,c){wc(a,b,c)}function PO(a,b){if(!a)return!1;var c=fk(lk(a),"host");if(!c)return!1;for(var d=0;b&&d<b.length;d++){var e=b[d]&&b[d].toLowerCase();if(e){var f=c.length-e.length;f>0&&e.charAt(0)!=="."&&(f--,e="."+e);if(f>=0&&c.indexOf(e,f)===f)return!0}}return!1}
function QO(a,b,c){for(var d={},e=!1,f=0;a&&f<a.length;f++)a[f]&&a[f].hasOwnProperty(b)&&a[f].hasOwnProperty(c)&&(d[a[f][b]]=a[f][c],e=!0);return e?d:null}var ZO=z.clearTimeout,$O=z.setTimeout;function aP(a,b,c){if(Rq()){b&&D(b)}else return sc(a,b,c,void 0)}function bP(){return z.location.href}function cP(a,b){return Rj(a,b||2)}function dP(a,b){z[a]=b}function eP(a,b,c){b&&(z[a]===void 0||c&&!z[a])&&(z[a]=b);return z[a]}function fP(a,b){if(Rq()){b&&D(b)}else uc(a,b)}
var gP={};var Z={securityGroups:{}};

Z.securityGroups.access_template_storage=["google"],Z.__access_template_storage=function(){return{assert:function(){},P:function(){return{}}}},Z.__access_template_storage.F="access_template_storage",Z.__access_template_storage.isVendorTemplate=!0,Z.__access_template_storage.priorityOverride=0,Z.__access_template_storage.isInfrastructure=!1,Z.__access_template_storage.runInSiloedMode=!1;
Z.securityGroups.v=["google"],Z.__v=function(a){var b=a.vtp_name;if(!b||!b.replace)return!1;var c=cP(b.replace(/\\\./g,"."),a.vtp_dataLayerVersion||1);return c!==void 0?c:a.vtp_defaultValue},Z.__v.F="v",Z.__v.isVendorTemplate=!0,Z.__v.priorityOverride=0,Z.__v.isInfrastructure=!0,Z.__v.runInSiloedMode=!1;

Z.securityGroups.read_event_data=["google"],function(){function a(b,c){return{key:c}}(function(b){Z.__read_event_data=b;Z.__read_event_data.F="read_event_data";Z.__read_event_data.isVendorTemplate=!0;Z.__read_event_data.priorityOverride=0;Z.__read_event_data.isInfrastructure=!1;Z.__read_event_data.runInSiloedMode=!1})(function(b){var c=b.vtp_eventDataAccess,d=b.vtp_keyPatterns||[],e=b.vtp_createPermissionError;return{assert:function(f,g){if(g!=null&&!bb(g))throw e(f,{key:g},"Key must be a string.");
if(c!=="any"){try{if(c==="specific"&&g!=null&&zg(g,d))return}catch(k){throw e(f,{key:g},"Invalid key filter.");}throw e(f,{key:g},"Prohibited read from event data.");}},P:a}})}();
Z.securityGroups.detect_youtube_activity_events=["google"],function(){function a(b,c){return{options:{fixMissingApi:!!c.fixMissingApi}}}(function(b){Z.__detect_youtube_activity_events=b;Z.__detect_youtube_activity_events.F="detect_youtube_activity_events";Z.__detect_youtube_activity_events.isVendorTemplate=!0;Z.__detect_youtube_activity_events.priorityOverride=0;Z.__detect_youtube_activity_events.isInfrastructure=!1;Z.__detect_youtube_activity_events.runInSiloedMode=!1})(function(b){var c=!!b.vtp_allowFixMissingJavaScriptApi,
d=b.vtp_createPermissionError;return{assert:function(e,f){if(!c&&f&&f.fixMissingApi)throw d(e,{},"Prohibited option: fixMissingApi.");},P:a}})}();


Z.securityGroups.detect_history_change_events=["google"],function(){function a(){return{}}(function(b){Z.__detect_history_change_events=b;Z.__detect_history_change_events.F="detect_history_change_events";Z.__detect_history_change_events.isVendorTemplate=!0;Z.__detect_history_change_events.priorityOverride=0;Z.__detect_history_change_events.isInfrastructure=!1;Z.__detect_history_change_events.runInSiloedMode=!1})(function(){return{assert:function(){},P:a}})}();



Z.securityGroups.detect_link_click_events=["google"],function(){function a(b,c){return{options:c}}(function(b){Z.__detect_link_click_events=b;Z.__detect_link_click_events.F="detect_link_click_events";Z.__detect_link_click_events.isVendorTemplate=!0;Z.__detect_link_click_events.priorityOverride=0;Z.__detect_link_click_events.isInfrastructure=!1;Z.__detect_link_click_events.runInSiloedMode=!1})(function(b){var c=b.vtp_allowWaitForTags,d=b.vtp_createPermissionError;return{assert:function(e,f){if(!c&&
f&&f.waitForTags)throw d(e,{},"Prohibited option waitForTags.");},P:a}})}();
Z.securityGroups.detect_form_submit_events=["google"],function(){function a(b,c){return{options:c}}(function(b){Z.__detect_form_submit_events=b;Z.__detect_form_submit_events.F="detect_form_submit_events";Z.__detect_form_submit_events.isVendorTemplate=!0;Z.__detect_form_submit_events.priorityOverride=0;Z.__detect_form_submit_events.isInfrastructure=!1;Z.__detect_form_submit_events.runInSiloedMode=!1})(function(b){var c=b.vtp_allowWaitForTags,d=b.vtp_createPermissionError;return{assert:function(e,f){if(!c&&
f&&f.waitForTags)throw d(e,{},"Prohibited option waitForTags.");},P:a}})}();Z.securityGroups.read_container_data=["google"],Z.__read_container_data=function(){return{assert:function(){},P:function(){return{}}}},Z.__read_container_data.F="read_container_data",Z.__read_container_data.isVendorTemplate=!0,Z.__read_container_data.priorityOverride=0,Z.__read_container_data.isInfrastructure=!1,Z.__read_container_data.runInSiloedMode=!1;

Z.securityGroups.listen_data_layer=["google"],function(){function a(b,c){return{eventName:c}}(function(b){Z.__listen_data_layer=b;Z.__listen_data_layer.F="listen_data_layer";Z.__listen_data_layer.isVendorTemplate=!0;Z.__listen_data_layer.priorityOverride=0;Z.__listen_data_layer.isInfrastructure=!1;Z.__listen_data_layer.runInSiloedMode=!1})(function(b){var c=b.vtp_accessType,d=b.vtp_allowedEvents||[],e=b.vtp_createPermissionError;return{assert:function(f,g){if(!bb(g))throw e(f,{eventName:g},"Event name must be a string.");
if(!(c==="any"||c==="specific"&&d.indexOf(g)>=0))throw e(f,{eventName:g},"Prohibited listen on data layer event.");},P:a}})}();
Z.securityGroups.detect_user_provided_data=["google"],function(){function a(b,c){return{dataSource:c}}(function(b){Z.__detect_user_provided_data=b;Z.__detect_user_provided_data.F="detect_user_provided_data";Z.__detect_user_provided_data.isVendorTemplate=!0;Z.__detect_user_provided_data.priorityOverride=0;Z.__detect_user_provided_data.isInfrastructure=!1;Z.__detect_user_provided_data.runInSiloedMode=!1})(function(b){var c=b.vtp_createPermissionError;return{assert:function(d,e){if(e!=="auto"&&e!=="manual"&&
e!=="code")throw c(d,{},"Unknown user provided data source.");if(b.vtp_limitDataSources)if(e!=="auto"||b.vtp_allowAutoDataSources){if(e==="manual"&&!b.vtp_allowManualDataSources)throw c(d,{},"Detection of user provided data via manually specified CSS selectors is not allowed.");if(e==="code"&&!b.vtp_allowCodeDataSources)throw c(d,{},"Detection of user provided data from an in-page variable is not allowed.");}else throw c(d,{},"Automatic detection of user provided data is not allowed.");},P:a}})}();



Z.securityGroups.get_url=["google"],function(){function a(b,c,d){return{component:c,queryKey:d}}(function(b){Z.__get_url=b;Z.__get_url.F="get_url";Z.__get_url.isVendorTemplate=!0;Z.__get_url.priorityOverride=0;Z.__get_url.isInfrastructure=!1;Z.__get_url.runInSiloedMode=!1})(function(b){var c=b.vtp_urlParts==="any"?null:[];c&&(b.vtp_protocol&&c.push("protocol"),b.vtp_host&&c.push("host"),b.vtp_port&&c.push("port"),b.vtp_path&&c.push("path"),b.vtp_extension&&c.push("extension"),b.vtp_query&&c.push("query"),
b.vtp_fragment&&c.push("fragment"));var d=c&&b.vtp_queriesAllowed!=="any"?b.vtp_queryKeys||[]:null,e=b.vtp_createPermissionError;return{assert:function(f,g,k){if(g){if(!bb(g))throw e(f,{},"URL component must be a string.");if(c&&c.indexOf(g)<0)throw e(f,{},"Prohibited URL component: "+g);if(g==="query"&&d){if(!k)throw e(f,{},"Prohibited from getting entire URL query when query keys are specified.");if(!bb(k))throw e(f,{},"Query key must be a string.");if(d.indexOf(k)<0)throw e(f,{},"Prohibited query key: "+
k);}}else if(c)throw e(f,{},"Prohibited from getting entire URL when components are specified.");},P:a}})}();



Z.securityGroups.gct=["google"],function(){function a(b){for(var c=[],d=0;d<b.length;d++)try{c.push(new RegExp(b[d]))}catch(e){}return c}(function(b){Z.__gct=b;Z.__gct.F="gct";Z.__gct.isVendorTemplate=!0;Z.__gct.priorityOverride=0;Z.__gct.isInfrastructure=!1;Z.__gct.runInSiloedMode=!0})(function(b){var c={},d=b.vtp_sessionDuration;d>0&&(c[M.m.Jd]=d);c[M.m.Ae]=b.vtp_eventSettings;c[M.m.eh]=b.vtp_dynamicEventSettings;c[M.m.Rc]=b.vtp_googleSignals===1;c[M.m.qh]=b.vtp_foreignTld;c[M.m.nh]=b.vtp_restrictDomain===
1;c[M.m.Yf]=b.vtp_internalTrafficResults;var e=M.m.Ba,f=b.vtp_linker;f&&f[M.m.Z]&&(f[M.m.Z]=a(f[M.m.Z]));c[e]=f;var g=M.m.eg,k=b.vtp_referralExclusionDefinition;k&&k.include_conditions&&(k.include_conditions=a(k.include_conditions));c[g]=k;var m=hm(b.vtp_trackingId);Yp(m,c);fN(m,b.vtp_gtmEventId);D(b.vtp_gtmOnSuccess)})}();



Z.securityGroups.get=["google"],Z.__get=function(a){var b=a.vtp_settings,c=b.eventParameters||{},d=String(a.vtp_eventName),e={};e.eventId=a.vtp_gtmEventId;e.priorityId=a.vtp_gtmPriorityId;a.vtp_deferrable&&(e.deferrable=!0);var f=yv(String(b.streamId),d,c);Bv(f,e.eventId,e);a.vtp_gtmOnSuccess()},Z.__get.F="get",Z.__get.isVendorTemplate=!0,Z.__get.priorityOverride=0,Z.__get.isInfrastructure=!1,Z.__get.runInSiloedMode=!1;
Z.securityGroups.detect_scroll_events=["google"],function(){function a(){return{}}(function(b){Z.__detect_scroll_events=b;Z.__detect_scroll_events.F="detect_scroll_events";Z.__detect_scroll_events.isVendorTemplate=!0;Z.__detect_scroll_events.priorityOverride=0;Z.__detect_scroll_events.isInfrastructure=!1;Z.__detect_scroll_events.runInSiloedMode=!1})(function(){return{assert:function(){},P:a}})}();



Z.securityGroups.detect_form_interaction_events=["google"],function(){function a(){return{}}(function(b){Z.__detect_form_interaction_events=b;Z.__detect_form_interaction_events.F="detect_form_interaction_events";Z.__detect_form_interaction_events.isVendorTemplate=!0;Z.__detect_form_interaction_events.priorityOverride=0;Z.__detect_form_interaction_events.isInfrastructure=!1;Z.__detect_form_interaction_events.runInSiloedMode=!1})(function(){return{assert:function(){},P:a}})}();

var Ho={dataLayer:Sj,callback:function(a){Gj.hasOwnProperty(a)&&ab(Gj[a])&&Gj[a]();delete Gj[a]},bootstrap:0};
function hP(){Go();km();EA();tb(Hj,Z.securityGroups);var a=fm(gm()),b,c=a==null?void 0:(b=a.context)==null?void 0:b.source;fo(c,a==null?void 0:a.parent);c!==2&&c!==4&&c!==3||O(142);Bf={wm:Rf}}var iP=!1;
function Hn(){try{if(iP||!tm()){qj();oj.O="";oj.Bc="ad_storage|analytics_storage|ad_user_data|ad_personalization";
oj.sa="ad_storage|analytics_storage|ad_user_data";oj.ba="5430";oj.ba="5490";im();if(I(103)){}ig[8]=
!0;var a=Fo("debugGroupId",function(){return String(Math.floor(Number.MAX_SAFE_INTEGER*Math.random()))});mo(a);Do();tq();Ko();if(lm()){GE();oA().removeExternalRestrictions(dm());}else{Sx();AA();zf();vf=Z;wf=nD;Tf=new $f;IO();hP();Fn=Jn();Ao();DC();PB();jC=!1;A.readyState==="complete"?lC():xc(z,"load",lC);JB();Ck&&(Ap(Op),z.setInterval(Np,864E5),Ap(qD),Ap(gB),Ap(Vy),Ap(Rp),Ap(tD),Ap(rB),I(114)&&(Ap(lB),Ap(mB),Ap(nB)));Dk&&(ln(),fp(),FC(),JC(),HC(),bn("bt",String(oj.C?2:zj?1:0)),bn("ct",String(oj.C?0:zj?1:Rq()?2:3)),GC());dD();vn(1);HE();Fj=qb();Ho.bootstrap=Fj;oj.N&&CC();I(103)&&nz();I(129)&&(typeof z.name==="string"&&
vb(z.name,"web-pixel-sandbox-CUSTOM")&&Nc()?LO("dMDg0Yz"):z.Shopify&&(LO("dN2ZkMj"),Nc()&&LO("dNTU0Yz")))}}}catch(b){vn(4),Kp()}}
(function(a){function b(){n=A.documentElement.getAttribute("data-tag-assistant-present");Sn(n)&&(m=k.Xj)}function c(){m&&jc?g(m):a()}if(!z["__TAGGY_INSTALLED"]){var d=!1;if(A.referrer){var e=lk(A.referrer);d=hk(e,"host")==="cct.google"}if(!d){var f=ar("googTaggyReferrer");d=!(!f.length||!f[0].length)}d&&(z["__TAGGY_INSTALLED"]=!0,sc("https://cct.google/taggy/agent.js"))}var g=function(u){var t="GTM",w="GTM";xj&&(t="OGT",w="GTAG");var x=z["google.tagmanager.debugui2.queue"];x||(x=
[],z["google.tagmanager.debugui2.queue"]=x,sc("https://"+rj.Qf+"/debug/bootstrap?id="+Xf.ctid+"&src="+w+"&cond="+u+"&gtm="+Tq()));var y={messageType:"CONTAINER_STARTING",data:{scriptSource:jc,containerProduct:t,debug:!1,id:Xf.ctid,targetRef:{ctid:Xf.ctid,isDestination:Vl()},aliases:Yl(),destinations:Wl()}};y.data.resume=function(){a()};rj.rl&&(y.data.initialPublish=!0);x.push(y)},k={Sl:1,Zj:2,lk:3,bj:4,Xj:5};k[k.Sl]="GTM_DEBUG_LEGACY_PARAM";k[k.Zj]="GTM_DEBUG_PARAM";k[k.lk]="REFERRER";k[k.bj]="COOKIE";k[k.Xj]="EXTENSION_PARAM";
var m=void 0,n=void 0,p=fk(z.location,"query",!1,void 0,"gtm_debug");Sn(p)&&(m=k.Zj);if(!m&&A.referrer){var q=lk(A.referrer);hk(q,"host")==="tagassistant.google.com"&&(m=k.lk)}if(!m){var r=ar("__TAG_ASSISTANT");r.length&&r[0].length&&(m=k.bj)}m||b();if(!m&&Rn(n)){var v=!1;xc(A,"TADebugSignal",function(){v||(v=!0,b(),c())},!1);z.setTimeout(function(){v||(v=!0,b(),c())},200)}else c()})(function(){I(81)&&iP&&!Jn()["0"]?Gn():Hn()});

})()

