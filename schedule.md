本地化CDN资源引用执行计划

## 目标

将所有页面中引用的 
- //cdn.globalso.com/uncleshipping/ 资源，统一替换为本地 /uncleshipping/ 路径，
- //www.uncleshipping.com/uploads统一替换为/uploads

提升网站加载速度和稳定性，减少对外部网络的依赖。

---

## 步骤一：资源梳理与准备

1. 梳理所有引用的CDN资源

- 使用全局搜索（如 grep 或 IDE 全局查找）查找所有 //cdn.globalso.com/uncleshipping/ 相关的资源引用，包括图片、CSS、JS等。

- 记录所有资源的完整路径和文件名。

1. 下载CDN资源到本地

- 检查 /uncleshipping/ 目录下是否已存在同名文件，已存在则跳过。

- 对于未存在的资源，使用 curl 或 wget 下载到本地，保持原有目录结构。例如：
    
    text
    
    Apply to index.html
    
         curl -o uncleshipping/style/global/style.css https://cdn.globalso.com/uncleshipping/style/global/style.css
    

- 确保所有引用的资源都已在本地对应目录下。

---

## 步骤二：批量替换页面引用路径

1. 批量替换HTML文件中的资源路径

- 对所有 HTML 文件，执行如下替换：

- //cdn.globalso.com/uncleshipping/ → /uncleshipping/

- 可使用文本编辑器的批量替换功能，或命令行工具（如 sed）：
    
    text
    
    Apply to index.html
    
         sed -i '' 's#//cdn.globalso.com/uncleshipping/#/uncleshipping/#g' *.html
    

- 检查替换后页面是否正常显示本地资源。

1. 特殊情况处理

- 如有部分页面需保留CDN引用，请提前列出并排除在批量替换之外。

---

## 步骤三：验证与优化

1. 本地预览和测试

- 启动本地服务器，访问所有页面，确保图片、样式、脚本等资源均能正常加载。

- 检查控制台无 404 或跨域等错误。

1. 团队协作说明