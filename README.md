# 本地化物流服务网站（UncleShipping 本地版）

## 项目简介

本项目旨在将原有 UncleShipping 物流服务网站的所有资源（页面、图片、脚本、样式等）全部转为本地资源，实现**完全离线运行**。用户无需依赖外部网络，即可在本地浏览和体验网站的全部内容。适合本地演示、内网部署、二次开发等场景。

---

## 主要功能

- **本地化所有页面**：所有业务页面、服务介绍、案例、常见问题等均可本地访问。
- **本地化静态资源**：图片、样式、脚本等全部存储在本地，无需外部CDN。
- **本地表单与交互**：表单、在线聊天等功能已本地化（仅界面，数据不会发送到外部服务器）。
- **多语言界面**：保留多语言切换入口，后续可扩展本地多语言支持。
- **页面结构清晰**：每个业务/服务/案例/术语等均有独立目录和页面，便于维护和扩展。

---

# 项目目录结构说明

## 目录结构

```
uncleshipping2/
  ├── business/                # 业务相关页面目录（如物流产品、服务等，包含大量子目录）
  ├── about-us/               # 公司介绍页面
  ├── contact-us/             # 联系我们页面
  ├── faqs/                   # 常见问题页面
  ├── case/                   # 案例展示页面
  ├── news/                   # 新闻资讯页面及子新闻目录
  ├── download/               # 下载中心页面
  ├── products/               # 产品介绍页面
  ├── patents/                # 专利展示页面
  ├── smart-logistics/        # 智能物流页面
  ├── warehouse-distribution/ # 仓储配送页面
  ├── featured/               # 精选内容页面
  ├── keywords-b/             # 关键词页面
  ├── news_catalog/           # 新闻目录（含article/news子目录）
  ├── css/                    # 预留样式目录（当前为空）
  ├── style/                  # 主要样式与字体资源目录
  │     ├── global/           # 全局样式、字体、js
  │     │     ├── style.css   # 全站主样式
  │     │     ├── js/         # 全局js脚本（如jquery、common.js）
  │     │     ├── fonts/      # 字体资源（如Roboto）
  │     │     ├── webfonts/   # web字体（如FontAwesome）
  │     └── public/           # 公共样式与脚本（public.css、contact-style.css、public.js）
  ├── js/                     # 本地化功能脚本（如form.js、gtag.js、livechat.js）
  ├── img/                    # 图片资源（含logo、banner、服务、案例、icons等子目录）
  ├── uploads/                # 上传图片资源（大量图片，新闻、案例等用）
  ├── gtag/                   # 本地统计脚本目录
  ├── index.html              # 网站首页
  └── README.md               # 项目说明文档
```

## 目录分类与详细说明

- `business/`：所有与物流业务直接相关的页面目录（如FCL、LCL、EXW、FOB、DDP、DDU等产品和服务页面），每个业务类型有独立子目录，便于维护和扩展。
- `about-us/`、`contact-us/`、`faqs/`、`case/`、`news/`、`download/`、`products/`、`patents/`、`smart-logistics/`、`warehouse-distribution/`、`featured/`、`keywords-b/`：公司基础信息、帮助、案例、新闻、产品、专利、智能物流、仓储配送等通用内容，均为独立目录，方便查找。
- `news_catalog/`：新闻目录，包含`article/`和`news/`子目录，便于新闻内容分类管理。
- `style/`：主要样式与字体资源目录。
  - `global/`：全局样式（style.css）、全局js（js/）、字体（fonts/、webfonts/）。
    - `js/`：如jquery.min.js、common.js等全站通用脚本。
    - `fonts/Roboto/`：Roboto字体多种字重。
    - `webfonts/`：FontAwesome等web字体。
  - `public/`：公共样式（public.css、contact-style.css）和公共脚本（public.js）。
- `js/`：本地化功能脚本，如form.js（表单）、gtag.js（统计）、livechat.js（在线聊天）。
- `img/`：图片资源，含logo、banner、服务、案例、icons等子目录，所有页面图片均本地化。
- `uploads/`：上传图片资源，新闻、案例等页面用到的大量图片。
- `gtag/`：本地统计脚本目录。
- `index.html`：网站首页。
- `README.md`：本说明文档。

## 静态资源分类说明

- **样式文件**：全部在`style/global/style.css`和`style/public/public.css`等，页面通过`<link>`标签引用。
- **脚本文件**：主要在`js/`和`style/global/js/`，如表单、统计、交互等功能。
- **图片资源**：全部在`img/`和`uploads/`，页面图片均为本地文件。
- **字体资源**：在`style/global/fonts/Roboto/`和`style/global/webfonts/`，支持多种字体和图标。

## 页面功能说明

- **首页（index.html）**：展示公司主营业务、服务、案例、新闻、联系方式等。
- **业务页面（business/）**：每种物流服务/产品有独立目录和页面，内容详尽。
- **公司介绍（about-us/）**：公司历史、团队、荣誉、专利等。
- **联系我们（contact-us/）**：联系方式、在线表单（仅界面）。
- **常见问题（faqs/）**：FAQ列表，帮助用户快速了解常见问题。
- **案例展示（case/）**：物流案例、成功故事等。
- **新闻资讯（news/）**：行业新闻、公司动态，含大量子新闻目录。
- **下载中心（download/）**：资料下载页面。
- **产品介绍（products/）**：物流相关产品介绍。
- **专利展示（patents/）**：公司专利信息。
- **智能物流（smart-logistics/）**：智能物流相关内容。
- **仓储配送（warehouse-distribution/）**：仓储与配送服务介绍。
- **精选内容（featured/）**、**关键词（keywords-b/）**：专题内容和关键词页面。

## 维护与扩展建议

- 新增业务页面时，请统一放入`business/`目录下，参考现有目录结构。
- 新增公司信息、帮助类页面时，直接放在根目录下的对应目录。
- 静态资源（图片、样式、脚本、字体）请放入对应资源目录，避免外链。
- 修改页面引用路径时，注意区分业务页面和通用信息页面。
- 所有表单、交互功能仅为界面展示，数据不会发送到外部服务器。如需本地数据收集，请自行开发后端。
- 多语言切换目前仅切换本地页面，未实现真正的多语言内容，可自行扩展。
- 如有残留CDN图片链接，请手动下载到`img/`或`uploads/`目录并修改引用。
- 新增页面建议复制现有目录结构并修改内容，保持风格一致。

---

## 使用方法

1. **下载项目**  
   将整个项目文件夹下载到本地电脑。

2. **启动本地Web服务器**  
   推荐使用 [VSCode Live Server](https://marketplace.visualstudio.com/items?itemName=ritwickdey.LiveServer)、Python SimpleHTTPServer、Apache、Nginx等工具。  
   例如，使用Python3命令行：
   ```
   cd 项目目录
   python3 -m http.server 8000
   ```
   然后在浏览器访问 `http://localhost:8000/index.html`

3. **浏览网站**  
   直接在浏览器中访问首页或任意业务页面，体验本地化网站功能。

---

## 常见问题解答

- **Q：为什么要用本地服务器？**  
  A：直接用浏览器打开html文件可能会因安全策略导致部分资源无法加载，建议用本地Web服务器。

- **Q：如何新增一个业务页面？**  
  A：复制任意业务目录，重命名并修改`index.html`内容即可。

- **Q：如何彻底离线？**  
  A：确保所有图片、脚本、样式都在本地，无任何外链。

- **Q：表单/在线聊天能用吗？**  
  A：仅界面可用，数据不会发送到外部服务器。如需本地数据收集或消息推送，请开发本地后端。

- **Q：如何切换多语言？**  
  A：目前仅切换本地页面，内容未翻译。可自行扩展多语言内容。

- **Q：图片太多怎么管理？**  
  A：业务/新闻/案例图片建议放在`img/`或`uploads/`目录，按页面或用途分类。

---

## 后续改进建议

1. **完善本地表单功能**：支持本地数据存储或邮件通知。
2. **实现本地多语言内容**：为每种语言建立独立内容文件。
3. **彻底本地化所有图片资源**：移除所有外链图片。
4. **增加页面内容管理工具**：便于非技术人员维护内容。
5. **增加监控与错误提示**：便于发现和修复本地运行问题。

---

## 联系与支持

如有问题或建议，请联系项目维护者，或在本地`contact-us`页面填写表单（仅界面，不会发送数据）。

---

> 本文档适合初学者和非技术用户，所有操作均有详细说明。如需进一步定制开发，请联系专业开发者。

---

如果你有任何不明白的地方，随时可以问我！

---

**后续建议**：  
- 你可以根据实际业务需求，继续补充readme.md中的“目录结构说明”和“常见问题解答”部分，让文档更贴合你的实际使用场景。
- 如果你需要实现本地数据收集、后台管理等功能，可以随时告诉我，我会帮你详细规划和实现。 