@charset "utf-8";
html { font-size: 625%; }
body { background: #fff; font-size: 14px; }
a, abbr, address, blockquote, body, code, dd, dl, dt, fieldset, figure, form, h1, h2, h3, h4, h5, h6, html, iframe, img, label, li, object, ol, p, pre, td, th, ul { margin: 0; padding: 0; }
body, button, html, input, textarea {
    color: #212121;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    line-height: 1.5;
}
body { background-color: #fff; }
article, aside, figcaption, figure, footer, header, main, nav, section { display: block; }
h1 { font-size: 20px; }
h2 { font-size: 18px; }
h3 { font-size: 16px; }
h4, h5 { font-size: 14px; }
img { width: 100%; max-width: 100%; border: none; vertical-align: middle; }
li { list-style: none; }
em, i { font-style: normal; }
a { color: #333; text-decoration: none; }
a:focus { outline: 0; }
a:hover {color: #000000;text-decoration: none;}
input[type=text]:focus { outline: 0; }
input[type=button], input[type=reset], input[type=submit] { cursor: pointer; }
input[type=button]::-moz-focus-inner, input[type=file]>input[type=button]::-moz-focus-inner, input[type=reset]::-moz-focus-inner, input[type=submit]::-moz-focus-inner {
padding:0;
border:none;
}
hr {margin: 15px 0 15px 0;height: 1px;border: none;border-top: 1px dashed #919191;}
a:active, input, select, textarea { outline: 0!important; -webkit-tap-highlight-color: transparent; -webkit-tap-highlight-color: transparent; }
* { -webkit-box-sizing: border-box; -moz-box-sizing: border-box; box-sizing: border-box; outline: 0; -ms-box-sizing: border-box; -o-box-sizing: border-box; }
/*-------------------------------
			font start
			----------------------------------*/
@font-face { font-weight: normal; font-style: normal; font-family: 'fontawesome'; src: url("fonts/Font-Awesome/fontawesome-webfont.eot"); src: url("fonts/Font-Awesome/fontawesome-webfont.eot?#iefix") format("embedded-opentype"), url("fonts/Font-Awesome/fontawesome-webfont.woff2") format("woff2"), url("fonts/Font-Awesome/fontawesome-webfont.woff") format("woff"), url("fonts/Font-Awesome/fontawesome-webfont.ttf") format("truetype"), url("fonts/Font-Awesome/fontawesome-webfont.svg#fontawesomeBold") format("svg"); font-display: fallback; }
@font-face { font-family: "Roboto-Regular"; font-style: normal; src: url(fonts/Roboto/Roboto-Regular.ttf) format("TrueType"); font-display: fallback; }
@font-face { font-family: "Roboto-Bold"; font-style: normal; src: url(fonts/Roboto/Roboto-Bold.ttf) format("TrueType"); font-display: fallback; }
@font-face { font-family: "Roboto-Light"; font-style: normal; src: url(fonts/Roboto/Roboto-Light.ttf) format("TrueType"); font-display: fallback; }
@font-face { font-family: "Roboto-Black"; font-style: normal; src: url(fonts/Roboto/Roboto-Black.ttf) format("TrueType"); font-display: fallback; }
@font-face { font-family: "Roboto-Thin"; font-style: normal; src: url(fonts/Roboto/Roboto-Thin.ttf) format("TrueType"); font-display: fallback; }
@font-face { font-family: "Roboto-Medium"; font-style: normal; src: url(fonts/Roboto/Roboto-Medium.ttf) format("TrueType"); font-display: fallback; }
@font-face { font-family: "POPPINS-REGULAR.TTF"; font-style: normal; src: url(/style/global/fonts/POPPINS-REGULAR.TTF) }
/*-------------------------------
			font end
			----------------------------------*/
/* clear floating */
.clearfix:after, .layout:after, .sys_row:after, .web_main:after, .page_main:after, .nav_wrap .head_nav:after, .items_list ul:after, .product_items:after, .promote_list:after, .cate_items ul:after, .web_head .logo:after, .product-intro:after, .detail-tabs:after, .foot_items:after, .news_cell:after, .sys_row:after, .banner_navigate_button:after { clear: both; display: block; visibility: hidden; height: 0; content: ""; }
.clearfix, .layout, .sys_row, .clearfix, .layout, .sys_row, .flex_row, .web_main, .page_main, .nav_wrap .head_nav, .items_list ul, .product_items, .promote_list, .cate_items ul, .web_head .logo, .product-intro, .detail-tabs, .foot_items, .sys_row, .banner_navigate_button { *zoom: 1;
}
.clear { clear: both; }
/* layout */
body { position: absolute; top: 0; left: 0; overflow-x: hidden; width: 100%; min-width: 1200px; }
.layout { position: relative; margin: 0 auto; width: 1400px; }
.web_head .layout, .index_main .layout { width: 100%; padding: 0 9.5%; }
.z9999 { z-index: 9999!important; }
.hide { display: none; }
/*gotop*/
.gotop { position: fixed; right: 50px; bottom: 50px; z-index: 99999; visibility: hidden; -webkit-box-sizing: content-box; box-sizing: content-box; width: 50px; height: 50px; background-color: #e1600d; background-clip: content-box; box-shadow: 0 0 8px rgba(0,0,0,.2); color: #ffffff; text-align: center; text-align: center; font-size: 18px; line-height: 50px; opacity: 0; cursor: pointer; -webkit-transition: all .3s ease; -o-transition: all .3s ease; transition: all .3s ease; -webkit-transform: translateY(100%); -moz-transform: translateY(100%); -o-transform: translateY(100%); transform: translateY(100%); -ms-transform: translateY(100%); }
.gotop:hover, .gotop.active:hover { background-color: #e1600d; color: #fff; }
.gotop.active { visibility: visible; opacity: 1; -webkit-transform: none; -moz-transform: none; -o-transform: none; transform: none; -ms-transform: none; }
.gotop:before, .gotop em { -webkit-transition: all .3s ease; -o-transition: all .3s ease; transition: all .3s ease; }
.gotop em { position: absolute; top: 0; left: 0; width: 100%; color: #fff; font-size: 12px; opacity: 0; -webkit-transform: translateY(10px); -o-transform: translateY(10px); transform: translateY(10px); filter: alpha(opacity=0); -ms-transform: translateY(10px); }
.gotop:before { display: block; content: '\f176'; font-family: 'fontawesome'; }
.gotop:hover em { opacity: 1; -webkit-transform: none; -o-transform: none; transform: none; filter: alpha(opacity=100); -ms-transform: none; }
.gotop:hover:before { opacity: 0; -webkit-transform: translateY(-15px) scale(.5); -o-transform: translateY(-15px) scale(.5); transform: translateY(-15px) scale(.5); -ms-transform: translateY(-15px) scale(.5); filter: alpha(opacity=0); }
/* transition */
.head_nav li a, .nav_wrap .head_nav li li a, .nav_wrap .head_nav li ul, .nav_wrap .head_nav li li>a:before, .head_nav b:after, .product_item figure, .gotop, .product-item .item-img img, .product_item .item_img img, .product_item .item_img a, .product_item, .product_item .item_img a:before, .product_item .item_img a:after, .product_item .item_img:after, .product-btn-wrap a, .web_head, .change-language .change-language-title a:after, .newsletter .button, .mobile-head-item .middle-show-content-wrap, .product_item figure figcaption, .foot_item a, .pages a, .head_sns li img, .side_product_item .add-friend, .side_product_item .add-friend:after, .side-product-items .btn-prev, .side-product-items .btn-next, .blog-item:after { -webkit-transition: all .3s ease; -moz-transition: all .3s ease; -o-transition: all .3s ease; transition: all .3s ease; -ms-transition: all .3s ease; }
.head_nav>li>a:before, .head_nav>li>a:after, .icon-cate, .foot_txt_list li a:before, .web_footer .button:before { -webkit-transition: all .3s ease-in-out; -moz-transition: all .3s ease-in-out; -o-transition: all .3s ease-in-out; transition: all .3s ease-in-out; -ms-transition: all .3s ease-in-out; }
a, .nav_wrap .nav_btn_close, .index_main .product_item figure, .foot_item.foot_nav .fot_tit:before, .foot_item.foot_nav .fot_tit:after, .goods-may-like .navigate_button_next:before, .goods-may-like .navigate_button_prev:before, .feature_item .item_img img, .feature_item .item_img:before, .feature_item .item_img:after, .foot_item li a:before, .foot_item li, .company_information .about_btn, .index_inquiry_wrap .inquiry_btn { -webkit-transition: all .35s ease-in-out; -o-transition: all .35s ease-in-out; transition: all .35s ease-in-out; }
.project_item .item_img img, .advantage_item .ad_icon, .company_new:after, .company_information .company_img img { -webkit-transition: all 1s ease-in-out; -o-transition: all 1s ease-in-out; transition: all 1s ease-in-out; }
/*hover*/
/* grid */
.sys-layer { margin: 0 auto; }
.sys-row { margin: 0 -8px; }
.sys-col { float: left; }
.sys-col-inner { margin: 0 8px; }
.sys-col-md { float: left; }
.sys-col-md-12 { width: 100%; }
.sys-col-md-11 { width: 91.66666667%; }
.sys-col-md-10 { width: 83.33333333%; }
.sys-col-md-9 { width: 75%; }
.sys-col-md-8 { width: 66.66666667%; }
.sys-col-md-7 { width: 58.33333333%; }
.sys-col-md-6 { width: 50%; }
.sys-col-md-5 { width: 41.66666667%; }
.sys-col-md-4 { width: 33.33333333%; }
.sys-col-md-3 { width: 25%; }
.sys-col-md-2 { width: 16.66666667%; }
.sys-col-md-1 { width: 8.33333333%; }
.sys_row { margin-right: -15px; margin-left: -15px; }
.sys_col { float: left; padding-right: 15px; padding-left: 15px; }
/* float */
.sys_fl { float: left!important; }
.sys_fr { float: right!important; }
/* flex */
.flex_row, .items_list>ul { display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-orient: horizontal; -webkit-box-direction: normal; -webkit-flex-direction: row; flex-direction: row; -webkit-flex-wrap: wrap; -moz-flex-wrap: wrap; -ms-flex-wrap: wrap; -o-flex-wrap: wrap; flex-wrap: wrap; }
.flex_row_nowrap { -webkit-flex-wrap: nowrap; -moz-flex-wrap: nowrap; -ms-flex-wrap: nowrap; -o-flex-wrap: nowrap; flex-wrap: nowrap; }
/* button */
.sys_btn { position: relative; display: inline-block; overflow: hidden; padding: .2em 2em .2em 1em; min-width: 150px; border: 2px solid #e1600d; background-color: #f9f9f9; color: #333!important; vertical-align: middle; text-align: left; text-transform: uppercase; text-overflow: ellipsis; white-space: nowrap; letter-spacing: -.1em; font-size: 22px; line-height: 2; cursor: pointer; transition: all .3s ease-in-out; -webkit-transform: scale(1); -o-transform: scale(1); transform: scale(1); user-select: none; -ms-transform: scale(1); }
.sys_btn:hover { background-color: #e1600d; color: #fff!important; }
.sys_btn:after { position: absolute; top: 50%; right: 1.5em; margin-top: -.6em; content: '\f04b'; font-size: 12px; font-family: 'fontawesome'; line-height: 1.2em; }
/* placeholder */
input::-webkit-input-placeholder {
 color: rgba(0,0,0,.71);
}
 input:-moz-placeholder {
 color: rgba(0,0,0,.71);
}
 input::-moz-placeholder {
 color: rgba(0,0,0,.71);
}
 input:-ms-input-placeholder {
 color: rgba(0,0,0,.71);
}
/* box-sizing */
.nav_wrap, .product_item .item_img { -webkit-box-sizing: content-box; -moz-box-sizing: content-box; box-sizing: content-box; -ms-box-sizing: content-box; -o-box-sizing: content-box; }
/* font-family */
.search-btn, .side-cate li .icon-cate:before, .product-btn-wrap a:before, .mobile-head-item .title:before, .head_nav > li:after, .head_nav li b:after, .download-item .item-button:before, .faq-item .faq-title:before { display: inline-block; text-decoration: inherit; text-transform: none; font-weight: normal; font-style: normal; font-variant: normal; font-family: "fontawesome"; speak: none; }
.fa:before, .btn_more:after, .sys_btn:after, .head-search .search-btn:before, .swiper-button-next:before, .swiper-button-prev:before, .side-product-items .btn-prev:before, .side-product-items .btn-next:before, .product-btn-wrap .email:before, .product-btn-wrap .pdf:before { font-family: 'fontawesome'; }
/*flex_row*/
.flex_row { display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-orient: horizontal; -webkit-box-direction: normal; -webkit-flex-direction: row; flex-direction: row; -webkit-flex-wrap: wrap; -moz-flex-wrap: wrap; -ms-flex-wrap: wrap; -o-flex-wrap: wrap; flex-wrap: wrap; justify-content: space-between; -webkit-justify-content: space-between; }
/*==============web_head=================*/
.web_head { position: relative; z-index: 99; width: 100%; }
.top_bar {position: relative;padding: 13px 0;background: #e0600e;}
.head_phone { position: relative; float: right; margin-right: 90px; }
.head_phone a { position: relative; display: block; padding-left: 26px; color: #dcdcdc; font-size: 12px; font-family: Roboto-Regular; line-height: 20px; }
.head_phone a:before { position: absolute; top: 2px; left: 0; width: 16px; height: 16px; background-image: url(img/headphone.png); content: ""; }
.head_sns { position: relative; float: right; font-size: 0; }
.head_sns li {display: inline-block;border-left: 1px solid rgb(255 255 255 / 70%);}
.head_sns li:last-child {border-right: 1px solid rgb(255 255 255 / 70%);}
.head_sns li:last-child a {border-right: 1px solid rgb(255 255 255 / 70%);}
.head_sns li a {display: block;padding: 0 22px;border-left: 1px solid rgb(255 255 255 / 70%);text-align: center;line-height: 20px;}
.head_sns li img { width: auto; max-height: 12px; }
.head_sns li:hover img { -webkit-transform: rotate(-15deg); transform: rotate(-15deg); }
.head_layout { position: relative; display: flex; height: 92px; -webkti-display: flex; justify-content: space-between; -webkit-justify-content: space-between; align-items: center; -webkit-align-items: center; }
.web_head .logo:before {position: absolute;top: 0;left: 0;width: 160px;height: 13px;background-image: url(img/header_bg.png);background-size: 100%;content: "";-webkit-transform: translateY(-98%) translateX(-40px);transform: translateY(-98%) translateX(-40px);}
.head_layout.layout:after { display: none; }
.web_head .change-language { position: relative; display: inline-block; vertical-align: middle; text-align: left; }
.web_head .change-language .change-language-cont { border-radius: 3px; box-shadow: 1px 1px 3px rgba(0,0,0,.2); }
.web_head .logo { position: relative; display: flex; height: 100%; align-items: center; }
.header_right { display: flex; align-items: center; }
.web_head .btn--search { -webkit-box-sizing: content-box; box-sizing: content-box; margin-right: 20px; padding-right: 20px; width: 16px; height: 16px; border-right: 1px solid #1e1e1e; cursor: pointer; }
.web_head .btn--search:after { display: inline-block; width: 100%; height: 100%; background-image: url(img/web_icon.png); background-position: 0 0; background-repeat: no-repeat; content: ''; vertical-align: top; -webkit-transition: all .3s ease; -o-transition: all .3s ease; transition: all .3s ease; }
.web_head .btn--search:hover:after { background-position: 0 -30px; opacity: 1; -webkit-transform: none; -o-transform: none; transform: none; -ms-transform: none; }
.web_head .nav_wrap { position: relative; margin-right: 30px; }
.web_head .nav_wrap .head_nav { position: relative; }
.head_nav>li {position: relative;display: inline-block;margin: 0 20px;}
.head_nav>li.has-child a { padding-right: 20px; }
.head_nav>li>a { position: relative; display: inline-block; color: #1e1e1e; text-transform: uppercase; font-weight: 400; font-weight: 400; font-size: 14px; font-family: Roboto-Medium; line-height: 40px; }
.head_nav li a b { position: absolute; top: 50%; right: 0; display: inline-block; overflow: hidden; -webkit-box-sizing: content-box; box-sizing: content-box; width: 19px; height: 19px; text-align: center; line-height: 19px; -webkit-transition: all .3s; -o-transition: all .3s; transition: all .3s; -webkit-transform: translateY(-50%); transform: translateY(-50%); }
.head_nav li a b:before { content: '\f107'; font-family: fontawesome; }
.head_nav li ul { position: absolute; top: calc(100% + 5px); left: 0; display: block; padding: 10px; width: 250px; border-radius: 0 0 7px 7px; background: rgba(16,78,180,.75); opacity: 0; -webkit-transition: all ease-in-out .35s; transition: all ease-in-out .35s; -webkit-transform: scaleY(0); transform: scaleY(0); -webkit-transform-origin: top; transform-origin: top; }
.head_nav>li ul li {position: relative;padding: 3px;border-bottom: 1px solid #fff;font-size: 14px;line-height: 2;text-transform: capitalize;}
.head_nav>li ul li a { position: relative; display: block; color: #fff; }
.head_nav>li ul li a b { top: 12px; }
.head_nav>li ul li a b:before { content: '\f105'; }
.head_nav li ul ul { top: 0; left: 240px; margin-left: 2px; border-radius: 7px; }
.head_nav li.menu_show>ul { display: block; opacity: 1; -webkit-transform: scaleY(1); transform: scaleY(1); }
.head_nav>li:before { position: absolute; bottom: 0; left: 0; z-index: 0; width: 0; height: 2px; background: #3a8ac3; content: ""; transition: all ease-in-out .35s; }
.head_nav>li.nav-current>a, .head_nav>li:hover>a { position: relative; z-index: 1; color: #e1600d; }
.head_nav>li.nav-current:before, .head_nav>li:hover:before { width: 100%; }
.head_nav>li>a:hover b { -webkit-transform: rotateX(180deg); -o-transform: translateY(-50%) rotate(180deg); transform: translateY(-50%) rotate(180deg); -ms-transform: translateY(-50%) rotate(180deg); }
/*.fixed-nav-active {   opacity: 0;  -webkit-transform: translateY(-100%); -o-transform: translateY(-100%); transform: translateY(-100%); -ms-transform: translateY(-100%);-webkit-transition: all 0.6s ease; -o-transition: all 0.6s ease; transition: all 0.6s ease;
}
.fixed-nav-active.fixed-nav-appear{opacity: 1; position: fixed; top: 0; left: 0; z-index: 9; width: 100%;  background-color: rgba(255,255,255,.95); box-shadow: 0 0 15px rgba(0,0,0,.15);  -webkit-transform: none; -o-transform: none; transform: none; -ms-transform: none; }
.fixed-nav-active .head_layout{height: 80px;}
.fixed-nav-active .head_layout .logo img{ max-height:70px;    width: auto;}*/
/* web_footer */
.web_footer { position: relative; background: #32373c; }
.web_footer .layout { width: 1170px; }
.web_footer, .web_footer a { display: block; color: #efefef; text-align: left; text-transform: capitalize; font-size: 14px; font-family: Roboto-Regular; line-height: 26px; }
.foor_service { display: flex; padding: 50px 0; width: 100%; -webkit-display: flex; justify-content: space-between; -webkit-justify-content: space-between; flex-wrap: wrap; }
.foot_logo { position: relative; margin-bottom: 15px; }
.foot_logo img {width: auto;MAX-WIDTH: 80PX;}
.foor_service .foot_item .fot_tit { margin-bottom: 12px; font-size: 20px; font-family: Roboto-Bold; line-height: 1; }
.foot_item { padding-right: 90px; }
.foot_item.foot_contact_item { margin-top: -10px; }
.foot_item.foot_nav_item li a { position: relative; display: table; }
.foot_item.foot_nav_item li a:before { position: absolute; bottom: 0; left: 0; width: 0; height: 1px; background: #efefef; content: ""; opacity: .5; }
.foot_item.foot_nav_item li:hover a:before { width: 100%; }
.copyright { position: relative; display: block; padding: 25px 0 40px 0; background: #282d32; color: #fff; text-align: center; text-transform: uppercase; font-size: 14px; font-family: Roboto-Regular; line-height: 1; }
.tel_link { pointer-events: none; }
 @media screen and (max-width: 768px) {
.tel_link { pointer-events: auto; }
}
/*-------------------------------------------------------- 
														page: index
/*index_cate*/
.index_cates{margin-top: -75px;position: relative;z-index: 3;}
.index_cates .cate_items{display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-orient:horizontal;-webkit-box-direction:normal;-webkit-flex-direction:row;flex-direction:row;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;}
.index_cates .cate_item,.index_cates .cate_item a{color: #fff;}
.index_cates .cate_item,.index_cates .cate_item .item_cont{-webkit-transition: all .2s ease-in-out;-o-transition: all .2s ease-in-out;transition: all .2s ease-in-out;}
.index_cates .cate_item{display: block;width: 16.66666666%;float: left;background-color: #07b2dd;text-align: center;padding: 20px;min-height: 300px;background-position: center bottom;background-repeat: no-repeat;-webkit-background-size: 88% auto;background-size: 94% auto;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;flex-direction:column;position: relative;}
.index_cates .cate_item:hover{}
.index_cates .cate_item:nth-child(1){background-color: #07bcdd;}
.index_cates .cate_item:nth-child(2){background-color: #07b2dd;}
.index_cates .cate_item:nth-child(3){background-color: #019bcf;}
.index_cates .cate_item:nth-child(4){background-color: #0c88c5;}
.index_cates .cate_item:nth-child(5){background-color: #1472ba;}
.index_cates .cate_item:nth-child(6){background-color: #215caa;}
.index_cates .cate_item .item_tit{font-size: 22px;font-weight: normal;TEXT-TRANSFORM: CAPITALIZE;}
.index_cates .cate_item .item_subtit{font-size: 16px;font-weight: normal;}
.index_cates .cate_item .item_cont{padding-top: 20px;font-size: 12px;opacity: 0;}
.index_cates .cate_item .item_desc{font-size: 12px;margin-bottom: 30px;}
.index_cates .cate_item .sys_btn{display: block;font-size: 12px;padding: 8px;}														

@media screen and (min-width: 951px){
.index_cates .cate_item:hover{background-color: #16385e;background-image: none!important;background-repeat: no-repeat;background-position: center bottom;-webkit-transform: scale(1.3);-ms-transform: scale(1.3);-o-transform: scale(1.3);transform: scale(1.3);-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;z-index: 1;}
.index_cates .cate_item:hover .item_cont{opacity: 1;}
}


.web_main { position: relative; z-index: 2; overflow: hidden; }
/* swiper */
.swiper-container-fade { display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-orient: horizontal; -webkit-box-direction: normal; -webkit-flex-direction: row; flex-direction: row; }
.swiper-container-fade .swiper-slide { height: auto; }
/*slider_banner*/
.slider_banner .swiper-wrapper { }
.slider_banner .swiper-slide img { width: 100%; min-height: 150px; }
.slider_banner .swiper-slide img+img { position: absolute; top: 0; left: 0; }
.slider_banner .swiper-slide img { width: 100%; transition: 3s linear; transform: scale(1.08); }
.slider_banner .swiper-slide.swiper-slide-active img { transform: scale(1); }
.slider_banner .slider_swiper_control { position: absolute; top: 50%; left: 0; z-index: 2; width: 100%; height: 54px; -webkit-transform: translateY(-50%); transform: translateY(-50%); }
.slider_banner .swiper-button-white { position: static; position: relative; right: auto; left: auto; display: inline-block; overflow: hidden; margin: auto; width: 60px; height: 90px; border-radius: 0; background-color: #e1600d; background-position: center; background-repeat: no-repeat; vertical-align: middle; text-indent: -9999px; opacity: 1; filter: alpha(opacity=100); }
.slider_banner .swiper-pagination { position: absolute; bottom: 38px !important; display: inline-block; width: 100%; height: 8px; color: #fff; vertical-align: middle; display: none; }
.slider_banner .swiper-pagination span { vertical-align: middle; }
.slider_banner .swiper-pagination i { display: inline-block; margin: 0 8px; width: 6px; height: 6px; background-color: #fff; vertical-align: middle; }
.slider_banner .swiper-pagination-bullet:before { display: none; }
.slider_banner .swiper-pagination-bullet { display: inline-block; box-sizing: border-box; margin-right: 23px; width: 41px; height: 8px; background: #fff; color: #fff; text-align: center; }
.slider_banner .swiper-pagination-bullet.swiper-pagination-bullet-active { background: #e1600d; }
.banner_button_prev, .banner_button_next { position: absolute; top: 0; display: block; width: 54px; height: 54px; border-radius: 50%; background: rgba(255,255,255,.5); vertical-align: middle; text-align: center; cursor: pointer; -webkit-transition: all ease-in-out .35s; transition: all ease-in-out .35s; }
.banner_button_prev { left: 76px; }
.banner_button_next { right: 76px; }
.banner_button_prev:before, .banner_button_next:before { position: absolute; top: 0; left: 0; width: 54px; height: 54px; background: url(img/web_icon.png) no-repeat; content: ""; -webkit-transition: all ease-in-out .35s; transition: all ease-in-out .35s; }
.banner_button_next:before { background-position: -54px -60px; }
.banner_button_prev:before { background-position: 0 -60px; }
.banner_button_next:hover, .banner_button_prev:hover { background-color: #e1600d; }
.banner_button_next:hover:before { background-position: -54px -114px; }
.banner_button_prev:hover:before { background-position: 0 -114px; }
/*=======idnex public=============*/ 
.index_hd { position: relative; display: block; margin-bottom: 42px; text-align: center; line-height: 1; }
.hd_title { position: relative; display: block; margin-bottom: 17px; color: #e1600d; text-transform: uppercase; font-weight: 400; font-size: 55px; font-family: Roboto-Bold; }
.sub_tit { color: #3d3e3e; font-size: 18px; font-family: Roboto-Regular; }
.banner_down {position: absolute;bottom: 37px;left: 50%;z-index: 9999;display: block;width: 110px;height: 110px;border-radius: 50%;background: #fff;cursor: pointer;-webkit-transform: translateY(100%) translateX(-50%);transform: translateY(100%) translateX(-50%);display: none;}
.banner_down:before { display: block; color: #161616; content: "\f107"; text-align: center; font-weight: 700; font-size: 20px; font-family: fontawesome; line-height: 45px; }
.index_project_container .index_hd { display: table; margin: auto; text-align: left; }
.index_project_container .hd_title, .index_project_container .sub_tit { color: #fff; }
/*company_information_wrap*/
.company_information_wrap { position: relative; background: #f3f3f4; padding: 75px 0 83px 0; }
.company_tit { color: #e1600d; font-size: 40px; line-height: 1; text-transform: uppercase; font-family: "Roboto-Bold"; font-weight: normal; padding-bottom: 23px; border-bottom: 1px dashed #d2d2d2; position: relative; margin-bottom: 32px; }
.company_tit:after { content: ""; width: 150px; height: 1px; background: #e1600d; position: absolute; left: 0; bottom: -1px; }
.company_information { position: relative; width: 50%; padding-left: 15px; float: right; }
.company_information .company_img { overflow: hidden; }
.company_information:hover .company_img img { transform: scale(1.2); -webkit-transform: scale(1.2); }
.company_information .about_text { position: relative; font-size: 14px; font-family: "Roboto-Regular"; line-height: 30px; color: #606060; font-weight: normal; margin-top: 17px; }
.company_information .about_btn { position: relative; display: table; font-size: 18px; line-height: 47px; padding: 0 50px; color: #fff; background: #e1600d; text-transform: capitalize; font-weight: normal; font-family: "Roboto-Regular"; margin-top: 35px; }
.company_information .about_btn:hover { background: #fff; color: #e1600d; }
.company_news { position: relative; width: 50%; padding-right: 15px; float: left; }
.company_news .company_new { line-height: 20px; position: relative; padding: 22px 0; border-bottom: 1px dashed #d2d2d2; font-size: 0; }
.company_new time { position: relative; color: #222222; font-size: 20px; font-weight: normal; font-family: "Roboto-Bold"; width: 185px; display: inline-block; z-index: 2; height: 20px; vertical-align: middle; }
.company_new .new_info { position: relative; color: #606060; font-size: 14px; height: 20px; overflow: hidden; display: inline-block; width: calc(100% - 185px); z-index: 2; vertical-align: middle; }
.company_news .company_new:first-child { padding-top: 0; }
.company_new:after { content: ""; height: 2px; width: 0; background: #e1600d; position: absolute; bottom: -1px; left: 0; z-index: 0; }
.company_new:hover:after { width: 100%; }
.company_new:hover .new_info, .company_new:hover time { color: #e1600d; }
/*index_inquiry_wrap*/
.index_inquiry_wrap {position: relative;padding: 34px 0 39px 0;background: #e0600e;}
.index_inquiry_wrap .inquiry_desc { position: relative; display: flex; margin: auto; width: 980px; color: #efefef; text-transform: none; font-weight: 400; font-size: 30px; font-family: Roboto-Bold; align-items: center; justify-content: space-between; -webkit-display: flex; -webkit-align-items: center; -webkit-justify-content: space-between; }
.index_inquiry_wrap .inquiry_btn {display: table;padding: 9px 30px;border-radius: 3px;background: #ffffff;color: #ce580d;font-size: 20px;font-family: Roboto-Regular;line-height: 29px;}
.index_inquiry_wrap .inquiry_btn i { position: relative; margin-left: 20px; padding-left: 20px; border-left: 2px solid #efefef; }
.index_inquiry_wrap .inquiry_btn i:before { content: "\f0a9"; font-size: 13px; font-family: fontawesome; }
.index_inquiry_wrap .inquiry_btn:hover { background: #fff; color: #e1600d; }
.index_inquiry_wrap .inquiry_btn:hover i { border-left: 2px solid #e1600d; }
/*feature_product_wrap*/
.index_feature_product { position: relative; margin-top: 90px; margin-bottom: 30px; }
.feature_tabs { display: block; margin-bottom: 23px; text-align: center; }
.feature_tabs .feature_tab { position: relative; font-size: 0; }
.feature_tab .feature_title { position: relative; display: inline-block; padding: 0 30px; color: #606060; font-size: 20px; font-family: "Roboto-Medium"; line-height: 38px; cursor: pointer; text-transform: capitalize; }
.feature_tab .feature_title.current { background: #e1600d; color: #fff; }
.feature_product_wrap { position: relative; }
.feature_items.feature_products_show { position: absolute; top: 0; left: 0; opacity: 0; -webkit-transition: all ease-in-out .5s; transition: all ease-in-out .5s; -webkit-transform: scaleY(0); transform: scaleY(0); }
.feature_items { position: relative; top: unset; left: unset; padding-bottom: 40px; width: 100%; height: 100%; opacity: 1; -webkit-transition: all ease-in-out .5s; transition: all ease-in-out .5s; -webkit-transform: scaleY(1); transform: scaleY(1); }
.feature_items .feature_item { position: relative; margin-top: 0!important; margin-bottom: 16px; }
.feature_items figure { position: relative; }
.feature_items figure .item_img { position: relative; display: block; overflow: hidden; }
.feature_item .img_shadow:after, .feature_item .img_shadow:before { position: absolute; width: 0; height: 0; background: rgba(37,97,239,.3); content: ""; -webkit-transition: all ease-in-out .5s; transition: all ease-in-out .5s; }
.feature_item .img_shadow:before { top: 0; right: 0; }
.feature_item .img_shadow:after { bottom: 0; left: 0; }
.feature_item:hover .img_shadow:after, .feature_item:hover .img_shadow:before { width: 100%; height: 100%; }
.feature_items .pd_tit { padding: 16px 0; text-align: center; }
.feature_items .pd_tit a { color: #606060; text-transform: capitalize; letter-spacing: .38px; font-weight: 400; font-size: 16px; font-family: Roboto-Regular; line-height: 20px; }
.feature_control_bar { position: absolute; bottom: 0; left: 0; width: 100%; text-align: center; opacity: 0; }
.feature_items:hover .feature_control_bar { opacity: 1; }
.feature_button_next, .feature_button_prev { z-index: 99; display: inline-block; margin: 0 5px; width: 40px; height: 40px; background: #333; color: #fff; text-align: center; font-size: 24px; line-height: 40px; cursor: pointer; }
.feature_button_next:before, .feature_button_prev:before { font-family: fontawesome; }
.feature_button_next:before { content: "\f105"; }
.feature_button_prev:before { content: "\f104"; }
.feature_button_prev { left: 0; }
.feature_button_next { right: 0; }
.feature_button_next:hover, .feature_button_prev:hover { background: #2561ef; }
.feature-button-disabled { opacity: 0; }
.feature-pagination { display: none; }
.feature_item:hover .item_img img { -webkit-transform: scale(1.2) rotate(-10deg); transform: scale(1.2) rotate(-10deg); }
.feature_items .pd_tit:hover a { color: #e1600d; }
/*index_project_container*/
.index_project_container { position: relative; padding-top: 42px; padding-bottom: 80px; }
.index_project_container .layout { padding: 0; width: 1170px; }
.index_project_container:before {position: absolute;top: 0;left: 0;width: 100%;height: 66.66%;background: #0f4eb3;content: "";}
.projectbottom_bg, .projecttop_bg { position: absolute; left: 0; height: 42px; background: #fff; }
.projecttop_bg { top: 0; width: 35.87%; }
.projectbottom_bg { top: 66.66%; margin-top: -42px; width: 90.6%; }
.projectbottom_bg:before, .projecttop_bg:before { position: absolute; top: 0; left: 100%; width: 49px; height: 42px; content: ""; }
.projecttop_bg:before { background-image: url(img/project_top.png); }
.projectbottom_bg:before { background-image: url(img/project_bottom.png); }
.project_items { display: flex; margin-top: 50px; margin-bottom: 40px; justify-content: space-between; flex-wrap: wrap; -webkit-display: flex; -webkit-justify-content: space-between; -webkit-flex-wrap: wrap; }
.project_items .project_item { position: relative; margin-bottom: 30px; width: calc((50% - 45px)/ 2); }
.project_item .item_img { position: relative; display: block; overflow: hidden; }
.project_item .item_img img {/* -webkit-filter: grayscale(100%); */-moz-filter: grayscale(100%);-ms-filter: grayscale(100%);-o-filter: grayscale(100%);/* filter: grayscale(100%); */filter: gray;}
.project_item .item_tit { position: absolute; top: 50%; left: 0; z-index: 9; width: 100%; text-align: center; text-transform: uppercase; letter-spacing: .38px; font-size: 20px; }
.project_item .item_tit a { color: #fff; font-weight: 400; font-family: Roboto-Bold; }
.project_items .project_item:nth-child(even) .item_tit a {color: white;}
.project_items .project_item:nth-child(6n), .project_items .project_item:nth-child(6n+1) { width: calc(50% - 15px); }
.project_items .project_item:nth-child(odd):before {position: absolute;top: 0;left: 0;z-index: 3;width: 100%;height: 100%;/* background: #0f4eb354; */content: "";mix-blend-mode: multiply;}
.project_item:hover .item_img img { -webkit-transform: scale(1.2); transform: scale(1.2); }
.advantage_items { position: relative; display: flex; justify-content: space-between; flex-wrap: wrap; }
.advantage_items .advantage_item { width: calc((100% - 100px)/3); }
.advantage_item figure { position: relative; font-size: 0; }
.advantage_item .ad_icon { width: 48px; height: 48px; border: 1px solid #606060; border-radius: 3px; text-align: center; margin-right: 34px; line-height: 48px; display: inline-block; vertical-align: top; }
.advantage_item .ad_icon img { width: auto; }
.advantage_item figcaption { width: calc( 100% - 82px); display: inline-block; }
.advantage_item .ad_tit { position: relative; margin-bottom: 9px; text-transform: capitalize; }
.advantage_item .ad_tit a { font-family: "Roboto-Bold"; font-weight: normal; color: #606060; font-size: 25px; line-height: 1; }
.advantage_item .ad_info { font-size: 14px; line-height: 22px; color: #606060; font-family: "Roboto-Regular"; }
.advantage_item:hover .ad_icon { transform: rotateY(360deg); -webkit-transform: rotateY(360deg); }
.advantage_item:hover .ad_tit a { color: #e1600d; }
/*----------------------------------page head---------------------------------*/	
/*public*/
.page_main { z-index: 1; overflow: visible; padding: 72px 0 30px 0; background: #FFF; }
/*sys_sub_head*/
.sys_sub_head { position: relative; z-index: 1; z-index: 1; text-align: center; }
.head_bn_item img { }
.path_bar { position: absolute; top: 50%; left: 50%; text-align: left; transform: translate(-50%, -50%); -webkit-transform: translate(-50%, -50%); }
.pagnation_title {font-size: 26px;font-family: "Roboto-Bold";font-weight: normal;text-transform: uppercase;color: #000;line-height: 1;}
.path_bar ul { font-size: 0; margin-bottom: 25px; }
.path_bar li { display: inline; letter-spacing: 0; }
.path_bar li, .path_bar li a { color: #fff; font-size: 25px; line-height: 1; font-family: "Roboto-Bold"; font-weight: normal; text-transform: uppercase; }
.path_bar li a:hover, .path_bar li.nav_current a { color: #e1600d; }
.path_bar li:before { position: relative; margin: 0 10px; content: '/'; vertical-align: baseline; }
.path_bar li:first-child:before { display: none; }
.mobile-head-items { display: none; }
/*----------------------------------aside---------------------------------*/	
.aside { float: left; width: 250px; }
.side-tit-bar { position: relative; }
.side-tit-bar .side-tit {position: relative;padding-left: 35px;background: #e0600e;color: #fff;font-weight: normal;font-size: 20px;line-height: 45px;}
.side-widget { position: relative; overflow: hidden; margin: 0 0 40px; }
.side-widget+.side-widget { padding-top: 20px; }
.side-cate { overflow: hidden; }
.side-cate li { position: relative; margin-bottom: 1px; font-size: 16px; line-height: 30px; }
.side-cate li a { position: relative; display: block; padding-left: 30px; }
.side-cate ul { display: none; overflow: visible !important; background-color: #fff; }
.side-cate li a { -webkit-transition: color .3s ease-in-out; -o-transition: color .3s ease-in-out; transition: color .3s ease-in-out; }
.side-cate li ul { padding-top: 8px; padding-bottom: 8px; }
.side-cate li li { font-size: 14px; }
.side-cate li+li { border-top: 1px solid #ddd; }
.side-cate li li a { margin-left: 30px; padding-top: 3px; padding-bottom: 3px; background: none; color: #425664; }
.side-cate li li li { font-size: 12px; }
.side-cate li a:hover, .side-cate li.active>a { color: #e1600d; }
.side-cate li a:hover:before, .side-cate li.active>a:before { background-color: #e1600d; }
.side-cate li .icon-cate { position: absolute; top: 0; right: 0; display: inline-block; overflow: hidden; width: 40px; height: 40px; border-radius: 0%; color: #616265; text-align: center; font-size: 10px; line-height: 40px; cursor: pointer; -webkit-transition: all .2s ease; -o-transition: all .2s ease; transition: all .2s ease; }
.side-cate li .icon-cate:before { padding-right: 1px; padding-left: 1px; }
.side-cate li .icon-cate.icon-cate-down:before { content: '\f067'; }
.side-cate li .icon-cate.icon-cate-up:before { content: '\f068'; }
.side-widget .cate-type-list>li { font-size: 14px; line-height: 30px; }
.side-widget .cate-type-list>li>a {margin-right: 0;padding-left: 15px;TEXT-TRANSFORM: CAPITALIZE;}
.side-widget .cate-type-list>li>a:before { position: absolute; top: 15px; left: 0; display: inline-block; width: 5px; height: 5px; background-color: #e0e0e0; content: ''; }
.side-cate li.has-child>a { padding-right: 40px; line-height: 40px; }
.side-cate li.nav-current>.icon-cate { color: #e1600d; }
.side-cate>li.nav-current>a, .side-cate>li.nav-current>.icon-cate { color: #e1600d; }
.side-cate li li.nav-current>a { color: #e1600d; }
.side-product-items { position: relative; margin-top: 8px; }
.side-product-items .items_content { position: relative; padding: 35px 0; }
.side_product_item { position: relative; display: block; overflow: hidden; width: 100%; }
.side_product_item figure { position: relative; display: table; overflow: hidden; width: 100%; }
.side_product_item .item-img, .side_product_item figcaption { display: table-cell; vertical-align: middle; }
.side_product_item .item-img { position: relative; -webkit-box-sizing: content-box; box-sizing: content-box; width: 70px; }
.side_product_item .item-img img { display: block; width: 100%; }
.side_product_item figcaption { padding-left: 15px; }
.side_product_item figcaption h3 { font-weight: normal; font-size: 14px; }
.side_product_item figcaption h3 a { display: block; display: -webkit-box; overflow: hidden; max-height: 3.2em; text-overflow: ellipsis; word-wrap: break-word; line-height: 1.6em; -ms-word-break: break-all; word-break: break-all; -webkit-line-clamp: 2; -webkit-box-orient: vertical; }
.side_product_item figcaption h3 a:hover, .side_product_item .item-img:hover + figcaption h3 a { color: #e1600d; }
.side_product_item:hover .item-img { border-color: #e1600d; }
.side-product-items .swiper-slide { display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-pack: center; -ms-flex-pack: center; -webkit-justify-content: center; justify-content: center; -webkit-box-align: center; -ms-flex-align: center; -webkit-align-items: center; align-items: center; -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; }
.side-product-items .side_slider { position: relative; overflow: hidden; -webkit-box-sizing: content-box; box-sizing: content-box; max-height: 480px; }
.side-product-items .side_slider .swiper-wrapper { -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; -ms-flex-direction: column; flex-direction: column; }
.side-product-items .side_product_item {height: 70px!important;}
.side-product-items .btn-prev, .side-product-items .btn-next { position: absolute; left: 0; z-index: 1; width: 100%; height: 32px; color: #ccd3d9; text-align: center; font-size: 38px; line-height: 32px; opacity: 1; cursor: pointer; filter: alpha(opacity=100); }
.side-product-items .swiper-button-disabled { color: #eee; cursor: default; }
.side-product-items .btn-prev:before, .side-product-items .btn-next:before { display: inline-block; -webkit-transform: scale(2.5, 1); -o-transform: scale(2.5, 1); transform: scale(2.5, 1); -ms-transform: scale(2.5, 1); }
.side-product-items .btn-prev { top: 0; }
.side-product-items .btn-prev:before { content: '\f106'; }
.side-product-items .btn-next { bottom: 0; }
.side-product-items .btn-next:before { content: '\f107'; }
.side-product-items .btn-prev:not(.swiper-button-disabled):hover, .side-product-items .btn-next:not(.swiper-button-disabled):hover { color: #000; }
.aside .side-bn { margin: 0 0 40px; width: 100%; }
.aside .side-bn img { width: 100%; }
/*----------------------------------main---------------------------------*/
.main {position: relative;float: right;padding-bottom: 50px;width: calc(100% - 350px);margin: 0 auto;}
.main .banner_section { margin-bottom: 25px; }
.page_main, .pagge_main a { color: #002140; }
.page_main a:hover { color: #e1600d; }
/*title*/
.main_hd { margin-bottom: 35px; text-align: center; }
.main_hd .page_title { font-size: 30px; }
.main_hd .page_title, .main_hd .page_title a { color: #595959; }
/*page*/
.page_bar { position: relative; margin: 50px 0 30px; height: 33px; text-align: center; font-size: 14px; line-height: 31px; }
.page_bar a, .page_bar span { position: relative; display: inline-block; margin: 0 0 0 8px; padding: 0 8px; min-width: 33px; border: 1px solid #333; background-color: #333; color: #fff; vertical-align: top; text-align: center; }
.page_bar a.current, .page_bar a:hover { border-color: #e1600d; background: #e1600d; color: #FFF; }
.page_bar span.current2 { padding-right: 8px; padding-left: 8px; }
/*-------------------------------------------------------- 
													page: product list
																	------------------------------------------------------*/
.items_list { position: relative; width: 100%; }
.items_list ul { margin: -10px -18px; -webkit-box-pack: center; -webkit-justify-content: center; -ms-flex-pack: center; justify-content: center; }
.items_list ul { margin: -2.5%; }
.items_list .product_item { display: -webkit-box; display: -webkit-flex; display: flex; padding: 2.5%; width: 25%; max-width: 25%; -webkit-box-flex: 0 0 25%; -webkit-flex: 0 0 25%; -ms-flex: 0 0 25%; flex: 0 0 25%; }
.items_list .product_item figure { display: -webkit-box; display: -webkit-flex; display: flex; overflow: hidden; width: 100%; border: 1px solid #e6e6e6; background-color: #fff; -webkit-box-flex: 1; -webkit-flex: 1; flex: 1; -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; -webkit-box-pack: end; -ms-flex-pack: end; -webkit-justify-content: flex-end; justify-content: flex-end; }
.items_list .product_item figcaption { padding: 25px 15px; text-align: center; }
.items_list .product_item .item_img, .items_list .product_item .item_img img { display: block; }
.items_list .product_item .item_img { border: 1px solid #e6e6e6; -webkit-box-flex: 1.0; -moz-flex-grow: 1; -webkit-flex-grow: 1; flex-grow: 1; }
.items_list .product_item .item_title { text-transform: uppercase; font-size: 12px; }
.items_list .product_item .item_title a {height: 3.7em;background-color: #484848;color: #fff;font-size: 12px;line-height: 2.7em;}
.items_list .product_item figure:hover { border-color: #ccc; box-shadow: 0 0 6px 0 rgba(0, 0, 0, .3); }
.items_list .product_item figure:hover .item_title a { background-color: #e1600d; color: #fff; }
/*-------------------------------------------------------- 
													page: single Product
																	------------------------------------------------------*/
/*product photos*/
.index_cates .layout{width: 1400px;}
.product-intro { position: relative; margin-bottom: 80px; }
.product-intro .page_title {display: block;margin-top: -3px;color: #333;text-transform: capitalize;font-weight: bold;font-size: 20px;line-height: 1;border-bottom: 1px solid #e1e1e1;/* padding-bottom: 28px; */margin-bottom: 23px;/* padding-top: 10px; */padding: 15px 0 28px;box-shadow: 4px 4px 4px 4px #00000000;}
.cloud-zoom-lens { border: 1px solid #eee; cursor: move; }
.cloud-zoom-title { position: absolute !important; top: 0px; padding: 3px; width: 100%; background-color: #000; color: #fff; text-align: center; font-weight: bold; font-size: 10px; }
.cloud-zoom-big { overflow: hidden; padding: 0px; background-color: #fff; }
.cloud-zoom-loading { padding: 3px; border: 1px solid #000; background: #222; color: white; }
.product-view .product-image.zoom_remove:after { position: absolute; bottom: 0; left: 0; z-index: 999; width: 100%; height: 100%; content: ""; }
.product-view { position: relative; float: left; width: 350px; }
.product-view .product-image {overflow: hidden;box-shadow: 0 0 6px 2px #0000001c;}
.product-view .product-image img { position: relative; width: 100%; }
.product-view .image-additional-wrap { position: relative; z-index: 999; margin-top: 24px; padding: 0 30px; }
.product-view .image-additional { position: relative; overflow: hidden; padding: 0 2px; }
.product-view .image-additional li { position: relative; float: left; display: -webkit-box; display: -webkit-flex; display: flex; overflow: visible; width: 100%; width: 20%; height: auto; border: 1px solid #8a8a8a; -webkit-box-orient: horizontal; -webkit-box-direction: normal; -webkit-flex-direction: row; flex-direction: row; }
.product-view .image-additional li:before { position: absolute; bottom: -5px; left: 40%; z-index: 1; display: block; width: 20%; height: 4px; border-radius: 6px; content: ''; -webkit-transition: all .2s ease-in-out; -o-transition: all .2s ease-in-out; transition: all .2s ease-in-out; }
.product-view .image-additional li.single { display: none; }
.product-view .image-additional li img { position: relative; display: block; margin: 0 auto; width: 100%; opacity: .4; -webkit-transition: all .3s ease-in-out; -o-transition: all .3s ease-in-out; transition: all .3s ease-in-out; filter: alpha(opacity=40); }
.product-view .image-additional li a { position: relative; display: block; display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-flex: 1; -webkit-flex: 1; flex: 1; -webkit-box-orient: horizontal; -webkit-box-direction: normal; -webkit-flex-direction: row; flex-direction: row; -webkit-box-align: center; -webkit-align-items: center; -ms-flex-align: center; align-items: center; }
.product-view .image-additional li.current { border: 2px solid #000; -bottom: 0; }
.product-view .image-additional li.current img { opacity: 1; filter: alpha(opacity=100); }
.product-view .image-additional img.popup { display: none; }
.ad_prompt { position: absolute; top: 50%; left: 0; display: block; display: none; margin: -20px 0 0; width: 100%; color: #e1600d; text-align: center; font-size: 20px; line-height: 40px; -webkit-animation: twinkling 1s infinite ease-in-out; animation: twinkling 1s infinite ease-in-out; }
.product-view .image-additional.swiper-container-horizontal>.swiper-pagination-bullets { display: none; }
.product-view .swiper-button-next, .product-view .swiper-button-prev { margin-top: -16px; width: 25px; height: 32px; background: none; font-size: 24px; line-height: 32px; opacity: 1; filter: alpha(opacity=100); }
.product-view .swiper-button-prev { left: 0; }
.product-view .swiper-button-next { right: 0; }
.product-view .swiper-button-next:before, .product-view .swiper-button-prev:before { width: 25px; height: 33px; background-image: url(img/web_icon.png); background-repeat: no-repeat; content: ""; -webkit-transition: all ease .35s; transition: all ease .35s; }
.product-view .swiper-button-next:before { background-position: right -93px; }
.product-view .swiper-button-prev:before { background-position: -250px -93px; }
.product-view .swiper-button-next:hover:before { background-position: right -126px; }
.product-view .swiper-button-prev:hover:before { background-position: -250px -126px; }
.product-view .swiper-button-next.swiper-button-disabled, .product-view .swiper-button-prev.swiper-button-disabled { opacity: 1; filter: alpha(opacity=100); }
.product-view:hover .swiper-button-prev, .product-view:hover .swiper-button-next { background: none; opacity: 1; filter: alpha(opacity=100); }
.product-view:hover .swiper-button-disabled { opacity: .3; filter: alpha(opacity=30); }
/*product summary*/
.product-summary { position: relative; float: right; width: calc(100% - 390px); color: #353333; }
.product-summary .short_tit { font-size: 18px; text-transform: capitalize; color: #545454; opacity: .8; line-height: 1; }
.product-summary .product-meta { color: #242424; font-weight: 300; font-size: 16px; line-height: 28px; }
.product-summary .product-meta .short_tit {position: relative;color: #000;text-transform: capitalize;font-weight: normal;font-size: 18px;line-height: 22px;font-weight: 500;}
.product-summary .product-meta p { margin: 27px 0; color: #333; font-size: 14.7px; line-height: 26px; }
.product-summary .product-meta li { display: table; padding: 2px 0; width: 100%; font-size: 13px; line-height: 1.6; }
.product-summary .product-meta li em { display: table-cell; width: 36%; color: rgba(36,36,36,.5); }
.product-summary .product-meta li .item-val { display: table-cell; word-wrap: break-word; -ms-word-break: break-all; word-break: break-all; }
.product-summary .product-meta br { display: none; height: 10px; }
.product-btn-wrap {position: relative;padding-top: 34px;text-align: left;}
.product-btn-wrap .email, .product-btn-wrap .pdf, .product-btn-wrap .buy {position: relative;display: inline-block;margin: 0 0 7px 7px;padding: 0 10px;border: 2px solid #ffb07e;background: #e0600e;color: #fff;vertical-align: top;text-transform: uppercase;font-size: 16px;line-height: 38px;border-radius: 5px;}
.product-btn-wrap .email:hover, .product-btn-wrap .pdf:hover, .product-btn-wrap .buy:hover { background: transparent; color: #e1600d; }
.product-btn-wrap .email:before, .product-btn-wrap .pdf:before { padding-right: 10px; font-size: 16px; }
.product-btn-wrap .email:before { content: '\f003'; }
.product-btn-wrap .pdf:before { content: '\f1c1'; }
.product-summary .share-this { margin: 45px 0 0; text-align: right; }
/* product detail */
.product-detail { position: relative; overflow: visible; }
.detail-tabs { position: relative; margin-bottom: -1px; background: #eaedf0; }
.detail-tabs .title { position: relative; position: relative; position: relative; float: left; display: inline-block; margin-right: -1px; padding: 1px 35px; border: 1px solid #ccd3d9; color: #959da7; color: rgba(0,33,64,.5); vertical-align: top; text-transform: uppercase; font-weight: bold; font-size: 18px; font-family: Times New Roman; line-height: 35px; cursor: pointer; }
.detail-tabs .title.current { position: relative; z-index: 2; margin-bottom: -1px; padding-bottom: 2px; border-bottom: none; background: #fff; color: rgba(0,33,64,1); }
.product-detail .tab-panel-content { display: block; overflow: hidden; padding: 10px 0; }
.product-detail .disabled { display: block !important; }
.tab-panel-wrap { margin-bottom: 60px; padding: 40px; border: 1px solid #ccd3d9; background-color: #fff; }
/*like product*/
.goods-may-like { position: relative; overflow: hidden; padding: 70px 0 30px 0; background-attachment: fixed; }
.goods-may-like .index_title_bar { position: relative; display: block; text-align: center; margin-bottom: 20px; }
.goods-may-like .index_title_bar .good_title { position: relative; text-transform: uppercase; font-weight: bold; font-size: 28px; line-height: 1; padding-bottom: 16px; }
.goods-may-like .index_title_bar .good_title span { color: #e1600d; }
.goods-may-like .index_title_bar .good_title:after { content: ""; width: 130px; height: 4px; background: #e1600d; position: absolute; left: 50%; bottom: 0; margin-left: -65px; }
.goods-may-like .index_title_bar p { color: #fff; line-height: 28px; font-size: 16px; font-family: "微软雅黑"; margin: 28px 0; }
.goods-may-like .swiper-pagination-bullets { margin-top: 23px; text-align: center; line-height: 1; }
.goods-may-like .swiper-pagination-bullet, .goods-may-like .swiper-pagination-bullet:before { width: 14px; height: 14px; }
.goods-may-like .product_item .item_img:hover img { -webkit-transition: all .3s ease-in-out; -o-transition: all .3s ease-in-out; transition: all .3s ease-in-out; -webkit-transform: scale(1.1); -o-transform: scale(1.1); transform: scale(1.1); -ms-transform: scale(1.1); }
/*----------------product-------------*/
.product_item { position: relative; }
.product_item figure { position: relative; height: 100%; }
.product_item .item_img { position: relative; display: block; overflow: hidden; -webkit-box-sizing: border-box; box-sizing: border-box; width: 100%; border-radius: 7px; }
.product_item .item_img a { position: absolute; top: 0; left: 0; z-index: 1; display: block; width: 100%; height: 100%; }
.product_item .item_img img { width: 100%; height: auto; }
.product_item figcaption { position: relative; padding: 0 1%; }
.product_item .item_title a { display: block; position: relative; padding: 10px 5px; color: #000; text-align: center; font-weight: 100; font-size: 16px; line-height: 1.5; }
.product_item:hover a { color: #e1600d; }
/*-------------------------------------------------------- 
													page: blog list
																	------------------------------------------------------*/
.blog_list { position: relative; width: 100%; }
.blog-item { position: relative; overflow: hidden; padding: 35px 20px; width: 100%; }
.blog-item:nth-child(2n+1) { background: #f9f9f9; }
.blog-item:before { position: absolute; bottom: 0; left: 0; width: 100%; height: 1px; border-top: 1px solid #ccc; border-bottom: 1px solid #ccc; content: ""; }
.blog-item:after { position: absolute; bottom: 0; left: 0; display: block; width: 0; height: 3px; background-color: #e1600d; content: ''; opacity: 0; filter: alpha(opacity=0); }
.blog-item:hover:after { width: 100%; opacity: 1; -webkit-transition: all 1s ease-in-out; -o-transition: all 1s ease-in-out; transition: all 1s ease-in-out; filter: alpha(opacity=100); }
.blog-item .item-img, .blog-item .item-img img { border-radius: 5px; }
.blog-item .item-img { position: relative; float: left; display: block; margin: 0 20px 0 0; padding: 5px; max-width: 200px; border: 1px solid #eee; background-color: #fff; }
.blog-item .item-img img { position: relative; width: 100%; }
.blog-item .item-info .item-title { overflow: hidden; height: 1.5em; font-weight: normal; font-size: 20px; line-height: 1.5em; }
.blog-item .item-info time { display: block; margin: 8px 0; font-size: 16px; }
.blog-item .item-info .item-detail { }
.blog-item .item-info .item-more { float: right; margin: 20px 0 0; color: #8f9395; }
.blog-item .item-info .item-more { position: relative; display: inline-block; padding: .2em .8em; color: #e1600d; text-transform: uppercase; line-height: 1.8; }
.blog-item .item-info .item-more:after { position: absolute; display: block; width: 100%; height: 2px; background-color: #e1600d; content: ''; -webkit-transition: all .4s ease; -o-transition: all .4s ease; transition: all .4s ease; }
.blog-item .item-info .item-more:before { top: 0; left: 0; }
.blog-item .item-info .item-more:after { right: 0; bottom: 0; }
.blog-item .item-info .item-more:hover:before, .blog-item .item-info .item-more:hover:after { width: 0; opacity: 0; }
.blog-item .item-info .item-more:hover { background-color: #e1600d; color: #fff; -webkit-transition-delay: .3s; -o-transition-delay: .3s; transition-delay: .3s; }
/*-------------------------------------------------------- 
													page: download list
																	------------------------------------------------------*/
.download_list { position: relative; width: 100%; }
.download-item { position: relative; overflow: hidden; margin: 0 0 10px; padding: 15px 0; border-bottom: 1px solid #eee; line-height: 30px; }
.download-item .item-img { max-width: 25px; }
.download-item .item-title { position: relative; display: inline-block; margin: 0 10px; vertical-align: top; font-weight: bold; font-size: 16px; *display: inline;
 *zoom: 1;
}
.download-item .item-button { position: relative; float: right; display: inline-block; padding: 0 15px; border: 1px solid #ccc; border-radius: 5px; background: #eee; vertical-align: top; line-height: 30px; cursor: pointer; *display: inline;
 *zoom: 1;
}
.download-item .item-button:before { margin: 0 10px 0 0; content: "\f019"; }
/*-------------------------------------------------------- 
													page: faq list
																	------------------------------------------------------*/
.faq_list { position: relative; width: 100%; }
.faq-item { position: relative; overflow: hidden; margin: 0 0 20px; padding: 0 25px; width: 100%; border: 1px solid #eee; }
.faq-item .faq-title { position: relative; padding: 15px 0; font-size: 16px; cursor: pointer; }
.faq-item .faq-title:before { position: absolute; right: 0; content: "\f067"; font-size: 14px; }
.faq-item .faq-title.show-title:before { content: '\f068'; }
.faq-item .faq-title h3 { font-weight: normal; }
.faq-item .faq-cont { display: none; margin: 0 0 25px; }
.faq-item:first-child .faq-cont { display: block; }
 @media only screen and (max-width: 768px) {
.inquiry-form .form-btn-wrapx .form-btn-submitx { top: 0 !important; }
}
 @media only screen and (max-width: 1680px) {
/*layout*/
body { min-width: 1300px; }
.layout { width: 1300px; }
}
 @media only screen and (max-width: 1440px) {
/*layout*/
.index_cates .layout{width: 92%;}
body { min-width: 1200px; }
.layout { width: 1200px; }
.head_nav > li {margin: 0 20px;}
}
 @media only screen and (max-width: 1366px) {
/*layout*/
body { min-width: 1150px; }
.layout { width: 1150px; }
.web_head .layout, .index_main .layout, .web_footer .layout { width: 1150px; padding: 0; }
/*header*/
.head_layout { height: 90px; }
.web_head .logo img { max-height: 60px; width: auto; }
.head_nav > li { margin: 0 15px; }
/*index*/
.index_hd { margin-bottom: 25px; }
.hd_title { margin-bottom: 13px; font-size: 45px; }
.sub_tit { font-size: 14px; }
.index_feature_product { margin-top: 50px; margin-bottom: 0; }
.feature_tab .feature_title { font-size: 16px; line-height: 2; }
.feature_items .feature_item { margin-bottom: 0; }
.feature_items .pd_tit { padding: 10px 1%; }
.feature_items .pd_tit a { font-size: 13px; line-height: 1.5; }
.index_project_container { padding-bottom: 50px; }
.index_project_container .layout { width: 1024px }
.index_project_container .index_hd { text-align: center; }
.project_items { margin : 20px 0; }
.project_items .project_item:nth-child(6n), .project_items .project_item:nth-child(6n+1) { width: calc(50% - 5px); }
.project_items .project_item { margin-bottom: 10px; width: calc((50% - 15px)/ 2); }
.project_item .item_img { max-height: 345px; }
.advantage_items .advantage_item { width: calc((100% - 60px)/3); }
.advantage_item .ad_tit a { font-size: 20px; }
.advantage_item .ad_info { font-size: 12px; line-height: 20px; }
.company_information_wrap { padding: 50px 0; }
/*list*/
.items_list .product_item { width: 33.33333333%; max-width: 33.33333333%; -webkit-box-flex: 0 0 33.33333333%; -webkit-flex: 0 0 33.33333333%; -ms-flex: 0 0 33.33333333%; flex: 0 0 33.33333333%; }
}
 @media only screen and (max-width: 1280px) {
/*layout*/
.index_cates .cate_item{width: 25%;}
body { min-width: unset; }
.layout, .web_head .layout, .index_main .layout, .web_footer .layout { width: 98%; }
/*footer*/
.index_inquiry_wrap .inquiry_desc { width: 950px; font-size: 24px; }
.index_inquiry_wrap .inquiry_btn { padding: 5px 20px; font-size: 16px; line-height: 24px; }
.foor_service { padding: 30px 0; }
.foot_item { padding-right: 50px; }
.copyright { padding: 15px 0; font-size: 12px; }
/*index*/
.index_project_container .layout { width: 80%; }
.company_tit { font-size: 30px; padding-bottom: 15px; margin-bottom: 20px; }
.company_news .company_new { padding: 15px 0; }
.company_new time { font-size: 16px; width: 150px; }
.company_new .new_info { font-size: 12px; width: calc(100% - 150px); }
.company_information .about_text { font-size: 12px; line-height: 18px; margin-top: 13px; max-height: 90px; overflow: hidden; }
.company_information .about_btn { font-size: 16px; padding: 0 30px; margin-top: 17px; }
.project_item .item_img { max-height: 325px; }
/*list*/
.main { padding-bottom: 0; width: calc(100% - 280px); }
.items_list ul { margin: -5px; }
.items_list .product_item { padding: 5px; }
.page_main { padding: 30px 0; }
.product-summary { width: calc(100% - 370px); }
.product-btn-wrap .email, .product-btn-wrap .pdf, .product-btn-wrap .buy { text-transform: capitalize; font-size: 12px; line-height: 30px; }
.pagnation_title { font-size: 28px; }
.path_bar li, .path_bar li a { font-size: 16px; }
.product-summary .product-meta p { margin: 15px 0; font-size: 13px; line-height: 1.5; }
.product-btn-wrap { padding-top: 50px; }
}
 @media only screen and (max-width:1200px) {
.project_item .item_img { max-height: 256px; }
.product-view { width: 40%; }
.product-summary { width: 57%; }
}
 @media only screen and (max-width: 950px) {
     .index_cates .cate_item{width: 100%;min-height:inherit;-webkit-background-size: 110px auto;background-size: 110px auto;margin-bottom: 1px;padding: 30px 30px 50px;}
	.index_cates .cate_item .item_cont{opacity: 1;}
	.index_cates .cate_item .item_desc{font-size: 16px;}
	.index_cates .cate_item .sys_btn{display: inline-block;font-size: 14px;padding: .8em 1.2em;}
body { min-width: 100%; }
.z10000 { position: relative; z-index: 100000; }
.mobile-body-mask { position: fixed; top: 0; left: 0; z-index: 999; width: 100%; height: 100vh; background: rgba(0, 0, 0, 0.6); }
.mobile-ico-close { position: absolute; top: 0; right: -35px; width: 30px; height: 30px; background: #fff url(img/mobile_close.png) center center no-repeat; background-size: 50% auto; cursor: pointer; }
.sub-content { position: relative; right: auto; z-index: 99999; display: block; border: none; border-radius: 0; box-shadow: none; }
.lang-more { display: none !important; }
.mobile-head-items { position: fixed; top: 0; left: 0; z-index: 99999; display: block; width: 100%; height: 25px; background: #161622; text-align: left; line-height: 25px; }
.mobile-head-item { float: left; width: 45px; }
.mobile-head-item.mobile-head-aside { float: right; }
.mobile-head-item .title { overflow: hidden; width: 100%; height: 25px; color: #FFF; text-align: center; line-height: 25px; cursor: pointer; -webkit-tap-highlight-color: rgba(0,0,0,0); }
.mobile-head-item .title a { position: relative; display: block; color: #FFF; }
.mobile-head-item.mobile-head-home .title a:before { content: '\f015'; }
.mobile-head-item.mobile-head-nav .title:before { content: '\f0c9'; }
.mobile-head-item.mobile-head-language .title:before { content: "\f1ab"; }
.mobile-head-item.mobile-head-search .title:before { content: "\f002"; }
.mobile-head-item.mobile-head-social .title:before { content: "\f007"; }
.mobile-head-item.mobile-head-aside .title:before { content: "\f060"; }
.mobile-head-item .main-content-wrap { top: 0; z-index: 99999; display: block; background: #FFF; }
.mobile-head-item .main-content-wrap .content-wrap { overflow-y: auto; padding: 15px 10px; height: 100%; background-color: #fff; }
.mobile-head-item .main-content-wrap.middle-content-wrap .content-wrap { overflow-y: hidden; }
.mobile-head-item .side-content-wrap { position: fixed; left: -70%; display: block; width: 70%; height: 100%; }
.mobile-head-item .middle-content-wrap { position: absolute; left: 0; visibility: hidden; padding: 20px 0; width: 100%; height: auto; opacity: 0; }
.mobile-head-item .middle-show-content-wrap { top: 30px; visibility: visible; opacity: 1; }
/* header */
.web_head .nav_wrap .head_nav, .web_head .change-language, .web_head .head-search, .head-search .search-attr, .web_head .logo:before, .header_right { display: none; }
.web_head { padding: 25px 0 0; height: auto; }
.web_head .logo { position: relative; display: block; width: 100%; text-align: center; height: auto; }
.web_head .nav_wrap { position: static; }
.head_phone { float: left; margin-right: 0; }
.head-search { position: relative; width: 100%; height: 35px; background: #e1600d; color: #fff; }
.head-search .search-ipt { width: 100%; line-height: 35px; height: 35px; padding-left: 1.5%; color: #fff; }
.head-search .search-btn { width: 35px; height: 35px; background-image: url(img/search_btn.png); background-position: 7px center; background-repeat: no-repeat; position: absolute; right: 0; top: 0; background-color: transparent; border: none; }
.search-ipt::-webkit-input-placeholder {
 color: #fff;
}
.search-ipt:-moz-placeholder {
color: #fff;
}
.search-ipt::-moz-placeholder {
color: #fff;
}
.search-ipt:-ms-input-placeholder {
 color: #fff;
}
/*nav */
.head_nav { width: 100%; }
.head_nav > li { display: block; }
.head_nav li { padding-top: 4px; padding-bottom: 4px; padding-left: 28px; }
.head_nav li, .head_nav li a, .head_nav>li ul li a, .head_nav li:hover a { color: #111; }
.head_nav li.has-child { margin-top: 0; margin-bottom: 0; padding-left: 28px; }
.head_nav li.has-child>a { margin-top: 3px; margin-right: 35px; margin-bottom: 3px; }
.head_nav li.has-child>ul { position: relative; top: auto; left: auto; display: block; margin-left: 0; padding: 0 0 0 10px; width: 100%; border-top: unset; box-shadow: unset; opacity: 1; -webkit-transform: scaleY(1); transform: scaleY(1); background: #fff; display: none; }
.head_nav li a { position: relative; display: block; line-height: 1.5; }
.head_nav li a:hover { color: inherit; }
.head_nav li em { display: block; overflow: hidden; height: 1.4em; }
.head_nav li li { font-size: 14px; }
.head_nav li li li { font-size: 12px; }
.head_nav li li a { color: #666; }
.head_nav li.has-child a b { position: absolute; top: 13px; right: -35px; display: inline-block; overflow: hidden; -webkit-box-sizing: content-box; box-sizing: content-box; width: 15px; height: 15px; border: 1px solid #111; border-radius: 2px; line-height: 15px; }
.head_nav li.has-child a b:before, .head_nav li.has-child a b:after { position: absolute; top: 50%; left: 3px; display: block; margin-top: -.5px; width: 9px; height: 1px; background-color: #111; content: ''; }
.head_nav li.has-child a b:after { -webkit-transform: rotate(90deg); -o-transform: rotate(90deg); transform: rotate(90deg); -ms-transform: rotate(90deg); }
.head_nav li.has-child a:hover b { border-color: #111; }
.head_nav>li { padding-top: 10px; padding-bottom: 10px; border-bottom: 1px solid #f2f2f2; }
.head_nav>li>a { position: relative; position: relative; margin-bottom: -1px; text-transform: uppercase; text-transform: uppercase; font-size: 16px; }
.head_nav li, .head_nav li.has-child { padding-left: 0; }
.head_nav li li, .head_nav li li.has-child { padding-left: 15px; }
.head_nav li.active>a>b:after { display: none; }
.web_head .nav_wrap { display: none; }
.head_nav>li ul li { border-bottom: none; }
.head_nav > li:before { display: none; }
/* footer */
.foot_item { padding-right: 10px; }
.foot_item.foot_contact_item { max-width: 40%; }
/*index*/
 
.banner_button_prev, .banner_button_next { display: none; }
.slider_banner .swiper-pagination { bottom: 10px !important; }
.slider_banner .swiper-pagination-bullet { margin-right: 10px; width: 25px; height: 5px; }
.index_hd { margin-bottom: 13px; }
.hd_title { font-size: 24px; }
.sub_tit { font-size: 12px; }
.index_feature_product { margin-top: 20px; }
.feature_control_bar { opacity: 1; margin-top: 20px; position: relative; bottom: unset; left: unset; }
.feature_button_next, .feature_button_prev { width: 30px; height: 30px; font-size: 20px; line-height: 30px; }
.feature_tab .feature_title { font-size: 14px; padding: 0 20px; }
.index_project_container .layout { width: 98%; }
.company_information_wrap { padding: 25px 0; }
.index_inquiry_wrap { padding: 20px 0; }
.index_inquiry_wrap .inquiry_desc { width: 90%; font-size: 18px; }
.advantage_items .advantage_item { width: calc((100% - 30px)/3); }
.advantage_item .ad_icon { width: 40px; height: 40px; margin-right: 15px; line-height: 40px; }
.advantage_item figcaption { width: calc( 100% - 55px); }
.advantage_item .ad_tit { margin-bottom: 5px; }
.company_new time { display: none; }
.company_new .new_info { width: 100%; line-height: 1.5; height: auto; }
.company_news .company_new { padding: 7px 0; }
.company_tit { font-size: 20px; padding-bottom: 7px; margin-bottom: 13px; }
.index_project_container { padding-bottom: 30px; }
/* aside */
.aside { display: none; }
.side-widget { margin-bottom: 35px; }
.side-product-items .items_content { margin-top: 10px; padding-top: 0; padding-bottom: 0; }
.side-product-items .btn-prev, .side-product-items .btn-next { display: none; }
.side-product-items .side_slider { padding-top: 0; padding-bottom: 0; max-height: inherit; }
.side-product-items .side_slider ul:after { clear: both; display: block; visibility: hidden; height: 0; content: ''; }
.side-product-items .side_slider .swiper-wrapper { display: block; margin: 0 -5px; width: auto; }
.side-product-items .side_product_item { float: left; display: block; width: 50%; height: auto!important; ; text-align: center; }
.side-product-items .side_product_item:nth-child(2n+1) { clear: left; }
.side-product-items .side_product_item figure { display: block; margin: 5px; padding: 3px; width: auto; border: 1px solid #eee; }
.side-product-items .side_product_item .item-img { float: none; display: block; width: 100%; border: 0; }
.side-product-items .side_product_item .item-img img { width: 100%; height: auto; border: 0; }
.side-product-items .side_product_item figcaption { display: block; padding: 8px; width: auto; }
.side-product-items .side_product_item figcaption .item_title { height: auto; text-transform: none; font-size: 12px; }
/* main */
.main { float: none; margin: 0 auto; width: auto; }
.product-item { width: 33.333%; }
.product-item .item-wrap { margin: 8px; }
.page_bar { margin-top: 20px; text-align: center; }
.path_bar .layout { width: auto; }
.sys_sub_head .layer_ft_bg { display: none; }
.path_bar ul { font-size: 0; margin-bottom: 15px; }
.path_bar li, .path_bar li a { font-size: 12px; }
.pagnation_title { font-size: 16px; }
/* list */
.main_hd { margin-bottom: 15px; text-align: center; }
.main_hd .page_title { font-size: 22px; }
.items_list>ul { margin: 0; }
/* product intro */
.page_title { border-bottom: 0; line-height: 1.1; }
.video_close { display: none; }
.image-additional li.image-item.current img { border: none; }
.product-view { float: none; margin: 0 auto 15px; width: 100%; }
.product-view .product-image, .single_product_items { display: none; }
.product-view .image-additional-wrap { width: 100%; margin-top: 0; }
.product-view .image-additional { margin: 0 -2px; padding: 0; background: none; }
.product-view .image-additional ul { width: 100%; }
.product-view .image-additional li { margin: 0; width: 33.33333333%; }
.product-view .image-additional li a { margin: 0 2px; padding: 0; border: 0; }
.product-view .image-additional li a:before { display: none; }
.product-view .image-additional li img { opacity: 1; filter: alpha(opacity=100); }
.product-view .image-additional li.current { border-bottom: unset; }
.product-view .image-additional li.current img { border-color: inherit; }
.product-view .image-additional li.current:before { display: none; }
.product-view .image-additional li.single { display: block; }
.product-view .image-additional li.current a:before, .product-view .image-additional li.current a:after { display: none; }
.product-view .swiper-button-next { right: 0; }
.product-view .swiper-button-prev { left: 0; }
.product-view .swiper-button-next, .product-view .swiper-button-prev { opacity: 1; filter: alpha(opacity=100); }
.product-view .swiper-button-next.swiper-button-disabled, .product-view .swiper-button-prev.swiper-button-disabled { opacity: .3; filter: alpha(opacity=30); }
/* product summary */
.product-intro { margin-bottom: 20px; }
.product-summary { position: relative; float: none; margin-left: 0; width: 100%; }
.product-summary .product-meta li .item-val { display: inline-block; }
.product-summary .share-this { float: none; margin: 30px 0; }
.product-summary .product-meta li { display: block; }
.product-summary .product-meta li em { display: inline; padding-right: 15px; color: #333; font-weight: bold; }
.product-btn-wrap { padding-top: 20px; text-align: left; }
.product-btn-wrap a { margin: 0 0 10px !important; }
.product-summary .share-this { text-align: left; }
/* product detail  */ 
.tab-panel-wrap { padding: 30px 0 0; }
.goods-may-like { padding: 50px 0 30px 0; }
.goods-may-like .index_title_bar .good_title { font-size: 22px; }
.goods-may-like .index_title_bar .good_title:after { width: 80px; height: 2px; margin-left: -40px; }
}
 @media only screen and (max-width: 768px) {
/*footer*/ 
/*index*/
.project_item .item_img { max-height: 210px; }
/*list*/
.items_list .product_item figcaption { padding: 12px; }
.head_bn_item img { min-width: 640px; }
.goods-may-like { padding: 30px 0; }
.goods-may-like .index_title_bar .good_title { font-size: 16px; }
.goods-may-like .index_title_bar .good_title:after { width: 40x; height: 2px; margin-left: -20px; }
.goods-may-like .index_title_bar p { line-height: 18px; font-size: 12px; margin: 10px 0; }
.product_item .item_title a { padding: 5px 0; font-size: 14px; line-height: 1.5; }
}
 @media only screen and (max-width: 640px) {
/*footer*/
.foot_item.foot_contact_item { max-width: unset; margin-top: 0; }
.foot_item { padding-right: 0; width: 100%; margin-bottom: 15px; }
/*index*/
.feature_tabs { margin-bottom: 10px; }
.feature_tab .feature_title { font-size: 12px; padding: 0 10px; }
.project_item .item_tit { text-transform: capitalize; letter-spacing: 0; font-size: 12px; }
.index_project_container:before { height: 50%; }
.projecttop_bg { display: none; }
.advantage_items .advantage_item { width: 100%; margin-bottom: 10px; }
.company_news, .company_information { width: 100%; padding-right: 0; padding-left: 0; float: none; }
.company_news { margin-bottom: 15px; }
.product-intro .page_title { margin-top: 0; font-size: 16px; line-height: 1.5; }
/* list */
.sys_sub_head .head_title h1 { font-size: 30px; }
.items_list .product_item { width: 50%; max-width: 50%; -webkit-box-flex: 0 0 50%; -webkit-flex: 0 0 50%; -ms-flex: 0 0 50%; flex: 0 0 50%; }
}
 @media only screen and (max-width: 480px) {
     .head_sns{
         display: none;
     }
     .index_cates{margin-top: 10px;}
	.index_cates .cate_item{padding: 20px 20px 40px;border-radius: 5px;margin-bottom: 5px;}
	.index_cates .cate_item .item_cont{padding-top: 15px;}
	.index_cates .cate_item .item_desc{font-size: 14px;}
	.index_cates .cate_item .sys_btn{padding: .5em 1em;}
.mobile-hide { display: none!important; }
.gotop { display: none !important; }
.service_item { width: 100%; }
.blog-item .item-img { width: 150px; }
.table_wrap { overflow-x: scroll; }
/* layout */
/*header*/
 /*index*/
.project_items .project_item { margin-bottom: 6px; width: calc((100% - 6px)/ 2); }
.project_items .project_item:nth-child(6n), .project_items .project_item:nth-child(6n+1) { width: 100%; }
.project_items .project_item:nth-child(odd):before { display: none; }
.project_item .item_img { max-height: unset; }
.project_item .item_tit a { color: yellow; }
.head_sns li a { padding: 0 7px; }
/*footer*/
/* aside */
.side-widget { margin: 0 0 30px; }
.side-tit-bar .side-tit { font-size: 16px; }
.side-cate li { font-size: 12px; }
.side-cate li .icon-cate { width: 30px; }
.side-cate li.has-child>a { padding: 10px 30px 10px 10px; line-height: 20px; }
.side-widget .cate-type-list>li { line-height: 20px; }
.side-widget .cate-type-list>li>a { margin-left: 10px; padding-top: 8px; padding-bottom: 8px; }
.side-product-items .side_product_item { float: none; width: 100%; }
.side-product-items .side_product_item figure { margin-right: 10px; margin-left: 10px; }
/* index */
.index_inquiry_wrap .inquiry_desc { width: 96%; font-size: 12px; }
/*main*/
.path_bar ul:before { width: 16px; height: 16px; background-size: contain; -webkit-background-size: contain; }
.path_bar li { padding-left: 5px; font-size: 12px; }
.path_bar li + li:before { margin-right: 5px; }
.main_banner .swiper-pagination-bullet { width: 8px; height: 8px; }
.page_bar { font-size: 12px; line-height: 22px; }
.page_bar a, .page_bar span { margin: 0 2px; min-width: 2em; border-radius: 2px; }
/*list*/
.main_hd .page_title { font-size: 20px; }
.items_list ul { margin: -2px; }
.items_list .product_item { padding: 2px; }
.items_list .product_item figure { position: relative; border: 0; }
.items_list .product_item figcaption { position: absolute; bottom: 0; left: 0; padding: 1px 0 0; width: 100%; }
.items_list .product_item .item_title { font-size: 12px; }
.items_list .product_item .item_title a { height: 2.5em; background-color: rgba(0,0,0,.7); line-height: 2.5em; }
/*blog*/
.blog-item { padding: 30px 5px; }
.blog-item .item-img { float: none; -webkit-box-sizing: border-box; box-sizing: border-box; margin-right: 0; width: auto; max-width: 70%; border: 1px solid #ddd; }
.blog-item .item-info .item-title { font-weight: normal; line-height: 1.2; }
.blog-item .item-info { padding-top: 15px; }
.blog-item .item-info .item-title { height: auto; font-size: 22px; }
.blog-item .item-info .item-detail { display: -webkit-box; overflow: hidden; overflow: hidden; max-height: 9em; text-overflow: ellipsis; line-height: 1.5em; -webkit-line-clamp: 6; -webkit-box-orient: vertical; }
.blog-item .item-info .item-more { float: none; }
.blog-item .item-info time { color: #888; font-size: 14px; }
/* product */
.items_list .share_this { position: static; float: none; padding-bottom: 15px; text-align: left; }
.product-intro { overflow: visible; }
.product-intro .page_title { font-size: 14px; }
.product-view { margin-right: -10px; margin-left: -10px; width: auto; }
.product-view .image-additional li { width: 100%; }
.product-view .image-additional li a { margin-right: 0; margin-left: 0; }
.product-view .image-additional li a:before, .product-view .image-additional li a:after { display: none; }
.product-view .swiper-button-prev, .product-view .swiper-button-next { width: 27px; height: 40px; font-size: 18px; line-height: 40px; opacity: .8; -webkit-transform: none; -o-transform: none; transform: none; -ms-transform: none; }
.product-view .swiper-button-prev { left: 0; padding-right: 2px; border-radius: 0 3px 3px 0; }
.product-view .swiper-button-next { right: 0; padding-left: 2px; border-radius: 3px 0 0 3px; }
.product-view .swiper-button-prev:before, .product-view .swiper-button-next:before { display: inline-block; -webkit-transform: scale(.7, 1); -o-transform: scale(.7, 1); transform: scale(.7, 1); -ms-transform: scale(.7, 1); background-image: none; }
.product-view .swiper-button-prev:before { content: '\f053'; }
.product-view .swiper-button-next:before { content: '\f054'; }
.goods-may-like .title { text-transform: none; }
.page_title { font-weight: normal; font-size: 20px; }
.product-summary { padding-top: 0; }
.product-summary .page_title { margin-bottom: 15px; font-weight: bold; font-size: 18px; }
.product-summary .product-meta { font-size: 12px; }
.product-summary .product-meta p { font-size: 12px; }
.product-summary .product-meta h3 { font-size: 16px; line-height: 1.3; }
.product-summary .product-meta li { font-size: 13px; }
.product-summary .share-this { margin: 20px 0 0; text-align: left; }
.product-btn-wrap { padding-top: 30px; }
.product-btn-wrap .email, .product-btn-wrap .pdf, .product-btn-wrap .buy { padding: 5px 10px; font-size: 12px; line-height: 20px; }
.product-detail { margin-top: 40px; }
.detail-tabs .title { padding: 1px 15px; font-size: 14px; }
.tab-panel-wrap { padding: 20px 0; border: 0; }
.goods-may-like .title { margin-top: 15px; margin-bottom: 15px; font-size: 14px; }
.goods-may-like .layer-bd { position: relative; padding: 0; }
.goods-may-like .swiper-slider { position: relative; }
.goods-may-like .product_item figure { padding: 1px; }
.goods-may-like .product_item .item_title { font-size: 14px; }
.goods-may-like .product_item .item_title a { padding-right: 0; padding-left: 0; }
.goods-may-like .swiper-control { position: static; }
.goods-may-like .swiper-button-prev, .goods-may-like .swiper-button-next { position: absolute; margin-top: -15px; margin-right: 0; margin-left: 0; width: 30px; height: 30px; background-color: rgba(0,0,0,.7); line-height: 30px; }
.goods-may-like .layer-bd .swiper-button-prev { left: 5px; }
.goods-may-like .layer-bd .swiper-button-next { right: 5px; }
}
 @media only screen and (max-width: 320px) {
.side_product_item { width: 100%; }
.foor_service { flex-direction: column; }
.foor_service .foot_item { margin-bottom: 10px; }
.foot_item.foot_Catalog_nav { width: 100%; }
.foot_item.foot_Catalog_nav ul li { width: 100%; flex: 0 0 100%; -webkit-flex: 0 0 100%; }
.foor_service .foot_item .fot_tit { margin-bottom: 5px; }
}
/*-------------------------------
			swiper-slide start
			----------------------------------*/
.slider_banner { position: relative; overflow: hidden; }
.swiper-container-no-flexbox .swiper-slide { float: left; }
.swiper-container-vertical>.swiper-wrapper { -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; -ms-flex-direction: column; flex-direction: column; }
.swiper-wrapper { position: relative; z-index: 1; display: -webkit-box; display: -webkit-flex; display: -ms-flexbox; display: flex; -webkit-box-sizing: content-box; box-sizing: content-box; width: 100%; height: 100%; -webkit-transition-property: -webkit-transform; -o-transition-property: transform; transition-property: -webkit-transform; transition-property: transform; transition-property: transform, -webkit-transform; }
.swiper-container-android .swiper-slide, .swiper-wrapper { -webkit-transform: translate3d(0, 0, 0); transform: translate3d(0, 0, 0); }
.swiper-container-multirow>.swiper-wrapper { -webkit-flex-wrap: wrap; -ms-flex-wrap: wrap; flex-wrap: wrap; }
.swiper-container-free-mode>.swiper-wrapper { margin: 0 auto; -webkit-transition-timing-function: ease-out; -o-transition-timing-function: ease-out; transition-timing-function: ease-out; }
.swiper-slide { position: relative; overflow: hidden; width: 100%; height: 100%; -webkit-transition-property: -webkit-transform; -o-transition-property: transform; transition-property: -webkit-transform; transition-property: transform; transition-property: transform, -webkit-transform; -webkit-flex-shrink: 0; -ms-flex-negative: 0; flex-shrink: 0; }
.swiper-invisible-blank-slide { visibility: hidden; }
.swiper-container-autoheight, .swiper-container-autoheight .swiper-slide { height: auto; }
.swiper-container-autoheight .swiper-wrapper { -webkit-transition-property: height, -webkit-transform; -o-transition-property: transform, height; transition-property: height, -webkit-transform; transition-property: transform, height; transition-property: transform, height, -webkit-transform; -webkit-box-align: start; -webkit-align-items: flex-start; -ms-flex-align: start; align-items: flex-start; }
.swiper-container-3d { -webkit-perspective: 1200px; perspective: 1200px; }
.swiper-container-3d .swiper-cube-shadow, .swiper-container-3d .swiper-slide, .swiper-container-3d .swiper-slide-shadow-bottom, .swiper-container-3d .swiper-slide-shadow-left, .swiper-container-3d .swiper-slide-shadow-right, .swiper-container-3d .swiper-slide-shadow-top, .swiper-container-3d .swiper-wrapper { -webkit-transform-style: preserve-3d; transform-style: preserve-3d; }
.swiper-container-3d .swiper-slide-shadow-bottom, .swiper-container-3d .swiper-slide-shadow-left, .swiper-container-3d .swiper-slide-shadow-right, .swiper-container-3d .swiper-slide-shadow-top { position: absolute; top: 0; left: 0; z-index: 10; width: 100%; height: 100%; pointer-events: none; }
.swiper-container-3d .swiper-slide-shadow-left { background-image: -webkit-gradient(linear, right top, left top, from(rgba(0, 0, 0, .5)), to(rgba(0, 0, 0, 0))); background-image: -webkit-linear-gradient(right, rgba(0, 0, 0, .5), rgba(0, 0, 0, 0)); background-image: -o-linear-gradient(right, rgba(0, 0, 0, .5), rgba(0, 0, 0, 0)); background-image: linear-gradient(to left, rgba(0, 0, 0, .5), rgba(0, 0, 0, 0)); }
.swiper-container-3d .swiper-slide-shadow-right { background-image: -webkit-gradient(linear, left top, right top, from(rgba(0, 0, 0, .5)), to(rgba(0, 0, 0, 0))); background-image: -webkit-linear-gradient(left, rgba(0, 0, 0, .5), rgba(0, 0, 0, 0)); background-image: -o-linear-gradient(left, rgba(0, 0, 0, .5), rgba(0, 0, 0, 0)); background-image: linear-gradient(to right, rgba(0, 0, 0, .5), rgba(0, 0, 0, 0)); }
.swiper-container-3d .swiper-slide-shadow-top { background-image: -webkit-gradient(linear, left bottom, left top, from(rgba(0, 0, 0, .5)), to(rgba(0, 0, 0, 0))); background-image: -webkit-linear-gradient(bottom, rgba(0, 0, 0, .5), rgba(0, 0, 0, 0)); background-image: -o-linear-gradient(bottom, rgba(0, 0, 0, .5), rgba(0, 0, 0, 0)); background-image: linear-gradient(to top, rgba(0, 0, 0, .5), rgba(0, 0, 0, 0)); }
.swiper-container-3d .swiper-slide-shadow-bottom { background-image: -webkit-gradient(linear, left top, left bottom, from(rgba(0, 0, 0, .5)), to(rgba(0, 0, 0, 0))); background-image: -webkit-linear-gradient(top, rgba(0, 0, 0, .5), rgba(0, 0, 0, 0)); background-image: -o-linear-gradient(top, rgba(0, 0, 0, .5), rgba(0, 0, 0, 0)); background-image: linear-gradient(to bottom, rgba(0, 0, 0, .5), rgba(0, 0, 0, 0)); }
.swiper-container-wp8-horizontal, .swiper-container-wp8-horizontal>.swiper-wrapper { -ms-touch-action: pan-y; touch-action: pan-y; }
.swiper-container-wp8-vertical, .swiper-container-wp8-vertical>.swiper-wrapper { -ms-touch-action: pan-x; touch-action: pan-x; }
.swiper-button-next, .swiper-button-prev { position: absolute; top: 50%; z-index: 2; margin-top: -25px; width: 36px; height: 50px; border-radius: 2px; background-color: rgba(0,0,0,.4); background-color: #ccc; text-align: center; font-size: 30px; line-height: 50px; opacity: 0; cursor: pointer; -webkit-transition: all 0.3s ease-in-out; -moz-transition: all 0.3s ease-in-out; -o-transition: all 0.3s ease-in-out; transition: all 0.3s ease-in-out; -ms-transition: all 0.3s ease-in-out; -webkit-tap-highlight-color: rgba(0,0,0,0); }
.swiper-button-next:before, .swiper-button-prev:before { display: inline-block; }
.swiper-container:hover .swiper-button-next, .swiper-container:hover .swiper-button-prev { }
.swiper-button-next.swiper-button-disabled, .swiper-button-prev.swiper-button-disabled { opacity: 0.3; cursor: auto;/* pointer-events:none; */ }
.swiper-button-prev, .swiper-container-rtl .swiper-button-next { right: auto; left: 10px; }
.swiper-button-next, .swiper-container-rtl .swiper-button-prev { right: 10px; left: auto; }
.swiper-button-prev:before, .swiper-container-rtl .swiper-button-next:before { content: '\f104'; }
.swiper-button-next:before, .swiper-container-rtl .swiper-button-prev:before { content: '\f105'; }
.swiper-button-lock { display: none; }
.swiper-pagination { position: absolute; z-index: 2; text-align: center; -webkit-transition: .3s opacity; -o-transition: .3s opacity; transition: .3s opacity; -webkit-transform: translate3d(0, 0, 0); transform: translate3d(0, 0, 0); }
.swiper-pagination.swiper-pagination-hidden { opacity: 0; }
.swiper-container-horizontal>.swiper-pagination-bullets, .swiper-pagination-custom, .swiper-pagination-fraction { margin-top: 30px; width: 100%; text-align: center; font-size: 0; }
.swiper-pagination-bullets-dynamic { overflow: hidden; font-size: 0; }
.swiper-pagination-bullets-dynamic .swiper-pagination-bullet { position: relative; -webkit-transform: scale(.33); transform: scale(.33); -ms-transform: scale(.33); }
.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active { -webkit-transform: scale(1); transform: scale(1); -ms-transform: scale(1); }
.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-main { -webkit-transform: scale(1); transform: scale(1); -ms-transform: scale(1); }
.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-prev { -webkit-transform: scale(.66); transform: scale(.66); -ms-transform: scale(.66); }
.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-prev-prev { -webkit-transform: scale(.33); transform: scale(.33); -ms-transform: scale(.33); }
.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-next { -webkit-transform: scale(.66); transform: scale(.66); -ms-transform: scale(.66); }
.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-next-next { -webkit-transform: scale(.33); transform: scale(.33); -ms-transform: scale(.33); }
.swiper-pagination-bullet, .swiper-pagination-bullet:before, .swiper-pagination-bullet:after { display: inline-block; width: 30px; height: 3px; vertical-align: top; }
.swiper-pagination-bullet { position: relative; overflow: hidden; cursor: pointer; }
.swiper-pagination-bullet:before { background-color: #828282; content: ''; }
.swiper-pagination-bullet-active:before { background-color: #e1600d; }
.swiper-pagination-clickable .swiper-pagination-bullet { cursor: pointer; }
.swiper-container-vertical>.swiper-pagination-bullets { top: 50%; right: 10px; -webkit-transform: translate3d(0, -50%, 0); transform: translate3d(0, -50%, 0); }
.swiper-container-vertical>.swiper-pagination-bullets .swiper-pagination-bullet { display: block; margin: 6px 0; }
.swiper-container-vertical>.swiper-pagination-bullets.swiper-pagination-bullets-dynamic { top: 50%; width: 8px; -webkit-transform: translateY(-50%); transform: translateY(-50%); -ms-transform: translateY(-50%); }
.swiper-container-vertical>.swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet { display: inline-block; -webkit-transition: .2s top, .3s -webkit-transform; -o-transition: .2s transform, .3s top; transition: .2s top, .3s -webkit-transform; transition: .2s transform, .3s top; transition: .2s transform, .3s top, .3s -webkit-transform; }
.swiper-container-horizontal>.swiper-pagination-bullets .swiper-pagination-bullet { margin: 0 5px; }
.swiper-container-horizontal>.swiper-pagination-bullets.swiper-pagination-bullets-dynamic { left: 50%; white-space: nowrap; -webkit-transform: translateX(-50%); transform: translateX(-50%); -ms-transform: translateX(-50%); }
.swiper-container-horizontal>.swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet { -webkit-transition: .2s left, .3s -webkit-transform; -o-transition: .2s transform, .3s left; transition: .2s left, .3s -webkit-transform; transition: .2s transform, .3s left; transition: .2s transform, .3s left, .3s -webkit-transform; }
.swiper-container-horizontal.swiper-container-rtl>.swiper-pagination-bullets-dynamic .swiper-pagination-bullet { -webkit-transition: .2s right, .3s -webkit-transform; -o-transition: .2s transform, .3s right; transition: .2s right, .3s -webkit-transform; transition: .2s transform, .3s right; transition: .2s transform, .3s right, .3s -webkit-transform; }
.swiper-pagination-progressbar { position: absolute; background: rgba(0, 0, 0, .25); }
.swiper-pagination-progressbar .swiper-pagination-progressbar-fill { position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: #007aff; -webkit-transform: scale(0); transform: scale(0); -webkit-transform-origin: left top; transform-origin: left top; -ms-transform: scale(0); -ms-transform-origin: left top; }
.swiper-container-rtl .swiper-pagination-progressbar .swiper-pagination-progressbar-fill { -webkit-transform-origin: right top; transform-origin: right top; -ms-transform-origin: right top; }
.swiper-container-horizontal { overflow: hidden; }
.swiper-container-horizontal>.swiper-pagination-progressbar { top: 0; left: 0; width: 100%; height: 4px; }
.swiper-container-vertical>.swiper-pagination-progressbar { top: 0; left: 0; width: 4px; height: 100%; }
.swiper-pagination-progressbar.swiper-pagination-white { background: rgba(255, 255, 255, .25); }
.swiper-pagination-progressbar.swiper-pagination-white .swiper-pagination-progressbar-fill { background: #fff; }
.swiper-pagination-black .swiper-pagination-bullet-active { background: #000; }
.swiper-pagination-progressbar.swiper-pagination-black { background: rgba(0, 0, 0, .25); }
.swiper-pagination-progressbar.swiper-pagination-black .swiper-pagination-progressbar-fill { background: #000; }
.swiper-pagination-lock { display: none; }
.swiper-scrollbar { position: relative; border-radius: 10px; background: rgba(0, 0, 0, .1); -ms-touch-action: none; }
.swiper-container-horizontal>.swiper-scrollbar { position: absolute; bottom: 3px; left: 1%; z-index: 50; width: 98%; height: 5px; }
.swiper-container-vertical>.swiper-scrollbar { position: absolute; top: 1%; right: 3px; z-index: 50; width: 5px; height: 98%; }
.swiper-scrollbar-drag { position: relative; top: 0; left: 0; width: 100%; height: 100%; border-radius: 10px; background: rgba(0, 0, 0, .5); }
.swiper-scrollbar-cursor-drag { cursor: move; }
.swiper-scrollbar-lock { display: none; }
.swiper-zoom-container { display: -webkit-box; display: -webkit-flex; display: -ms-flexbox; display: flex; width: 100%; height: 100%; text-align: center; -webkit-box-pack: center; -webkit-justify-content: center; -ms-flex-pack: center; justify-content: center; -webkit-box-align: center; -webkit-align-items: center; -ms-flex-align: center; align-items: center; }
.swiper-zoom-container>canvas, .swiper-zoom-container>img, .swiper-zoom-container>svg { max-width: 100%; max-height: 100%; -o-object-fit: contain; object-fit: contain; }
.swiper-slide-zoomed { cursor: move; }
.swiper-lazy-preloader { position: absolute; top: 50%; left: 50%; z-index: 10; margin-top: -21px; margin-left: -21px; width: 42px; height: 42px; -webkit-transform-origin: 50%; transform-origin: 50%; -ms-transform-origin: 50%; -webkit-animation: swiper-preloader-spin 1s steps(12, end) infinite; animation: swiper-preloader-spin 1s steps(12, end) infinite; }
.swiper-lazy-preloader:after { display: block; width: 100%; height: 100%; background-position: 50%; background-size: 100%; background-repeat: no-repeat; content: ''; }
.swiper-lazy-preloader-white:after { }
 @-webkit-keyframes swiper-preloader-spin { 100% {
 -webkit-transform: rotate(360deg);
 transform: rotate(360deg);
}
}
 @keyframes swiper-preloader-spin { 100% {
 -webkit-transform: rotate(360deg);
 transform: rotate(360deg);
}
}
.swiper-container .swiper-notification { position: absolute; top: 0; left: 0; z-index: -1000; opacity: 0; pointer-events: none; }
.swiper-container-fade.swiper-container-free-mode .swiper-slide { -webkit-transition-timing-function: ease-out; -o-transition-timing-function: ease-out; transition-timing-function: ease-out; }
.swiper-container-fade .swiper-slide { -webkit-transition-property: opacity; -o-transition-property: opacity; transition-property: opacity; pointer-events: none; }
.swiper-container-fade .swiper-slide .swiper-slide { pointer-events: none; }
.swiper-container-fade .swiper-slide-active, .swiper-container-fade .swiper-slide-active .swiper-slide-active { pointer-events: auto; }
.swiper-container-cube { overflow: visible; }
.swiper-container-cube .swiper-slide { z-index: 1; visibility: hidden; width: 100%; height: 100%; -webkit-transform-origin: 0 0; transform-origin: 0 0; pointer-events: none; -webkit-backface-visibility: hidden; backface-visibility: hidden; -ms-transform-origin: 0 0; }
.swiper-container-cube .swiper-slide .swiper-slide { pointer-events: none; }
.swiper-container-cube.swiper-container-rtl .swiper-slide { -webkit-transform-origin: 100% 0; transform-origin: 100% 0; -ms-transform-origin: 100% 0; }
.swiper-container-cube .swiper-slide-active, .swiper-container-cube .swiper-slide-active .swiper-slide-active { pointer-events: auto; }
.swiper-container-cube .swiper-slide-active, .swiper-container-cube .swiper-slide-next, .swiper-container-cube .swiper-slide-next+.swiper-slide, .swiper-container-cube .swiper-slide-prev { visibility: visible; pointer-events: auto; }
.swiper-container-cube .swiper-slide-shadow-bottom, .swiper-container-cube .swiper-slide-shadow-left, .swiper-container-cube .swiper-slide-shadow-right, .swiper-container-cube .swiper-slide-shadow-top { z-index: 0; -webkit-backface-visibility: hidden; backface-visibility: hidden; }
.swiper-container-cube .swiper-cube-shadow { position: absolute; bottom: 0; left: 0; z-index: 0; width: 100%; height: 100%; background: #000; opacity: .6; -webkit-filter: blur(50px); filter: blur(50px); }
.swiper-container-flip { overflow: visible; }
.swiper-container-flip .swiper-slide { z-index: 1; pointer-events: none; -webkit-backface-visibility: hidden; backface-visibility: hidden; }
.swiper-container-flip .swiper-slide .swiper-slide { pointer-events: none; }
.swiper-container-flip .swiper-slide-active, .swiper-container-flip .swiper-slide-active .swiper-slide-active { pointer-events: auto; }
.swiper-container-flip .swiper-slide-shadow-bottom, .swiper-container-flip .swiper-slide-shadow-left, .swiper-container-flip .swiper-slide-shadow-right, .swiper-container-flip .swiper-slide-shadow-top { z-index: 0; -webkit-backface-visibility: hidden; backface-visibility: hidden; }
.swiper-container-coverflow .swiper-wrapper { -ms-perspective: 1200px; }
 @media screen and (max-width: 480px) {
.swiper-pagination-bullet { margin: 0 5px; width: 10px; height: 10px; }
}
/*ie*/
.swiper-container { overflow: hidden\9; }
.slider_banner .swiper-container .swiper-wrapper { width: 2000%\9; }
.swiper-slide { float: left\9; }
 @media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
.swiper-container .swiper-wrapper { width: auto; }
}
/*-------------------------------
			swiper-slide end
			----------------------------------*/
/*-------------------------------
			entry-table start
			----------------------------------*/	
.entry { padding: 15px 0; }
.entry { color: #666; }
.entry a { color: #0030FF; }
.entry p {margin: 0;padding: 8px 0;font-size: 16px;line-height: 1.8;font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;}
.entry table { margin-bottom: 20px; width: 100%; border-collapse: collapse; border-spacing: 0; }
.entry th, .entry td { padding: 0 15px; height: 30px; border: 1px solid #ccc; background: #fbfbfb; color: #666; text-align: left; font-size: 12px; line-height: 1.5; }
.entry th { background: #eee; font-size: 12px; }
.entry td p, .entry th p, .pd-panel .entry td p, .pd-panel .entry th p { margin: 0; padding: 5px; color: #666; font-size: 12px; line-height: 1.3; }
.entry td.amts { color: #cb2027; }
.entry th#cartDelete { text-align: center; }
.entry .button { margin: 15px 10px 0 0; padding: 8px 15px; border: 0; background: #dfdfdf; color: #777; text-transform: Capitalize; font-weight: bold; line-height: 1.2; cursor: pointer; }
.entry .button:hover { background: #cb2027; color: #fff; }
.entry .deletecartitem { width: 50px; text-align: center; }
.entry .deletecartitem label { display: none; }
.entry .stotal td.amts { font-weight: bold; font-size: 18px; }
.entry img.alignleft { float: left; margin: 20px 15px 0 0; }
.entry img.alignright { float: right; margin: 20px 0 0 15px; }
.entry img.alignleftnopad { float: left; margin-left: 0; }
.entry img.alignrightnopad { float: right; margin-right: 0; }
.entry img.aligncenter { display: block; margin: 20px auto 0 auto; text-align: center; }
.entry img { max-width: 100%; }
.cartitem a { color: #292929; font-weight: bold; }
.inquiry-form ul li { background: none; }
.entry .inquiry-form-wrap { margin: 20px 0 0; }
.table_wrap { position: relative; display: block; }
.main table { position: relative; margin-bottom: 0 !important; width: 100%; border-collapse: collapse; border-spacing: 0; }
.main table th, .main table td { padding: 0 15px; height: 30px; border: 1px solid #ccc; background: #fbfbfb; color: #666; text-align: left; font-size: 12px; line-height: 1.5; }
.main table th { background: #eee; font-size: 12px; }
.main table td p, .main table th p, table td p, table th p { margin: 0; padding: 5px; color: #666; font-size: 12px; line-height: 1.3; }
.main table td.amts { color: #cb2027; }
.main table th#cartDelete { text-align: center; }
/*-------------------------------
			entry-table end
			----------------------------------*/
/*-------------------------------
			search  start
			----------------------------------*/
.container { position: relative; min-height: 100vh; background-position: left bottom; background-repeat: no-repeat; }
.search-ipt { border: 0; border-radius: 0; background: transparent; -webkit-appearance: none; }
.web-search { display: flex; visibility: hidden; text-align: center; flex-direction: column; justify-content: center; align-items: center; }
.web-search .btn--search-close { position: absolute; top: 1.25em; right: 1.25em; display: inline-block; display: none; width: 45px; height: 45px; border-radius: 50%; background-color: #ccc; background-image: url(img/search_btn_close.png); background-position: center; background-size: auto 35%; background-repeat: no-repeat; font-size: 1.8em; line-height: 45px; cursor: pointer; -webkit-transition: transform .3s ease; -o-transition: transform .3s ease; transition: transform .3s ease; -webkit-background-size: auto 35%; }
.web-search .btn--search-close:hover { background-color: #e1600d; -webkit-transform: rotate(90deg); -o-transform: rotate(90deg); transform: rotate(90deg); -ms-transform: rotate(90deg); }
.js .btn--search-close { display: block; }
.web-search .search-ipt { display: inline-block; box-sizing: border-box; padding: 0.05em 0; width: 75%; border-bottom: 3px solid; color: #333; font-size: 20px; font-family: inherit; line-height: 1em; }
 .web-search .search-ipt::-webkit-input-placeholder {
 color: #333;
 font-weight: bold;
}
 .web-search .search-ipt::-moz-placeholder {
 color: #333;
 font-weight: bold;
 opacity: 1;
}
 .web-search .search-ipt:-ms-input-placeholder {
 color: #333;
 font-weight: bold;
}
 .web-search .search-ipt::-webkit-search-cancel-button, .web-search .search-ipt::-webkit-search-decoration {
 -webkit-appearance: none;
}
 .web-search .search-ipt::-ms-clear {
 display: none;
}
.web-search .search-attr { display: block; margin: 0 auto; padding: 0.85em 0; width: 75%; color: #333; text-align: right; font-size: 90%; }
.js .container, .js .web-search .search-ipt { -webkit-transition: all .3s ease-in-out; -moz-transition: all .3s ease-in-out; -o-transition: all .3s ease-in-out; transition: all .3s ease-in-out; -ms-transition: all .3s ease-in-out; }
.js .container { position: relative; }
.js .main-wrap--move { min-height: 100vh; }
.js .web-search { pointer-events: none; }
.web-search { position: fixed; top: 0; left: 0; z-index: 100001; margin: auto; padding: 25px 0; width: 100%; background-color: #fff; -webkit-transition: all .3s ease; -o-transition: all .3s ease; transition: all .3s ease; -webkit-transform: translateY(-100%); -o-transform: translateY(-100%); transform: translateY(-100%); perspective: 1200px; -ms-transform: translateY(-100%); }
.web-search .btn--search-close { opacity: 0; -webkit-transition: opacity 0.3s ease; -o-transition: opacity 0.3s ease; transition: opacity 0.3s ease; }
.search__suggestion, .web-search .search-attr { opacity: 0; -webkit-transition: opacity 0.8s, transform 0.8s; -moz-transition: opacity 0.8s, transform 0.8s; -o-transition: opacity 0.8s, transform 0.8s; transition: opacity 0.8s, transform 0.8s; -webkit-transform: translate3d(-15%, 0, 0); -moz-transform: translate3d(-15%, 0, 0); -o-transform: translate3d(-15%, 0, 0); transform: translate3d(-15%, 0, 0); -ms-transform: translate3d(-15%, 0, 0); -ms-transition: opacity 0.8s, transform 0.8s; }
.web-search .search-attr { padding-bottom: 0; font-size: 20px; }
.js .web-search .search-ipt { padding: 0 0 15px; text-transform: uppercase; font-weight: bold; font-size: 60px; opacity: 0; -webkit-transform: scale3d(0, 1, 1); -moz-transform: scale3d(0, 1, 1); -o-transform: scale3d(0, 1, 1); transform: scale3d(0, 1, 1); -webkit-transform-origin: 0% 50%; -moz-transform-origin: 0% 50%; -o-transform-origin: 0% 50%; transform-origin: 0% 50%; border-bottom-width: 1px; -ms-transform: scale3d(0, 1, 1); -ms-transform-origin: 0% 50%; }
.js .web-search .search-ipt, .web-search .search-attr { -webkit-transition-delay: .15s; -o-transition-delay: .15s; transition-delay: .15s; }
.js .web-search .search-btn { display: none !important; }
.js .search--open { visibility: visible; opacity: 1; pointer-events: auto; }
.js .search--open .btn--search-close { opacity: 1; -webkit-transition: transform .3s ease; -o-transition: transform .3s ease; transition: transform .3s ease; -webkit-animation: bounceInA 1s ease; -o-animation: bounceInA 1s ease; animation: bounceInA 1s ease; }
.js .search--open .search-ipt { opacity: 1; -webkit-transform: scale3d(1, 1, 1); -moz-transform: scale3d(1, 1, 1); -o-transform: scale3d(1, 1, 1); transform: scale3d(1, 1, 1); -ms-transform: scale3d(1, 1, 1); }
.js .search--open .search-attr { opacity: 1; -webkit-transform: translate3d(0, 0, 0); -moz-transform: translate3d(0, 0, 0); -o-transform: translate3d(0, 0, 0); transform: translate3d(0, 0, 0); -ms-transform: translate3d(0, 0, 0); }
.js .search--open { -webkit-transform: none; -o-transform: none; transform: none; -ms-transform: none; }
.js .search--open::before { opacity: 1; -webkit-transform: none; -o-transform: none; transform: none; -ms-transform: none; }
.js .main-wrap--move { -webkit-transform: translateY(180px); -o-transform: translateY(180px); transform: translateY(180px); -ms-transform: translateY(180px); }
.main-wrap--move:after { position: fixed; top: 0; left: 0; z-index: 100000; display: block; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); content: ''; }
/*-------------------------------
			search  end
			----------------------------------*/
/*-------------------------------
			scrollsidebar start
			----------------------------------*/
.scrollsidebar, .scrollsidebar a { color: #999; font-size: 12px; font-family: Arial; }
.scrollsidebar { position: absolute; top: 150px; right: 0; z-index: 30; }
.show_btn { position: absolute; top: 0; right: 0; display: block; overflow: hidden; overflow: hidden; width: 38px; height: 192px; background: url(img/custom_service/show_btn.png) no-repeat center center; cursor: pointer; }
.side_content { float: left; overflow: hidden; width: 0; height: auto; }
.side_content .side_list {position: relative;overflow: hidden;width: 156px;border: 1px solid #dadada;border-radius: 8px;background: #FFF;}
.side_content .side_list .hd img { width: 100%; border-bottom: 1px solid #dadada; }
.side_content .side_list .cont li { position: relative; height: 40px; border-bottom: 1px solid #dadada; list-style: none; line-height: 40px; }
.side_content .side_list .cont li a { position: relative; display: block; padding: 0 0 0 43px; text-decoration: none; }
.side_content .side_list .cont li a:before { position: absolute; top: 0; left: 10px; display: block; overflow: hidden; width: 25px; height: 40px; background-position: center center; background-repeat: no-repeat; content: ""; }
.side_content .side_list .cont li .email:before { background-image: url(img/custom_service/icons01.png); }
.side_content .side_list .cont li .whatsapp:before { background-image: url(img/whatsapp.png); }
.side_content .side_list .cont li .skype:before { background-image: url(img/custom_service/icons02.png); }
.side_content .side_list .cont li .inquiry:before { background-image: url(img/custom_service/icons03.png); }
.side_content .side_list .cont li .qq:before { background-image: url(img/custom_service/icons04.png); }
.ww { margin-left: -30px; }
.side_content .side_list .t-code { padding: 10px 10px 7px; border-bottom: 1px solid #dadada; }
.side_content .side_list .t-code img { width: 100%; }
.side_content .side_list .side_title { padding: 0 12px; height: 30px; line-height: 30px; }
.side_content .side_list .side_title .close_btn { position: absolute; right: 10px; bottom: 0; width: 12px; height: 30px; background: url(img/custom_service/close.png) no-repeat center center; cursor: pointer; }
.side_content .side_list .side_title .close_btn span { display: none; }
/*-------------------------------
			scrollsidebar end
			----------------------------------*/
/*inquiry*/
.inquiry-pop-bd { position: fixed; top: 50%; left: 50%; z-index: 2000; display: none; margin: -300px 0 0 -275px; width: 550px; height: 500px; background: #FFF; box-shadow: 0 0 10px rgba(255, 255, 255, .5); }
.inquiry-pop-bd:before { position: fixed; top: 0; left: 0; z-index: -1; width: 100%; height: 100%; background: rgba(0, 0, 0, .7); content: ""; }
.inquiry-pop-bd .inquiry-pop { position: absolute; top: 0; left: 0; z-index: 1; padding: 22px; width: 100%; height: 100%; background: #FFF; -webkit-animation: fadeInDownA .4s ease; -o-animation: fadeInDownA .4s ease; animation: fadeInDownA .4s ease; }
.inquiry-pop-bd .inquiry-pop .ico-close-pop { position: absolute; top: -10px; right: -10px; z-index: 1; width: 30px; height: 30px; background: url(img/custom_service/fancy_close.png) no-repeat center center; cursor: pointer; }
.inquiry-form-wrap { position: relative; width: 100.5%; height: 420px; }
.ad_prompt { position: absolute; top: 50%; left: 0; display: block; display: none; margin: -20px 0 0; width: 100%; color: #e1600d; text-align: center; font-size: 20px; line-height: 40px; -webkit-animation: twinkling 1s infinite ease-in-out; animation: twinkling 1s infinite ease-in-out; }
 @media screen and (max-width: 550px) {
.inquiry-pop-bd { top: 10px; right: 10px; bottom: 10px; left: 10px; margin: auto; width: auto; }
.inquiry-pop-bd .inquiry-pop { padding: 10px; }
.scrollsidebar { display: none; }
}
/*-------------------------------
			language  start
			----------------------------------*/
.prisna-wp-translate-seo { display: none; font-family: Arial, Helvetica, sans-serif; }
.mobile-head-language .prisna-wp-translate-seo, .change-language .prisna-wp-translate-seo { display: block; }
.change-language .change-language-title, .change-language .change-language-title a { font-size: 10pt; }
.change-language .change-language-title a:after { display: inline-block; padding-left: 2px; content: "\f0d7"; font-size: 12px; font-family: 'fontawesome'; }
.change-language:hover .change-language-title a:after { -webkit-transform: rotate(-180deg); -moz-transform: rotate(-360deg); -o-transform: rotate(-180deg); transform: rotate(-180deg); -ms-transform: rotate(-180deg); }
.change-language .change-language-cont { position: absolute; top: 36px; right: 0; z-index: 999; visibility: hidden; overflow-y: scroll; padding: 20px 10px 20px 20px; width: 360px; max-height: 80vh; border: 1px solid #eee; border-radius: 5px; background: #FFF; opacity: 0; -webkit-transition: all 0.3s ease-in-out; -moz-transition: all 0.3s ease-in-out; -o-transition: all 0.3s ease-in-out; transition: all 0.3s ease-in-out; transform: scale3d(0.9, 0.9, 1); -ms-transition: all 0.3s ease-in-out; }
.change-language:hover .change-language-cont { visibility: visible; opacity: 1; transform: scale3d(1, 1, 1); }
.change-language .change-language-cont li { display: inline-block; overflow: hidden; margin: 0 0 10px; padding: 0 10px 0 0; width: 30%; vertical-align: top; font-size: 10pt; }
.change-language .change-language-cont li a:hover { color: #e1600d; }
.change-language .change-language-cont .lang-item-hide { display: none !important; }
.change-language .change-language-cont .lang-more { position: relative; display: block; text-align: center; line-height: 30px; cursor: pointer; }
.change-language .change-language-cont .more-active { font-size: 20px; }
.language-flag span { position: relative; display: inline-block; overflow: hidden; padding: 0 0 0 33px; vertical-align: top; white-space: nowrap; *display: inline;
 *zoom: 1;
}
 .change-language ::-webkit-scrollbar {
 width: 5px;
}
 .change-language ::-webkit-scrollbar-track {
 background-color: #fff;
}
 .change-language ::-webkit-scrollbar-thumb {
 border-radius: 3px;
 background-color: rgba(0,0,0,.3);
}
 .change-language .language-cont:hover::-webkit-scrollbar-thumb {
 display: block;
}
.language-flag .country-flag { position: absolute; top: 50%; left: 0; display: inline-block; margin: -8px 5px 0; width: 22px; height: 16px; background-image: url(img/all-m.gif); background-repeat: no-repeat; vertical-align: top; *display: inline;
 *zoom: 1;
}
li.language-flag a { position: relative; }
.language-flag-af .country-flag { background-position: 0 0 !important; }
.language-flag-sq .country-flag { background-position: 0 -16px !important; }
.language-flag-ar .country-flag { background-position: 0 -32px !important; }
.language-flag-hy .country-flag { background-position: 0 -48px !important; }
.language-flag-az .country-flag { background-position: 0 -64px !important; }
.language-flag-eu .country-flag { background-position: 0 -80px !important; }
.language-flag-be .country-flag { background-position: 0 -96px !important; }
.language-flag-bg .country-flag { background-position: 0 -112px !important; }
.language-flag-ca .country-flag { background-position: 0 -128px !important; }
.language-flag-zh .country-flag { background-position: 0 -144px !important; }
.language-flag-zh-TW .country-flag { background-position: 0 -160px !important; }
.language-flag-hr .country-flag { background-position: 0 -176px !important; }
.language-flag-cs .country-flag { background-position: 0 -192px !important; }
.language-flag-da .country-flag { background-position: 0 -208px !important; }
.language-flag-nl .country-flag { background-position: 0 -240px !important; }
.language-flag-en .country-flag { background-position: 0 -256px !important; }
.language-flag-et .country-flag { background-position: 0 -272px !important; }
.language-flag-tl .country-flag { background-position: 0 -288px !important; }
.language-flag-fi .country-flag { background-position: 0 -304px !important; }
.language-flag-fr .country-flag { background-position: 0 -320px !important; }
.language-flag-gl .country-flag { background-position: 0 -336px !important; }
.language-flag-ka .country-flag { background-position: 0 -352px !important; }
.language-flag-de .country-flag { background-position: 0 -368px !important; }
.language-flag-el .country-flag { background-position: 0 -384px !important; }
.language-flag-ht .country-flag { background-position: 0 -400px !important; }
.language-flag-iw .country-flag { background-position: 0 -416px !important; }
.language-flag-hi .country-flag, .language-flag-te .country-flag, .language-flag-kn .country-flag, .language-flag-ta .country-flag, .language-flag-gu .country-flag { background-position: 0 -432px !important; }
.language-flag-hu .country-flag { background-position: 0 -448px !important; }
.language-flag-is .country-flag { background-position: 0 -464px !important; }
.language-flag-id .country-flag { background-position: 0 -480px !important; }
.language-flag-ga .country-flag { background-position: 0 -496px !important; }
.language-flag-it .country-flag { background-position: 0 -512px !important; }
.language-flag-ja .country-flag { background-position: 0 -528px !important; }
.language-flag-ko .country-flag { background-position: 0 -544px !important; }
.language-flag-la .country-flag { background-position: 0 -560px !important; }
.language-flag-lv .country-flag { background-position: 0 -576px !important; }
.language-flag-lt .country-flag { background-position: 0 -592px !important; }
.language-flag-mk .country-flag { background-position: 0 -608px !important; }
.language-flag-ms .country-flag { background-position: 0 -624px !important; }
.language-flag-mt .country-flag { background-position: 0 -640px !important; }
.language-flag-no .country-flag { background-position: 0 -656px !important; }
.language-flag-fa .country-flag { background-position: 0 -672px !important; }
.language-flag-pl .country-flag { background-position: 0 -688px !important; }
.language-flag-pt .country-flag { background-position: 0 -704px !important; }
.language-flag-ro .country-flag { background-position: 0 -720px !important; }
.language-flag-ru .country-flag { background-position: 0 -736px !important; }
.language-flag-sr .country-flag { background-position: 0 -752px !important; }
.language-flag-sk .country-flag { background-position: 0 -768px !important; }
.language-flag-sl .country-flag { background-position: 0 -784px !important; }
.language-flag-es .country-flag { background-position: 0 -800px !important; }
.language-flag-sw .country-flag { background-position: 0 -816px !important; }
.language-flag-sv .country-flag { background-position: 0 -832px !important; }
.language-flag-th .country-flag { background-position: 0 -848px !important; }
.language-flag-tr .country-flag { background-position: 0 -864px !important; }
.language-flag-uk .country-flag { background-position: 0 -880px !important; }
.language-flag-ur .country-flag { background-position: 0 -896px !important; }
.language-flag-vi .country-flag { background-position: 0 -912px !important; }
.language-flag-cy .country-flag { background-position: 0 -928px !important; }
.language-flag-yi .country-flag { background-position: 0 -944px !important; }
.language-flag-bn .country-flag { background-position: 0 -960px !important; }
.language-flag-eo .country-flag { background-position: 0 -976px !important; }
.language-flag.language-flag-custom-afghanistan .country-flag { background-position: 0 -992px !important; }
.language-flag.language-flag-custom-albania .country-flag { background-position: 0 -1008px !important; }
.language-flag.language-flag-custom-algeria .country-flag { background-position: 0 -1024px !important; }
.language-flag.language-flag-custom-argentina .country-flag { background-position: 0 -1040px !important; }
.language-flag.language-flag-custom-australia .country-flag { background-position: 0 -1056px !important; }
.language-flag.language-flag-custom-austria .country-flag { background-position: 0 -1072px !important; }
.language-flag.language-flag-custom-belgium .country-flag { background-position: 0 -1088px !important; }
.language-flag.language-flag-custom-brazil .country-flag { background-position: 0 -1104px !important; }
.language-flag.language-flag-custom-canada .country-flag { background-position: 0 -1120px !important; }
.language-flag.language-flag-custom-chile .country-flag { background-position: 0 -1136px !important; }
.language-flag.language-flag-custom-cote-d-ivoire .country-flag { background-position: 0 -1152px !important; }
.language-flag.language-flag-custom-ecuador .country-flag { background-position: 0 -1168px !important; }
.language-flag.language-flag-custom-egypt .country-flag { background-position: 0 -1184px !important; }
.language-flag.language-flag-custom-england .country-flag { background-position: 0 -1200px !important; }
.language-flag.language-flag-custom-luxembourg .country-flag { background-position: 0 -1216px !important; }
.language-flag.language-flag-custom-malaysia .country-flag { background-position: 0 -1232px !important; }
.language-flag.language-flag-custom-mexico .country-flag { background-position: 0 -1248px !important; }
.language-flag.language-flag-custom-new-zealand .country-flag { background-position: 0 -1264px !important; }
.language-flag.language-flag-custom-oman .country-flag { background-position: 0 -1280px !important; }
.language-flag.language-flag-custom-qatar .country-flag { background-position: 0 -1296px !important; }
.language-flag.language-flag-custom-saudi-arabia .country-flag { background-position: 0 -1312px !important; }
.language-flag.language-flag-custom-singapore .country-flag { background-position: 0 -1328px !important; }
.language-flag.language-flag-custom-south-africa .country-flag { background-position: 0 0 !important; }
.language-flag.language-flag-custom-switzerland .country-flag { background-position: 0 -1344px !important; }
.language-flag.language-flag-custom-syria .country-flag { background-position: 0 -1360px !important; }
.language-flag.language-flag-custom-united-arab-emirates .country-flag { background-position: 0 -1376px !important; }
.language-flag.language-flag-custom-united-states-of-america .country-flag { background-position: 0 -1392px !important; }
.language-flag-hy .country-flag { background-position: 0 -1408px; }
.language-flag-bs .country-flag { background-position: 0 -1424px; }
.language-flag-ceb .country-flag { background-position: 0 -1440px; }
.language-flag-ny .country-flag { background-position: 0 -1456px; }
.language-flag-ha .country-flag { background-position: 0 -1472px; }
.language-flag-hmn .country-flag { background-position: 0 -1488px; }
.language-flag-ig .country-flag { background-position: 0 -1504px; }
.language-flag-jw .country-flag { background-position: 0 -1520px; }
.language-flag-kk .country-flag { background-position: 0 -1536px; }
.language-flag-km .country-flag { background-position: 0 -1552px; }
.language-flag-lo .country-flag { background-position: 0 -1568px; }
.language-flag-la .country-flag { background-position: 0 -1584px; }
.language-flag-mg .country-flag { background-position: 0 -1600px; }
.language-flag-ml .country-flag { background-position: 0 -1616px; }
.language-flag-mi .country-flag { background-position: 0 -1632px; }
.language-flag-mr .country-flag { background-position: 0 -1648px; }
.language-flag-mn .country-flag { background-position: 0 -1664px; }
.language-flag-my .country-flag { background-position: 0 -1680px; }
.language-flag-ne .country-flag { background-position: 0 -1696px; }
.language-flag-pa .country-flag { background-position: 0 -1712px; }
.language-flag-st .country-flag { background-position: 0 -1728px; }
.language-flag-si .country-flag { background-position: 0 -1744px; }
.language-flag-so .country-flag { background-position: 0 -1760px; }
.language-flag-su .country-flag { background-position: 0 -1776px; }
.language-flag-tg .country-flag { background-position: 0 -1792px; }
.language-flag-uz .country-flag { background-position: 0 -1808px; }
.language-flag-yo .country-flag { background-position: 0 -1824px; }
.language-flag-zu .country-flag { background-position: 0 -1840px; }
/*-------------------------------
			language  end
			----------------------------------*/
/*-------------------------------
			animation  start
			----------------------------------*/
.animated { -webkit-animation-duration: 1s; animation-duration: 1s; -webkit-animation-fill-mode: both; animation-fill-mode: both; }
.animated.infinite { -webkit-animation-iteration-count: infinite; animation-iteration-count: infinite; }
.animated.hinge { -webkit-animation-duration: 2s; animation-duration: 2s; }
 @-webkit-keyframes fadeInLeftA { 0% {
 opacity: 0;
 -webkit-transform: translate3d(-30%, 0, 0);
 transform: translate3d(-30%, 0, 0);
}
 100% {
 opacity: 1;
 -webkit-transform: none;
 transform: none;
}
}
 @keyframes fadeInLeftA { 0% {
 opacity: 0;
 -webkit-transform: translate3d(-30%, 0, 0);
 transform: translate3d(-30%, 0, 0);
 -ms-transform: translate3d(-30%, 0, 0);
}
 100% {
 opacity: 1;
 -webkit-transform: none;
 transform: none;
 -ms-transform: none;
}
}
.fadeInLeftA { -webkit-animation-name: fadeInLeftA; animation-name: fadeInLeftA; }
 @-webkit-keyframes fadeInUpA { 0% {
 opacity: 0;
 -webkit-transform: translate3d(0, 30%, 0);
 transform: translate3d(0, 30%, 0);
}
 100% {
 opacity: 1;
 -webkit-transform: none;
 transform: none;
}
}
 @keyframes fadeInUpA { 0% {
 opacity: 0;
 -webkit-transform: translate3d(0, 30%, 0);
 transform: translate3d(0, 30%, 0);
 -ms-transform: translate3d(0, 30%, 0);
}
 100% {
 opacity: 1;
 -webkit-transform: none;
 transform: none;
 -ms-transform: none;
}
}
.fadeInUpA { -webkit-animation-name: fadeInUpA; animation-name: fadeInUpA; }
 @-webkit-keyframes fadeInRightA { 0% {
 opacity: 0;
 -webkit-transform: translate3d(30%, 0, 0);
 transform: translate3d(30%, 0, 0);
}
 100% {
 opacity: 1;
 -webkit-transform: none;
 transform: none;
}
}
 @keyframes fadeInRightA { 0% {
 opacity: 0;
 -webkit-transform: translate3d(30%, 0, 0);
 transform: translate3d(30%, 0, 0);
 -ms-transform: translate3d(30%, 0, 0);
}
 100% {
 opacity: 1;
 -webkit-transform: none;
 transform: none;
 -ms-transform: none;
}
}
.fadeInRightA { -webkit-animation-name: fadeInRightA; animation-name: fadeInRightA; }
 @-webkit-keyframes twinkling { 0% {
 opacity: 0;
}
 100% {
 opacity: 1;
}
}
 @-webkit-keyframes OrangePulse { from {
 background-color: rgba(210,174,109,.2);
 -webkit-box-shadow: 0 0 10px rgba(210,174,109,1);
}
 50% {
 background-color: rgba(210,174,109,1);
 -webkit-box-shadow: 0 0 10px rgba(210,174,109,1);
}
to { background-color: rgba(210,174,109,.2); -webkit-box-shadow: 0 0 10px rgba(210,174,109,1); }
}
 @-webkit-keyframes swing { 20% {
 -webkit-transform: rotate(40deg);
 transform: rotate(40deg);
}
 40% {
 -webkit-transform: rotate(-20deg);
 transform: rotate(-20deg);
}
 60% {
 -webkit-transform: rotate(10deg);
 transform: rotate(10deg);
}
 80% {
 -webkit-transform: rotate(-10deg);
 transform: rotate(-10deg);
}
 100% {
 -webkit-transform: rotate(0deg);
 transform: rotate(0deg);
}
}
 @keyframes swing { 20% {
 -webkit-transform: rotate(40deg);
 transform: rotate(40deg);
 -ms-transform: rotate(40deg);
}
 40% {
 -webkit-transform: rotate(-20deg);
 transform: rotate(-20deg);
 -ms-transform: rotate(-20deg);
}
 60% {
 -webkit-transform: rotate(10deg);
 transform: rotate(10deg);
 -ms-transform: rotate(10deg);
}
 80% {
 -webkit-transform: rotate(-10deg);
 transform: rotate(-10deg);
 -ms-transform: rotate(-10deg);
}
 100% {
 -webkit-transform: rotate(0deg);
 transform: rotate(0deg);
 -ms-transform: rotate(0deg);
}
}
.swing { -webkit-transform-origin: top center; transform-origin: top center; -ms-transform-origin: top center; -webkit-animation-name: swing; animation-name: swing; }
 @-webkit-keyframes jump { 0% {
 -webkit-transform: translateY(0);
}
 25% {
 -webkit-transform: translateY(-20%);
}
 50% {
 -webkit-transform: translateY(0);
}
 75% {
 -webkit-transform: translateY(-10%);
}
 100% {
 -webkit-transform: translateY(0);
}
}
 @keyframes jump { 0% {
 transform: translateY(0);
}
 25% {
 transform: translateY(-20%);
}
 50% {
 transform: translateY(0);
}
 75% {
 transform: translateY(-10%);
}
 100% {
 transform: translateY(0);
}
}
 @-webkit-keyframes bounceInA { 0%, 100%, 20%, 40%, 60%, 80% {
 -webkit-transition-timing-function: cubic-bezier(0.215, .61, .355, 1);
 transition-timing-function: cubic-bezier(0.215, .61, .355, 1);
}
 0% {
 opacity: 0;
 -webkit-transform: scale3d(.3, .3, .3);
 transform: scale3d(.3, .3, .3);
}
 20% {
 -webkit-transform: scale3d(1.1, 1.1, 1.1);
 transform: scale3d(1.1, 1.1, 1.1);
}
 40% {
 -webkit-transform: scale3d(.9, .9, .9);
 transform: scale3d(.9, .9, .9);
}
 60% {
 opacity: 1;
 -webkit-transform: scale3d(1.03, 1.03, 1.03);
 transform: scale3d(1.03, 1.03, 1.03);
}
 80% {
 -webkit-transform: scale3d(.97, .97, .97);
 transform: scale3d(.97, .97, .97);
}
 100% {
 opacity: 1;
 -webkit-transform: scale3d(1, 1, 1);
 transform: scale3d(1, 1, 1);
}
}
 @keyframes bounceInA { 0%, 100%, 20%, 40%, 60%, 80% {
 -webkit-transition-timing-function: cubic-bezier(0.215, .61, .355, 1);
 transition-timing-function: cubic-bezier(0.215, .61, .355, 1);
}
 0% {
 opacity: 0;
 -webkit-transform: scale3d(.3, .3, .3);
 transform: scale3d(.3, .3, .3);
 -ms-transform: scale3d(.3, .3, .3);
}
 20% {
 -webkit-transform: scale3d(1.1, 1.1, 1.1);
 transform: scale3d(1.1, 1.1, 1.1);
 -ms-transform: scale3d(1.1, 1.1, 1.1);
}
 40% {
 -webkit-transform: scale3d(.9, .9, .9);
 transform: scale3d(.9, .9, .9);
 -ms-transform: scale3d(.9, .9, .9);
}
 60% {
 opacity: 1;
 -webkit-transform: scale3d(1.03, 1.03, 1.03);
 transform: scale3d(1.03, 1.03, 1.03);
 -ms-transform: scale3d(1.03, 1.03, 1.03);
}
 80% {
 -webkit-transform: scale3d(.97, .97, .97);
 transform: scale3d(.97, .97, .97);
 -ms-transform: scale3d(.97, .97, .97);
}
 100% {
 opacity: 1;
 -webkit-transform: scale3d(1, 1, 1);
 transform: scale3d(1, 1, 1);
 -ms-transform: scale3d(1, 1, 1);
}
}
.bounceInA { -webkit-animation-name: bounceInA; animation-name: bounceInA; -webkit-animation-duration: .75s; animation-duration: .75s; }
 @-webkit-keyframes fadeInDownA { 0% {
 opacity: 0;
 -webkit-transform: translate3d(0, -20%, 0);
 transform: translate3d(0, -20%, 0);
}
 100% {
 opacity: 1;
 -webkit-transform: none;
 transform: none;
}
}
 @keyframes fadeInDownA { 0% {
 opacity: 0;
 -webkit-transform: translate3d(0, -20%, 0);
 transform: translate3d(0, -20%, 0);
 -ms-transform: translate3d(0, -20%, 0);
}
 100% {
 opacity: 1;
 -webkit-transform: none;
 transform: none;
 -ms-transform: none;
}
}
.fadeInDownA { -webkit-animation-name: fadeInDownA; animation-name: fadeInDownA; }
 @keyframes fadeInA { 0% {
 opacity: 0;
}
 100% {
 opacity: 1;
}
}
.fadeInA { -webkit-animation-name: fadeInA; animation-name: fadeInA; }
 @-webkit-keyframes flipInY { 0% {
 opacity: 0;
 -webkit-transform: perspective(400px) rotateY(90deg);
 transform: perspective(400px) rotateY(90deg);
}
 0%, 40% {
 -webkit-animation-timing-function: ease-in;
 animation-timing-function: ease-in;
}
 40% {
 -webkit-transform: perspective(400px) rotateY(-20deg);
 transform: perspective(400px) rotateY(-20deg);
}
 60% {
 opacity: 1;
 -webkit-transform: perspective(400px) rotateY(10deg);
 transform: perspective(400px) rotateY(10deg);
}
 80% {
 -webkit-transform: perspective(400px) rotateY(-5deg);
 transform: perspective(400px) rotateY(-5deg);
}
to { -webkit-transform: perspective(400px); transform: perspective(400px); }
}
 @keyframes flipInY { 0% {
 opacity: 0;
 -webkit-transform: perspective(400px) rotateY(90deg);
 transform: perspective(400px) rotateY(90deg);
}
 0%, 40% {
 -webkit-animation-timing-function: ease-in;
 animation-timing-function: ease-in;
}
 40% {
 -webkit-transform: perspective(400px) rotateY(-20deg);
 transform: perspective(400px) rotateY(-20deg);
}
 60% {
 opacity: 1;
 -webkit-transform: perspective(400px) rotateY(10deg);
 transform: perspective(400px) rotateY(10deg);
}
 80% {
 -webkit-transform: perspective(400px) rotateY(-5deg);
 transform: perspective(400px) rotateY(-5deg);
}
to { -webkit-transform: perspective(400px); transform: perspective(400px); }
}
.flipInY { -webkit-backface-visibility: visible!important; backface-visibility: visible!important; -webkit-animation-name: flipInY; animation-name: flipInY; }
/* animate.css */
@-webkit-keyframes fadeInUp { 0% {
 opacity: 0;
 -webkit-transform: translate3d(0, 100%, 0);
 transform: translate3d(0, 100%, 0);
}
to { opacity: 1; -webkit-transform: none; transform: none; }
}
 @keyframes fadeInUp { 0% {
 opacity: 0;
 -webkit-transform: translate3d(0, 100%, 0);
 transform: translate3d(0, 100%, 0);
}
to { opacity: 1; -webkit-transform: none; transform: none; }
}
.fadeInUp { -webkit-animation-name: fadeInUp; animation-name: fadeInUp; }
 @-webkit-keyframes fadeInUpBig { 0% {
 opacity: 0;
 -webkit-transform: translate3d(0, 2000px, 0);
 transform: translate3d(0, 2000px, 0);
}
to { opacity: 1; -webkit-transform: none; transform: none; }
}
 @-webkit-keyframes bounceInUp { 0%, 60%, 75%, 90%, to {
 -webkit-animation-timing-function: cubic-bezier(.215, .61, .355, 1);
 animation-timing-function: cubic-bezier(.215, .61, .355, 1);
}
 0% {
 opacity: 0;
 -webkit-transform: translate3d(0, 3000px, 0);
 transform: translate3d(0, 3000px, 0);
}
 60% {
 opacity: 1;
 -webkit-transform: translate3d(0, -20px, 0);
 transform: translate3d(0, -20px, 0);
}
 75% {
 -webkit-transform: translate3d(0, 10px, 0);
 transform: translate3d(0, 10px, 0);
}
 90% {
 -webkit-transform: translate3d(0, -5px, 0);
 transform: translate3d(0, -5px, 0);
}
to { -webkit-transform: translateZ(0); transform: translateZ(0); }
}
 @keyframes bounceInUp { 0%, 60%, 75%, 90%, to {
 -webkit-animation-timing-function: cubic-bezier(.215, .61, .355, 1);
 animation-timing-function: cubic-bezier(.215, .61, .355, 1);
}
 0% {
 opacity: 0;
 -webkit-transform: translate3d(0, 3000px, 0);
 transform: translate3d(0, 3000px, 0);
}
 60% {
 opacity: 1;
 -webkit-transform: translate3d(0, -20px, 0);
 transform: translate3d(0, -20px, 0);
}
 75% {
 -webkit-transform: translate3d(0, 10px, 0);
 transform: translate3d(0, 10px, 0);
}
 90% {
 -webkit-transform: translate3d(0, -5px, 0);
 transform: translate3d(0, -5px, 0);
}
to { -webkit-transform: translateZ(0); transform: translateZ(0); }
}
.bounceInUp { -webkit-animation-name: bounceInUp; animation-name: bounceInUp; }
 @-webkit-keyframes zoomInA { 0% {
 opacity: 0;
 -webkit-transform: scale3d(.6, .6, .6);
 transform: scale3d(.6, .6, .6);
}
 50% {
 opacity: 1;
}
}
 @keyframes zoomInA { 0% {
 opacity: 0;
 -webkit-transform: scale3d(.6, .6, .6);
 transform: scale3d(.6, .6, .6);
}
 50% {
 opacity: 1;
}
}
.zoomInA { -webkit-animation-name: zoomInA; animation-name: zoomInA; }
/*-------------------------------
			animation  end
			----------------------------------*/
.blue-bg {
  position: relative;
  background-color: #ff66000d;
  color: #ff6600;
  height: 100%;
  margin: 0 -400px;
  padding: 0 400px;
}
.pb-3{font-size: 0.35rem;padding-bottom: 25px;padding-top:35px;}
.row,.containers,.col-2{position:relative;}
.row:last-child .col-2.full::after{height:50% !important;top:0;}
.col-s::after{height:50% !important;top:50%;}
.justify-content-end{justify-content: flex-end !important;}
.row{display:flex;flex-wrap:wrap;margin-right: -15px;margin-left: -15px;}
.text-right{text-align:right;}
.col-6 h5{font-size:0.22rem;}
.container-fluid{padding-bottom:35px;}
.row_6{display:none;}
.col_ss.full::after{height:50% !important;top:0;}
.circle {
  font-weight: bold;
  padding: 15px 20px;
  border-radius: 50%;
  background-color: #ff6600;
  color: #ffffff;
  max-height: 50px;
  z-index: 2;
}
.col-2{
    flex: 0 0 16.666667%;
    max-width: 16.666667%;
}
.col-6{
        flex: 0 0 65%;
        max-width: 65%;
}
.col-8{
    flex: 0 0 66.666667%;
    max-width: 66.666667%;
}
.how-it-works.row {
  display: flex;
}
.how-it-works.row .col-2 {
  display: inline-flex;
  align-self: stretch;
  align-items: center;
  justify-content: center;
}
.how-it-works.row .col-2::after {
  content: "";
  position: absolute;
  border-left: 3px solid #ff6600;
  z-index: 1;
}
.how-it-works.row .col-2.bottom::after {
  height: 100%;
  left: 50%;
  /* top: 50%; */
}
.how-it-works.row .col-2.full::after {
  height: 100%;
  left: calc(50% - 3px);
}
.how-it-works.row .col-2.top::after {
  height: 100%;
  left: 50%;
  top: 0;
}

.timeline div {
  padding: 0;
  height: 40px;
}
.timeline hr {
  border-top: 3px solid #ff6600;
  margin: 0;
  top: 17px;
  position: relative;
}
.timeline .col-2 {
  display: flex;
  overflow: hidden;
}
.timeline .corner {
  border: 3px solid #ff6600;
  width: 100%;
  position: relative;
  border-radius: 15px;
}
.timeline .top-right {
  left: 50%;
  top: -50%;
}
.timeline .left-bottom {
  left: -50%;
  top: calc(50% - 3px);
}
.timeline .top-left {
  left: -50%;
  top: -50%;
}
.timeline .right-bottom {
  left: 50%;
  top: calc(50% - 3px);
}
.logo img{
    max-width: 100px;
}





content_box 后面可跟上wow fadeInUpA ，wow fadeInLeftA。

只需要给标题设置H2

/*----------------Custom style strat------------------*/
.main table th, .main table td , .main table td p, .main table th p, table td p, table th p , .entry p{
    font-size: 16px;
    font-family: "POPPINS-REGULAR.TTF";
}
.table_wrap{
margin:10px 0;
}
.entry tr:nth-child(2n+1) {
    background-color: #f7fcff;
}
.entry{
color: #333;
}

.content_box , .content_box>h2{
    position: relative;
}
.content_box .companyname , .content_box:before{
    position: absolute;
}
.content_box , .content_box h2 > span{
    overflow: hidden;
}
.content_box h2{
    width: 35%;
    color: white;
    line-height: 35px;
    z-index: 2;
}
.content_box h2 > span{
    text-transform: capitalize;
    padding: 2px 12px 2px 20px; 
    text-overflow: ellipsis;
    white-space: nowrap;
}
.content_box .companyname{
    right: 0;
    bottom: 0;
    width: 100%;
    height: 33px;
    z-index: 0;
}
.content_box:before{
    content: '';
    left: 5px;
    bottom: 0;
    width: 25%;
    height: 54%;
    z-index: 1;
}
.content_box h2 after , .content_box:before , .content_box h2 span{
    display:block;
}
.content_box h2:after , .content_box:before , .content_box h2 > span{
    transform: skewX(-25deg) !important;
    -ms-transform: skewX(-25deg) !important;
    -moz-transform: skewX(-25deg) !important;
    -webkit-transform: skewX(-25deg) !important;
    -o-transform: skewX(-25deg) !important;
    font-weight: 500;
}
.content_box h2{
    transform: skewX(25deg) !important;
    -ms-transform: skewX(25deg) !important;
    -moz-transform: skewX(25deg) !important;
    -webkit-transform: skewX(25deg) !important;
    -o-transform: skewX(25deg) !important;
    transform-origin:bottom;
}
 .content_box h2{
    background: -webkit-linear-gradient(top, #8cceff, #0e4e7d);
    background: -moz-linear-gradient(top, #666666, #000000);
    background: -ms-linear-gradient(top, #666666, #000000);
    background: -o-linear-gradient(top, #666666, #000000);
    background: linear-gradient(top, #666666, #000000);
    background: #666\9;
}
.content_box:before{
    background-color: #5192c2;
}
.content_box .companyname{
    background-color: #b6d2e7;
}
.entry table {
    margin: 10px 0;
}

@media only screen and (max-width:500px){
    .content_box:after{
        content: '';
    }
    .content_box h2 , .content_box:before{
        width: 45%;
    }
    .content_box h2 > span {
        font-size: 15px !important;
    }
   .content_box .companyname{
        color: transparent;
    }
}
/*------------------Custom style end----------------*/
.fl-accordion-item-active .fl-accordion-button{
    background: #5192c2;
    /* color: #fff; */
}
.fl-accordion-button-label{
    font-size: 16px;
    color: #000;
}
.pro_table td{
    width: 25%!important;
}
.product_photo img{
    box-shadow: 0px 0px 4px 4px #00000026;
}
.product_shows p{
    color: #fff;
}
.product_shows hr{
    border-color: #fff;
    margin:5px 0;
}
.product_show hr{
    border-color: #fff;
    margin:5px 0;
}
.product_show p span {
    /* font-size: 18px; */
    text-shadow: 1px 1px 1px #000;
    font-weight: 900;
}
.fl-accordion-item-active .fl-accordion-button span{
    color: #fff;
}
.product_show .fl-rich-text p{
    padding: 0!important;
}
 .product_shows .fl-rich-text p {
    padding: 0!important;
}
/*download*/

.product-list, .case-list, .certificate-list, .video-list, .download-list, .faq-list{

	position: relative;

    overflow: hidden;

    margin: 40px 0 0;

}

.download-list ul li{

	position: relative;

    display: block;

    overflow: hidden;

    line-height: 30px;

    padding: 30px 0 10px;

    border-bottom: 1px solid #E9E9E9;

}

.download-list ul li .download-cont{

	position: relative;

    display: inline-block;

    vertical-align: top;

    float: left;

}

.download-list ul li{

	position: relative;

    display: block;

    overflow: hidden;

    line-height: 30px;

    padding: 30px 0 10px;

    border-bottom: 1px solid #E9E9E9;

}

.download-list ul li .download-cont .download-pic{

	position: relative;

    display: inline-block;

    vertical-align: top;

    width: 25px;

    overflow: hidden;

}

.download-list ul li .download-cont .download-pic img{

	max-width: 25px;

    max-height: 25px;

}

.download-list ul li .download-cont .download-detail{

	position: relative;

    display: inline-block;

    vertical-align: top;

    font-size: 16px;

    margin: 0 15px;

    text-shadow: 0 0 1px rgba(0,0,0,0.3);

}

.download-list ul li .download-button{

	position: relative;

    display: inline-block;

    vertical-align: top;

    float: right;

    height: 30px;

    line-height: 30px;

    font-size: 14px;

    border-width: 1px;

    border-style: solid;

    border-left-color: #C1C1C1;

    border-right-color: #C1C1C1;

    border-top-color: #C9C9C9;

    border-bottom-color: #A5A5A5;

    border-radius: 5px;

    filter: progid:DXImageTransform.Microsoft.Gradient(startColorStr='#F1F1F1',endColorStr='#DADADA',gradientType='0');

    background: -moz-linear-gradient(top, #F1F1F1, #DADADA);

    background: -o-linear-gradient(top,#F1F1F1, #DADADA);

    background: -webkit-gradient(linear, 0% 0%, 0% 100%, from(#F1F1F1), to(#DADADA));

}

.download-list ul li .download-button a {
    position: relative;
    display: block;
    padding: 0 12px 0 40px;
    color: #0A0A0A;
    background-image: url(/style/global/img/download.png);
    background-repeat: no-repeat;
    background-position: 10px 3px;
}
.about_txt span {
    text-shadow: 1px 1px 2px #000;
    font-size: 24px;
}
.single_product {
    float: none;
}
.copyright a{
    display: inline-block!important;
}
.foot_contact a{
    display: inline-block;
}